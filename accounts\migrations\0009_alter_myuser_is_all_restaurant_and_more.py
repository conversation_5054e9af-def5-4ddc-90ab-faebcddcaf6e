# Generated by Django 4.2.11 on 2024-06-26 15:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_auto_20221107_1103'),
    ]

    operations = [
        migrations.AlterField(
            model_name='myuser',
            name='is_all_restaurant',
            field=models.BooleanField(blank=True, default=False, help_text='Authorize whether to manage all restaurants.', verbose_name='Authorize all restaurants'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='is_alldept',
            field=models.BooleanField(blank=True, default=False, help_text='Authorize whether to manage all departments.', verbose_name='Authorize all departments'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='is_allzone',
            field=models.BooleanField(blank=True, default=False, help_text='Authorize whether to manage all zones.', verbose_name='Authorize all zones'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='is_public',
            field=models.<PERSON>oleanField(blank=True, default=False, editable=False, help_text='Open super administrator name, phone number, EMAIL, convenient to contact.', verbose_name='Is it public?'),
        ),
    ]
