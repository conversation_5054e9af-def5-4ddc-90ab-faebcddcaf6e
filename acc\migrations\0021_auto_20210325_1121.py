# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-03-25 11:21
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('acc', '0020_auto_20210218_1017'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='empzone',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'verbose_name': 'EmpZone', 'verbose_name_plural': 'EmpZone'},
        ),
        migrations.AlterModelOptions(
            name='firstopen',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'permissions': (('addemps_firstopen', 'Add emps'), ('delallemps_firstopen', 'Delete allemps')), 'verbose_name': 'The first person is always open', 'verbose_name_plural': 'The first person is always open'},
        ),
        migrations.AlterModelOptions(
            name='level',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'permissions': (('addemps_level', 'Add emps'), ('delallemps_level', 'Delete allemps')), 'verbose_name': 'Access Control Group', 'verbose_name_plural': 'AccLevel'},
        ),
        migrations.AlterModelOptions(
            name='records',
            options={'default_permissions': (), 'permissions': (('monitor_oplog', 'Real Monitor'), ('incoming_and_control', 'Incoming And Control'), ('acc_records', 'Acc Records'), ('acc_reports', 'Acc Reports'), ('access_control_record_export', 'Access Control Record Export'), ('access_reports_export', 'Access Reports Export')), 'verbose_name': 'Access Control Record', 'verbose_name_plural': 'Access Control Record'},
        ),
        migrations.AlterModelOptions(
            name='zone',
            options={'default_permissions': ('browse', 'add', 'change', 'delete', 'export'), 'verbose_name': 'area'},
        ),
    ]
