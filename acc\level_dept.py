#!/usr/bin/python
# -*- coding: utf-8 -*-

from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from mysite.acc.models import *
from django.core.paginator import Paginator

from mysite.iclock.iutils import get_emp_queryset
from mysite.iclock.models import *
from mysite.core.zkcmdproc import *


@login_required
def index(request):
    """
    获取按部门设置门禁权限页面
    """
    if request.method == 'GET':
        tmp_file = 'acc/level_dept.html'
        tmp_file = request.GET.get('t', tmp_file)
        cc = {}
        settings.PAGE_LIMIT = GetParamValue('opt_users_page_limit', '30', str(request.user.id))
        limit = int(request.GET.get('l', settings.PAGE_LIMIT))
        colmodels = [
                {'name': 'DeptID', 'hidden': True},
                {'name': 'DeptNumber', 'width': 100, 'label': u"%s" % (department._meta.get_field('DeptNumber').verbose_name)},
                {'name': 'DeptName', 'width': 200, 'label': u"%s" % (_('department name'))},
                {'name': 'empCount', 'sortable': False, 'width': 80, 'label': u"%s" % (_('EmpCount'))},
                {'name': 'level_detail', 'sortable': False, 'width': 169, 'label': u"%s" % (_(u'operating'))},
            ]
        colmodels_level = [
                {'name': 'id', 'hidden': True},
                {'name': 'name', 'index': '', 'width': 140, 'label': u"%s" % (_(u'Privilege Group Name'))},
                {'name': 'tz', 'width': 217, 'label': u"%s" % (_(u'period'))}
            ]

        cc['limit'] = limit
        cc['colModel'] = dumps1(colmodels)
        cc['colModel_level'] = dumps1(colmodels_level)
        request.model = level
        return render(request, tmp_file, cc)
    else:
        return getJSResponse({"ret": 0, "message": ""})


@login_required
def get_level_dept(request):
    """
    查询部门权限组
    """
    dept_id = request.GET.get('id')
    limit = int(request.POST.get('rows', 0))
    offset = int(request.POST.get('page', 1)) if type(request.POST.get('page', 1)) == int else 1
    sidx = 'level__timeseg__tz' if request.POST.get('sidx', 'level') == 'tz' else 'level'
    sidx = '-%s' % sidx if request.POST.get('sord', 'desc') == 'desc' else sidx

    objs = level_dept.objects.filter(DeptID=dept_id).order_by(sidx)
    p = Paginator(objs, limit)
    offset = 1 if p.count < (offset - 1) * limit else offset
    pp = p.page(offset)
    data = []
    for t in pp.object_list:
        d = {}
        d['id'] = t.id
        d['name'] = t.level.name
        d['tz'] = t.level.timeseg.Name
        data.append(d.copy())
    offset = p.num_pages if offset > p.num_pages else offset
    result = {
        "page": offset,
        "total": p.num_pages,
        "records": p.count,
        "rows": data
    }
    return getJSResponse(result)


@login_required
def post_level_dept(request):
    """
    门禁权限按部门设置 添加所属权限组
    """
    dept_id = request.GET.get('id', '')
    level_ids = request.POST.get('K', "").split(',')
    level_ids = list(map(int, level_ids))

    exist_level_ids = level_dept.objects.filter(DeptID_id=dept_id).values_list('level__id', flat=True)
    add_level_ids = set(level_ids) - set(exist_level_ids)
    add_list = []
    for add_level_id in add_level_ids:
        add_list.append(level_dept(DeptID_id=dept_id, level_id=add_level_id))
    level_dept.objects.bulk_create(add_list)

    # 给部门下的人员也同时设置新增的门禁权限
    emps = employee.objects.filter(DeptID__exact=dept_id).exclude(DelTag=1).exclude(OffDuty=1)
    lelist = []
    for emp in emps:
        for level_id in add_level_ids:
            lelist.append(level_emp(UserID=emp, level_id=level_id))
    level_emp.objects.filter(UserID__in=emps, level_id__in=add_level_ids).delete()  #先将相关的删除，以便批量插入
    level_emp.objects.bulk_create(lelist)

    doors = level_door.objects.filter(level__in=add_level_ids).values('door')
    devs = AccDoor.objects.filter(id__in=doors).distinct().values_list('device', flat=True)
    if devs:
        zk_send_acc_data(emps, devs)
    adminLog(time=datetime.datetime.now(), User_id=request.user.id, object=department.objByID(dept_id),
             model=str(_('Add permission groups by department')) + ":" + str(_('Add')),
             action=_(u"Modify")).save(force_insert=True)
    return getJSResponse({"ret": 0, "message": u"%s" % _('Operation Success')})


@login_required
def del_level_dept(request):
    """删除部门权限组"""
    ids = request.POST.get('ids', '').split(',')
    dept_id = request.POST.get('dept_id', '')
    if not ids or ids == ['']:
        return getJSResponse({"ret": -1, "message": u"%s" % _('Select the permission group you want to delete!')})

    # 部门下人员，同步删除门禁权限
    level_ids = [ld.level for ld in level_dept.objects.filter(id__in=ids)]
    depts = [ld.DeptID for ld in level_dept.objects.filter(id__in=ids)]
    emps = employee.objects.filter(DeptID__in=set(depts)).exclude(DelTag=1).exclude(OffDuty=1)
    level_emp.objects.filter(UserID__in=emps, level_id__in=level_ids).delete()
    level_dept.objects.filter(id__in=ids).delete()
    # 下发命令
    doors = level_door.objects.filter(level__in=level_ids).values('door')
    devs = AccDoor.objects.filter(id__in=doors).distinct().values_list('device', flat=True)
    if devs:
        zk_send_acc_data(emps, devs)
    for sn in devs:
        dev = getDevice(sn)
        appendDevCmd(dev, 'INFO')
    adminLog(time=datetime.datetime.now(), User_id=request.user.id, object=department.objByNumber(dept_id),
             model=str(_('Add permission groups by department')) + ":" + str(_('Delete')),
             action=_(u"Modify")).save(force_insert=True)
    return getJSResponse({"ret": 0, "message": u"%s" % _('successfully deleted!')})
