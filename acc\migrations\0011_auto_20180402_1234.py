# -*- coding: utf-8 -*-
# Generated by Django 1.11.10 on 2018-04-02 12:34
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('acc', '0010_auto_20180310_1337'),
    ]

    operations = [
        migrations.AlterField(
            model_name='accdoor',
            name='opendoor_type',
            field=models.IntegerField(choices=[(0, '\u81ea\u52a8\u5339\u914d'), (1, '\u4ec5\u6307\u7eb9'), (2, '\u4ec5\u5de5\u53f7'), (3, '\u4ec5\u5bc6\u7801'), (4, '\u4ec5\u5361'), (5, '\u6307\u7eb9\u6216\u5bc6\u7801'), (6, '\u5361\u6216\u6307\u7eb9'), (7, '\u5361\u6216\u5bc6\u7801'), (8, '\u5de5\u53f7\u52a0\u6307\u7eb9'), (9, '\u6307\u7eb9\u52a0\u5bc6\u7801'), (10, '\u5361\u52a0\u6307\u7eb9'), (11, '\u5361\u52a0\u5bc6\u7801'), (12, '\u6307\u7eb9\u52a0\u5bc6\u7801\u52a0\u5361'), (13, '\u5de5\u53f7\u52a0\u6307\u7eb9\u52a0\u5bc6\u7801'), (14, '\u5de5\u53f7\u52a0\u6307\u7eb9\u6216\u5361\u52a0\u6307\u7eb9'), (15, '\u4eba\u8138'), (16, '\u4eba\u8138\u52a0\u6307\u7eb9'), (17, '\u4eba\u8138\u52a0\u5bc6\u7801'), (18, '\u4eba\u8138\u52a0\u5361'), (19, '\u4eba\u8138\u52a0\u6307\u7eb9\u52a0\u5361'), (20, '\u4eba\u8138\u52a0\u6307\u7eb9\u52a0\u5bc6\u7801'), (21, '\u6307\u9759\u8109'), (22, '\u6307\u9759\u8109\u52a0\u5bc6\u7801'), (23, '\u6307\u9759\u8109\u52a0\u5361'), (24, '\u6307\u9759\u8109\u52a0\u5361\u52a0\u5bc6\u7801'), (200, '\u5176\u4ed6')], default=6, null=True, verbose_name='\u9a8c\u8bc1\u65b9\u5f0f'),
        ),
        migrations.AlterField(
            model_name='records',
            name='event_no',
            field=models.IntegerField(choices=[(-1, '\u65e0'), (0, '\u6b63\u5e38\u5f00\u95e8'),
                                               (1, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5185\u5f00\u95e8'),
                                               (2, '\u9996\u4eba\u5f00\u95e8(\u5237\u5361)'),
                                               (3, '\u591a\u4eba\u6b63\u5e38\u5f00\u95e8'),
                                               (4, '\u7d27\u6025\u72b6\u6001\u5bc6\u7801\u5f00\u95e8'),
                                               (5, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5f00\u95e8'),
                                               (6, '\u89e6\u53d1\u8054\u52a8\u4e8b\u4ef6'),
                                               (7, '\u53d6\u6d88\u62a5\u8b66'), (8, '\u8fdc\u7a0b\u5f00\u95e8'),
                                               (9, '\u8fdc\u7a0b\u5173\u95e8'),
                                               (10, '\u7981\u7528\u5f53\u5929\u5e38\u5f00\u65f6\u95f4\u6bb5'),
                                               (11, '\u542f\u7528\u5f53\u5929\u5e38\u5f00\u65f6\u95f4\u6bb5'),
                                               (12, '\u5f00\u542f\u8f85\u52a9\u8f93\u51fa'),
                                               (13, '\u5173\u95ed\u8f85\u52a9\u8f93\u51fa'),
                                               (14, '\u6b63\u5e38\u6309\u6307\u7eb9\u5f00\u95e8'),
                                               (15, '\u591a\u4eba\u5f00\u95e8(\u6309\u6307\u7eb9)'),
                                               (16, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5185\u6309\u6307\u7eb9'),
                                               (17, '\u5361\u52a0\u6307\u7eb9\u5f00\u95e8'),
                                               (18, '\u9996\u5361\u5f00\u95e8(\u6309\u6307\u7eb9)'),
                                               (19, '\u9996\u5361\u5f00\u95e8(\u5361\u52a0\u6307\u7eb9)'),
                                               (20, '\u5237\u5361\u95f4\u9694\u592a\u77ed'),
                                               (21, '\u95e8\u975e\u6709\u6548\u65f6\u95f4\u6bb5(\u5237\u5361)'),
                                               (22, '\u975e\u6cd5\u65f6\u95f4\u6bb5'), (23, '\u975e\u6cd5\u8bbf\u95ee'),
                                               (24, '\u53cd\u6f5c'), (25, '\u4e92\u9501\u672a\u901a\u8fc7'),
                                               (26, '\u591a\u4eba\u9a8c\u8bc1\u7b49\u5f85'),
                                               (27, '\u4eba\u672a\u767b\u8bb0'), (28, '\u95e8\u5f00\u8d85\u65f6'),
                                               (29, '\u5361\u5df2\u8fc7\u6709\u6548\u671f'),
                                               (30, '\u5bc6\u7801\u9519\u8bef'),
                                               (31, '\u6309\u6307\u7eb9\u95f4\u9694\u592a\u77ed'),
                                               (32, '\u591a\u4eba\u9a8c\u8bc1\u7b49\u5f85'),
                                               (33, '\u5df2\u8fc7\u6709\u6548\u671f'), (34, '\u4eba\u672a\u767b\u8bb0'),
                                               (35, '\u95e8\u975e\u6709\u6548\u65f6\u95f4\u6bb5'), (36,
                                                                                                    '\u95e8\u975e\u6709\u6548\u65f6\u95f4\u6bb5(\u6309\u51fa\u95e8\u6309\u94ae)'),
                                               (37, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u65e0\u6cd5\u5173\u95e8'),
                                               (38, '\u5361\u5df2\u6302\u5931'), (39, '\u9ed1\u540d\u5355'),
                                               (41, '\u9a8c\u8bc1\u65b9\u5f0f\u9519\u8bef'),
                                               (42, '\u97e6\u6839\u5361\u683c\u5f0f\u4e0d\u5bf9'),
                                               (44, '\u53cd\u6f5c\u9a8c\u8bc1\u5931\u8d25'),
                                               (48, '\u591a\u4eba\u5f00\u95e8\u5931\u8d25'),
                                               (51, '\u591a\u4eba\u9a8c\u8bc1'),
                                               (52, '\u591a\u4eba\u9a8c\u8bc1\u5931\u8d25'),
                                               (63, '\u95e8\u5df2\u9501\u5b9a'),
                                               (64, '\u51fa\u95e8\u6309\u94ae\u672a\u5728\u65f6\u95f4\u6bb5\u5185'),
                                               (65, '\u8f85\u52a9\u8f93\u5165\u4e0d\u5728\u65f6\u95f4\u6bb5\u5185'),
                                               (100, '\u9632\u62c6\u62a5\u8b66'),
                                               (101, '\u80c1\u8feb\u5bc6\u7801\u5f00\u95e8'),
                                               (102, '\u95e8\u88ab\u610f\u5916\u6253\u5f00'),
                                               (104, '\u65e0\u6548\u5361\u8fde\u7eed\u5237\u53615\u6b21'),
                                               (200, '\u95e8\u5df2\u6253\u5f00'), (201, '\u95e8\u5df2\u5173\u95ed'),
                                               (202, '\u51fa\u95e8\u6309\u94ae\u5f00\u95e8'),
                                               (204, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u7ed3\u675f'),
                                               (205, '\u8fdc\u7a0b\u5f00\u95e8\u5e38\u5f00'),
                                               (206, '\u8bbe\u5907\u542f\u52a8'), (207, '\u5bc6\u7801\u5f00\u95e8'),
                                               (208, '\u8d85\u7ea7\u7528\u6237\u5f00\u95e8'),
                                               (209, '\u89e6\u53d1\u51fa\u95e8\u6309\u94ae(\u88ab\u9501\u5b9a)'),
                                               (216, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5185(\u6309\u5bc6\u7801)'),
                                               (220, '\u8f85\u52a9\u8f93\u5165\u70b9\u65ad\u5f00'),
                                               (221, '\u8f85\u52a9\u8f93\u5165\u70b9\u77ed\u8def'),
                                               (225, '\u8f93\u5165\u70b9\u6062\u590d\u6b63\u5e38'),
                                               (226, '\u8f93\u5165\u70b9\u62a5\u8b66'),
                                               (222, '\u53cd\u6f5c\u9a8c\u8bc1\u6210\u529f'),
                                               (223, '\u53cd\u6f5c\u9a8c\u8bc1'), (224, '\u6309\u95e8\u94c3'),
                                               (233, 'Activate Lockdown'), (232, 'Verify Success'),
                                               (234, 'Deactivate Lockdown'), (300, 'Trigger Global Linkage'),
                                               (500, 'Global Anti-Passback(logical)')], null=True,
                                      verbose_name='\u4e8b\u4ef6\u63cf\u8ff0'),
        ),
        migrations.AlterField(
            model_name='records',
            name='verify',
            field=models.IntegerField(choices=[(0, '\u81ea\u52a8\u5339\u914d'), (1, '\u4ec5\u6307\u7eb9'), (2, '\u4ec5\u5de5\u53f7'), (3, '\u4ec5\u5bc6\u7801'), (4, '\u4ec5\u5361'), (5, '\u6307\u7eb9\u6216\u5bc6\u7801'), (6, '\u5361\u6216\u6307\u7eb9'), (7, '\u5361\u6216\u5bc6\u7801'), (8, '\u5de5\u53f7\u52a0\u6307\u7eb9'), (9, '\u6307\u7eb9\u52a0\u5bc6\u7801'), (10, '\u5361\u52a0\u6307\u7eb9'), (11, '\u5361\u52a0\u5bc6\u7801'), (12, '\u6307\u7eb9\u52a0\u5bc6\u7801\u52a0\u5361'), (13, '\u5de5\u53f7\u52a0\u6307\u7eb9\u52a0\u5bc6\u7801'), (14, '\u5de5\u53f7\u52a0\u6307\u7eb9\u6216\u5361\u52a0\u6307\u7eb9'), (15, '\u4eba\u8138'), (16, '\u4eba\u8138\u52a0\u6307\u7eb9'), (17, '\u4eba\u8138\u52a0\u5bc6\u7801'), (18, '\u4eba\u8138\u52a0\u5361'), (19, '\u4eba\u8138\u52a0\u6307\u7eb9\u52a0\u5361'), (20, '\u4eba\u8138\u52a0\u6307\u7eb9\u52a0\u5bc6\u7801'), (21, '\u6307\u9759\u8109'), (22, '\u6307\u9759\u8109\u52a0\u5bc6\u7801'), (23, '\u6307\u9759\u8109\u52a0\u5361'), (24, '\u6307\u9759\u8109\u52a0\u5361\u52a0\u5bc6\u7801'), (200, '\u5176\u4ed6')], default=200, null=True, verbose_name='verification'),
        ),
    ]
