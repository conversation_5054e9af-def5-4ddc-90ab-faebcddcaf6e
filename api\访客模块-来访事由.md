## 访客模块
### 访客模块-来访事由功能点新增来访事由
#### 访客模块来访事由功能点RAG检索锚点
<!-- RAG检索锚点: 新增来访事由、来访事由管理、访客事由、来访原因、事由配置 -->

#### 新增来访事由功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示新增表单]
    D --> E[填写事由信息]
    E --> F[验证表单数据]
    F --> G{数据验证}
    G -->|验证失败| H[显示错误信息]
    G -->|验证通过| I[检查事由编号重复]
    I --> J{编号是否重复}
    J -->|重复| K[提示编号已存在]
    J -->|不重复| L[保存事由信息]
    L --> M[更新事由列表]
    M --> N[记录操作日志]
    N --> O[返回成功信息]
    H --> E
    K --> E
    C --> P[结束]
    O --> P
```
#### 新增来访事由功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增来访事由业务流程。
2. 权限验证：验证当前用户是否具有来访事由管理权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行新增操作
3. 新增表单显示：
   - 显示来访事由信息录入表单
   - 提供必填和可选字段的输入界面
   - 显示字段验证规则和提示信息
4. 事由信息填写：
   - 事由编号（reasonNo）：必填，用于唯一标识来访事由
   - 事由名称（reasonName）：必填，来访事由的显示名称
   - 事由描述：可选，来访事由的详细说明
5. 表单数据验证：
   - 验证必填字段是否已填写
   - 检查字段长度是否符合要求
   - 验证数据格式的正确性
6. 事由编号重复检查：
   - 检查输入的事由编号是否已存在
   - 通过reason表的唯一约束验证
   - 考虑软删除记录的影响（DelTag字段）
7. 事由信息保存：
   - 将事由信息保存到reason表中
   - 设置DelTag为0，标识未删除
   - 确保数据的完整性和一致性
8. 事由列表更新：
   - 刷新来访事由列表显示
   - 更新事由统计信息
   - 同步事由信息到相关模块
9. 操作日志记录：记录来访事由创建操作的详细日志。
10. 流程完成：返回操作成功信息，完成事由创建流程。

#### 新增来访事由功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 新增的事由可用于访客预约时选择
   - 事由信息影响预约的分类和管理
   - 预约统计按事由进行分组

2. **与访客登记模块的关联**：
   - 访客登记时需要选择来访事由
   - 事由信息用于访客记录的分类
   - 登记统计按事由进行汇总

3. **与访客报表模块的关联**：
   - 事由信息用于访客报表的分类统计
   - 不同事由的访客数据分别统计
   - 事由趋势分析依赖于事由配置

4. **与系统配置模块的关联**：
   - 事由配置影响访客业务的选项
   - 系统初始化时会创建默认事由
   - 事由数量可能受系统限制

5. **与权限管理模块的关联**：
   - 事由管理需要相应的管理权限
   - 不同用户对事由的操作权限不同
   - 事由的使用范围可能受权限控制

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-来访事由功能点删除来访事由
#### 访客模块来访事由功能点RAG检索锚点
<!-- RAG检索锚点: 删除来访事由、移除来访事由、事由删除、来访事由管理删除 -->

#### 删除来访事由功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的事由]
    D --> E[检查事由使用情况]
    E --> F{事由是否被使用}
    F -->|正在使用| G[提示事由使用中]
    F -->|未使用| H[检查是否为默认事由]
    H --> I{是否为默认事由}
    I -->|是默认事由| J[提示不能删除默认事由]
    I -->|非默认事由| K[确认删除操作]
    K --> L{用户确认}
    L -->|取消删除| M[返回事由列表]
    L -->|确认删除| N[执行软删除]
    N --> O[更新事由状态]
    O --> P[记录操作日志]
    P --> Q[返回成功信息]
    C --> R[结束]
    G --> R
    J --> R
    M --> R
    Q --> R
```
#### 删除来访事由功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除来访事由业务流程。
2. 权限验证：验证当前用户是否具有事由删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行删除操作
3. 事由选择：
   - 从来访事由列表中选择要删除的事由
   - 显示事由的基本信息供确认
   - 支持单个事由的删除操作
4. 事由使用情况检查：
   - 检查事由是否被访客预约使用
   - 检查事由是否被访客登记使用
   - 确保删除操作不会影响现有数据
5. 默认事由检查：
   - 检查要删除的事由是否为系统默认事由
   - 系统初始化的6种默认事由（ID<=6）不允许删除
   - 保护系统基础配置数据
6. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 要求用户明确确认删除意图
7. 软删除执行：
   - 设置DelTag字段为1，标记事由为已删除
   - 保留事由数据用于历史记录
   - 避免物理删除造成的数据丢失
8. 事由状态更新：
   - 更新事由的删除状态
   - 从可用事由列表中移除
   - 保持数据的一致性
9. 操作日志记录：记录事由删除操作的详细日志。
10. 流程完成：返回删除成功信息，完成事由删除流程。

#### 删除来访事由功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 删除事由前需要检查预约记录的使用情况
   - 已使用的事由不能删除
   - 删除后的事由不再出现在预约选项中

2. **与访客登记模块的关联**：
   - 删除事由前需要检查登记记录的使用情况
   - 已使用的事由不能删除
   - 删除后的事由不再出现在登记选项中

3. **与访客报表模块的关联**：
   - 删除事由影响报表的分类统计
   - 历史数据中的事由信息仍然保留
   - 报表需要处理已删除事由的显示

4. **与系统配置模块的关联**：
   - 事由删除操作受系统权限配置限制
   - 默认事由受系统保护不能删除
   - 删除操作需要记录到系统审计日志

5. **与数据完整性模块的关联**：
   - 删除前需要检查数据完整性
   - 确保删除操作不会破坏数据关联
   - 软删除保证数据的可追溯性

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
