#!/usr/bin/env python
#coding=utf-8
import re

from django.db import models
from django.db.models import *
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.cache import cache
from django.utils.translation import gettext_lazy as _
from django.utils.translation import activate
from django.contrib.auth import get_user_model
import datetime,sys
from mysite.iclock.models import employee,department,iclock,holidays,getDevice,device_options,applocation
from mysite.iclock.i18n_backend import trans_opt
from mysite.base.models import GetParamValue



#各种机型常量
DEVICE_C3_100 = '4'
DEVICE_C3_200 = '1'
DEVICE_C3_400 = '2'
DEVICE_C3_400_TO_200 = '7'
DEVICE_C4_200 = '5'
DEVICE_C4_400 = '3'
DEVICE_C4_400_TO_200 = '6'
DEVICE_C3_160 = '8'
DEVICE_C3_260 = '9'
DEVICE_C3_460 = '10'
DEVICE_ELEVATOR   = '11'
DEVICE_ACCESS_CONTROL_DEVICE = '12' #一体机

PUBLISHED=(
(0, "NOT SHARE"),
(1, "SHARE READ"),
(2, "SHARE READ/WRITE")
)




INOUT_CHOICES = (
        (0, _(u'in')), (1, _(u'out')),
)

READER_CHOICES = (
        (1, _(u'card reading head')), (2, _(u'fingerprint reader')),
)

LCHANNEL_CHOICES = (
    (0, _(u'Channel 1')),
    (1, _(u'Channel 2')),
    (2, _(u'Channel 3')),
    (3, _(u'Channel 4')),
    (4, _(u'Channel 5')),
    (5, _(u'Channel 6')),
    (6, _(u'Channel 7')),
    (7, _(u'Channel 8')),
)


#开门方式 即验证方式
OPENDOOR_CHOICES = (
    (0, _(u'automatic matching')),
    (1, _(u'fingerprint only')),
    (2, _(u'Work only number')),
    (3, _(u'password only')),
    (4, _(u'only card')),
    (5, _(u'fingerprint or password')),
    (6, _(u'card or fingerprint')),
    (7, _(u'card or password')),
    (8, _(u'Work number plus fingerprint')),
    (9, _(u'Fingerprint plus password')),
    (10, _(u'Caga fingerprint')),
    (11, _(u'Caga Password')),
    (12, _(u'Fingerprint plus password plus card')),
    (13, _(u'Work number plus fingerprint plus password')),
    (14, _(u'Work number plus fingerprint or card plus fingerprint')),
    (15, _(u'human face')),
    (16, _(u'Face plus fingerprint')),
    (17, _(u'Face plus password')),
    (18, _(u'Face plus card')),
    (19, _(u'Face plus fingerprint plus card')),
    (20, _(u'Face plus fingerprint plus password')),
    (21, _(u'Finger vein')),
    (22, _(u'Finger vein plus password')),
    (23, _(u'Finger vein plus card')),
    (24, _(u'Finger vein plus card plus password')),
    (25, _(u'Palm')),
    (26, _(u'Palm&Card')),
    (27, _(u'Palm&FACE')),
    (28, _(u'Palm&FP')),
    (29, _(u'Palm&FACE&FP')),
    (200, _(u'other')),
)


OPENDOOR_CHOICES_DEFAULT = 6
#if get_option("IACCESS_5TO4"):
#    OPENDOOR_CHOICES_DEFAULT = 4
#else:
#    OPENDOOR_CHOICES_FINGERPRINT = (
#           (1, _(u'fingerprint only')),
#           (6, _(u'card or fingerprint')),
#           (10, _(u'Caga fingerprint')),
#       )
#    OPENDOOR_CHOICES += OPENDOOR_CHOICES_FINGERPRINT
#    OPENDOOR_CHOICES_DEFAULT = 6

# NewVFStyles类型（设备支持新验证方式，如inbio610,inbio640），详见push协议 门禁新验证参数
NEW_VERIFY_STYLE_CHOICES = (
    (0, _(u'None')),  # 逻辑位 ，0：或；1：与
    (1, _(u'FACE')),  # 人脸
    (2, _(u'palm')),  # 掌纹
    (3, _(u'Palm vein')),  #掌静脉
    (4, _(u'FP')),  # 指纹
    (5, _(u'Finger vein')),  # 指静脉
    (6, _(u'soundprint')),  #声纹
    (7, _(u'Iris')),  # 虹膜
    (8, _(u'retina')),  # 视网膜
    (9, _(u'PW')),  # 密码
    (10, _(u'PIN')),  # 工号
    (11, _(u'RF')),  # 卡
    (12, _(u'Qrcode')),  # 二维码/健康码
)

STATUS_CHOICES = (
        (0, _(u'nothing')), (1, _(u'Normally open')), (2, _(u'Normally closed')),
)


DOOR_STATE_CHOICES = (
        (0, _(u'disabled')),(1, _(u'Enable')), 
)

IS_GLOBAL_ANTIPASSBACK = (
        (0, _(u'No')), (1, _(u'Yes')),
)
def get_open_choices(choices):
    result=[]
    for t in OPENDOOR_CHOICES:
        if t[0] in choices:
            result.append(t)
    return result
def get_OPENDOOR_CHOICES(device):
    from mysite.core.zktools import zk_get_101_Choices
    OPEN_CHOICES=[3,4,6,7,11]
    default_value=6
    if device.Style in ['101','102','103']:#表示一体机
        if hasattr(device,'VerifyStyles'):
            OPEN_CHOICES=device.VerifyStyles
        else:
            OPEN_CHOICES=zk_get_101_Choices(device)
    else:
        if hasattr(device,'VerifyStyles'):
            OPEN_CHOICES=device.VerifyStyles
        else:
            OPEN_CHOICES=[3,4,7,11]
            if device.isFptemp==1:
                OPEN_CHOICES=OPEN_CHOICES+[0,1,5,6,9,10,12]
        if not  device.isFptemp:
            if 0 in OPEN_CHOICES:
                    OPEN_CHOICES.remove(0)

    # if device.ProductType in [15]:
        #     if 0 in OPEN_CHOICES:
        #         OPEN_CHOICES.remove(0)
        #     if 5 in OPEN_CHOICES:
        #         OPEN_CHOICES.remove(5)
        #     if 9 in OPEN_CHOICES:
        #         OPEN_CHOICES.remove(9)
        #     if 12 in OPEN_CHOICES:
        #         OPEN_CHOICES.remove(12)

    if 0 in OPEN_CHOICES:default_value=0
    elif 6 in OPEN_CHOICES:default_value=6
    else:default_value=4

    open_choices=list(set(OPEN_CHOICES))
    result = get_open_choices(open_choices)

    return (result,default_value,open_choices)  # 验证方式元组  默认值   验证方式列表
def get_event_point_name(item):
    event_point_name=item.event_point_name
    #if item.event_no==6:
        # try:
        #     obj=linkage_trigger.objects.get(id=item.card_no)
        #     id=obj.linkage_inout_id
        #     obj=linkage_inout.objects.get(id=id)
        #     return u'门%s'%(obj.input_id)
        # except:
        #     try:
        #         objs=AccDoor.objects.filter(device=item.SN,door_no=item.event_point_name)
        #         if objs:
        #             event_point_name=u'%s(%s)'%(objs[0].door_name,objs[0].door_no)
        #     except:
        #         pass

        #return event_point_name
        #return event_point_name
    if item.event_no==7:
        #event_point_name=item.device_id
        return event_point_name
    # if item.event_no in [220,221]:
    #     AuxIns=AuxIn.objects.filter(device=item.SN,aux_no=item.event_point_name)
    #     return AuxIns[0].aux_name
#        return u'辅助输入%s'%(item.event_point_name)

    # try:
    #     objs=AccDoor.objects.filter(device=item.SN,door_no=item.event_point_name)
    #     if objs:
    #         event_point_name=u'%s'%(objs[0].door_name)
    # except Exception as e:
    #     pass
    return event_point_name

def getCacheDoor(sn,door_no):
    door=cache.get('%s_door_%s_%s'%(settings.UNIT,sn,door_no))
    if door:return door
    try:
        objs=AccDoor.objects.filter(device=sn,door_no=door_no)
        if objs:
            door=objs[0]
    except Exception as e:
        pass
    if door:
        cache.set('%s_door_%s_%s'%(settings.UNIT,sn,door_no),door)
    return door

class zone(models.Model):
    code = models.CharField(_(u'area number'),blank=False,null=False,max_length=40,help_text=_(u'The maximum length is no more than 40 characters. After modifying the area number, it will not change the area where the device is located.'))
    name = models.CharField(_(u'area name'),max_length=40)
    remark = models.CharField(_(u'Remarks'), null=True, max_length=50, blank=True)
    parent = models.IntegerField(db_column="parent_id",verbose_name=_(u'superior area'), null=False, blank=True, default=0)
    DelTag = models.IntegerField(_(u'delete tag'),default=0, editable=False, null=True, blank=True,db_column='deltag')

    def __unicode__(self):
        return u"%s"%(self.name)
    def __str__(self):
        return u"%s"%(self.name)
    def parentname(id):
        if id.parent==0:
            return ''
        else:
            zones=zone.objects.filter(id=id.parent)
            if zones.count()>0:
                return zones[0].name
            else:
                return ''
    class Admin:
        list_display=("name","code", )
        search_fields=['code', 'name']
        @staticmethod
        def initial_data():
            if zone.objects.all().count()==0:
                zone(code='1',name=u'%s'%_('Default area'),parent=0).save()
                

    def save(self):
        if re.findall(r'\\', self.name):
            raise Exception(f'{_("Area name cannot contain special symbols")}')
        try:
            cache.delete("%s_iclock_zone_%s" % (settings.UNIT, self.id))

            obj=zone.objects.get(code=self.code,name=self.name)
            if obj.DelTag==1:
                obj.DelTag=0
                obj.remark=self.remark
                obj.parent=self.parent
                super(zone,obj).save()
            else:
                super(zone,self).save()

        except:
            objs = zone.objects.filter(code=self.code).first()
            if objs and objs.name != self.name and objs.DelTag == 1:
                objs.DelTag = 0
                objs.remark = self.remark
                objs.parent = self.parent
                objs.name = self.name
                super(zone, objs).save()
            else:
                super(zone, self).save()
    
    def get_children(self):
        """
        获取直系子区域（不包含子区域的子区域）
        :return:
        """
        return zone.objects.filter(parent=self.id).exclude(DelTag=1)
    
    def get_all_children(self, obj_list=[]):
        """
        获取每一层子区域（包含子区域的子区域）
        :return:子区域对象列表
        """
        for obj in self.get_children():
            if obj not in obj_list:
                obj_list.append(obj)
                obj.get_all_children(obj_list)
        return obj_list
       
       
    @staticmethod
    def objByID(id):
        if (id==None) or (id==0) or (id=='0') or (id<0):
            return None
        zn=cache.get("%s_iclock_zone_%s"%(settings.UNIT, id))
        if zn:return zn
        try:
            zn=zone.objects.get(id=id)
        except:
            zn=None
        if zn:
            cache.set("%s_iclock_zone_%s" % (settings.UNIT, id),zn)
        return zn

    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'code','width':80,'label':u"%s"%(zone._meta.get_field('code').verbose_name)},
                {'name':'name','width':200,'label':u"%s"%(zone._meta.get_field('name').verbose_name)},
                {'name':'remark','width':240,'label':u"%s"%(zone._meta.get_field('remark').verbose_name)},
                {'name':'parent','width':200,'label':u"%s"%(zone._meta.get_field('parent').verbose_name)}
                ]
    class Meta:
        verbose_name = _(u"area")
        unique_together=['code','name']
        default_permissions = ('browse', 'add', 'change', 'delete', 'export')


class ZoneAdmin(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    code = models.ForeignKey(zone,null=False, blank=False, on_delete=models.CASCADE)
    iscascadecheck=models.IntegerField(null=True, blank=True,editable=False,default=0)
    def __unicode__(self):
        return u"%s"%(self.user)
    def __str__(self):
        return u"%s"%(self.user)
    class Admin:
        list_display=("user","code", )
    class Meta:
        verbose_name=_("admin granted zone")
        verbose_name_plural=verbose_name
        unique_together = (("user", "code"),)

class IclockZone(models.Model):
    SN = models.ForeignKey(iclock,db_column='sn_id', on_delete=models.CASCADE)
    zone = models.ForeignKey(zone, verbose_name=_(u'area'), null=False, blank=False, on_delete=models.CASCADE)
    iscascadecheck=models.IntegerField(null=True, blank=True,editable=False,default=0)
    def __unicode__(self):
        return u"%s"%(self.SN)
    def __str__(self):
        return u"%s"%(self.SN)
    class Admin:
        list_display=("SN","zone", )
    class Meta:
        verbose_name=_(u"area")
        verbose_name_plural=verbose_name
        unique_together = (("SN", "zone"),)
    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'Device','index':'SN__SN','width':200,'label':u"%s"%(_('device'))}
                #{'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
                #{'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('Emp Name'))},
                #{'name':'DeptName','index':'UserID__DeptID__DeptName','width':100,'label':u"%s"%(_('department name'))},
                #{'name':'Title','index':'UserID__Title','width':80,'label':u"%s"%(_('Title'))},
                #{'name':'Device','hidden':True,'index':'SN','width':180,'label':u"%s"%(_('Device name'))}
                ]

level = (
    (1, _('normal')),
    (2, _('alarm')),
)

class AccEvent(models.Model):
    code = models.CharField(_(u'Event number'), max_length=10)
    name = models.CharField(_(u'Event name'), max_length=20)
    rank = models.CharField(_(u'Event level'), max_length=10, editable=True, choices=level)
    mark = models.CharField(_(u'Remarks'), max_length=40, null=True, blank=True, editable=True)

    def __unicode__(self):
        return u"%s" % (self.name)

    def __str__(self):
        return u"%s" % (self.name)

    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'code', 'sortable': False, 'width': 80, 'label': u"%s" % (_(u'Event number'))},
            {'name': 'name', 'width': 200, 'label': u"%s" % (_(u'Event name'))},
            {'name': 'rank', 'width': 100, 'label': u"%s" % (_(u'Event level'))},
            {'name': 'mark', 'sortable': False, 'width': 200, 'label': u"%s" % (_(u'Remarks'))}
        ]

    class Admin:
        search_fields = ['code','name']
        @staticmethod
        def initial_data():
            if AccEvent.objects.count() == 0:
                AccEvent(code=0, name=_(u'Normal opening'), rank='1', mark='').save()
                AccEvent(code=1, name=_(u'Punch during Passage Mode Time Zone'), rank='1', mark='').save()
                AccEvent(code=2, name=_(u'The first person opens the door (swipe the card)'), rank='1', mark='').save()
                AccEvent(code=3, name=_(u'Multiple people open the door normally'), rank='1', mark='').save()
                AccEvent(code=4, name=_(u'Emergency password opens'), rank='1', mark='').save()
                AccEvent(code=5, name=_(u'Open during Passage Mode Time Zone'), rank='1', mark='').save()
                AccEvent(code=6, name=_(u'trigger linkage event'), rank='1', mark='').save()
                AccEvent(code=7, name=_(u'Cancel the alarm'), rank='1', mark='').save()
                AccEvent(code=8, name=_(u'Open the door remotely'), rank='1', mark='').save()
                AccEvent(code=9, name=_(u'remote closing'), rank='1', mark='').save()
                AccEvent(code=10, name=_(u'Disable the usual opening time of the day'), rank='1', mark='').save()
                AccEvent(code=11, name=_(u'Enable the usual opening time of the day'), rank='1', mark='').save()
                AccEvent(code=12, name=_(u'Turn on auxiliary output'), rank='1', mark='').save()
                AccEvent(code=13, name=_(u'Turn off auxiliary output'), rank='1', mark='').save()
                AccEvent(code=14, name=_(u'Normally open the door by fingerprint'), rank='1', mark='').save()
                AccEvent(code=15, name=_(u'Multiple people open the door (by fingerprint)'), rank='1', mark='').save()
                AccEvent(code=16, name=_(u'Filling fingerprints during the normally open time period'), rank='1', mark='').save()
                AccEvent(code=17, name=_(u'Kajia Fingerprint Opens the Door'), rank='1', mark='').save()
                AccEvent(code=18, name=_(u'First card opens (by fingerprint)'), rank='1', mark='').save()
                AccEvent(code=19, name=_(u'First card opening (kajia fingerprint)'), rank='1', mark='').save()
                AccEvent(code=20, name=_(u'Swipe card interval is too short'), rank='1', mark='').save()
                AccEvent(code=21, name=_(u'Do not valid time period'), rank='1', mark='').save()
                AccEvent(code=22, name=_(u'Illegal time period'), rank='1', mark='').save()
                AccEvent(code=23, name=_(u'Unauthorized access'), rank='1', mark='').save()
                AccEvent(code=24, name=_(u'anti-submarine'), rank='1', mark='').save()
                AccEvent(code=25, name=_(u'Interlock failed'), rank='1', mark='').save()
                AccEvent(code=26, name=_(u'Multiple verification waits'), rank='1', mark='').save()
                AccEvent(code=27, name=_(u'People are not registered'), rank='1', mark='').save()
                AccEvent(code=28, name=_(u'door open timeout'), rank='2', mark='').save()
                AccEvent(code=29, name=_(u'The card has expired'), rank='1', mark='').save()
                AccEvent(code=30, name=_(u'wrong password'), rank='1', mark='').save()
                AccEvent(code=31, name=_(u'The fingerprint interval is too short'), rank='1', mark='').save()
                AccEvent(code=32, name=_(u'Verificando multi-usuario con huellas'), rank='1', mark='').save()
                AccEvent(code=33, name=_(u'Expired validity period'), rank='1', mark='').save()
                AccEvent(code=34, name=_(u'People are not registered'), rank='1', mark='').save()
                AccEvent(code=35, name=_(u'Do not valid time period'), rank='1', mark='').save()
                AccEvent(code=36, name=_(u'The door is not valid (press the exit button)'), rank='1', mark='').save()
                AccEvent(code=37, name=_(u'Unable to close the door during the normal opening period'), rank='1',mark='').save()
                AccEvent(code=38, name=_(u'The card has been lost'), rank='1', mark='').save()
                AccEvent(code=39, name=_(u'blacklist'), rank='1', mark='').save()
                AccEvent(code=41, name=_(u'Verification error'), rank='1', mark='').save()
                AccEvent(code=42, name=_(u'The Wiegand Card format is wrong'), rank='1', mark='').save()
                AccEvent(code=44, name=_(u'Anti-submarine verification failed'), rank='1', mark='').save()
                AccEvent(code=48, name=_(u'Multiple people fail to open the door'), rank='2', mark='').save()
                AccEvent(code=51, name=_(u'Multi-person verification'), rank='1', mark='').save()
                AccEvent(code=52, name=_(u'Multiple authentication failed'), rank='2', mark='').save()
                AccEvent(code=63, name=_(u'The door has been locked'), rank='1', mark='').save()
                AccEvent(code=64, name=_(u'The door button is not in the time period'), rank='1', mark='').save()
                AccEvent(code=65, name=_(u'Auxiliary input is not within the time period'), rank='1', mark='').save()
                AccEvent(code=73, name=_(u'Invalid QR code'), rank='1', mark='').save()
                AccEvent(code=74, name=_(u'The QR code has expired'), rank='1', mark='').save()
                AccEvent(code=100, name=_(u'tamper alarm'), rank='2', mark='').save()
                AccEvent(code=101, name=_(u'Duress password to open the door'), rank='2', mark='').save()
                AccEvent(code=102, name=_(u'The door was accidentally opened'), rank='2', mark='').save()
                AccEvent(code=104, name=_(u'Invalid card continuously swipe 5 times'), rank='2', mark='').save()
                AccEvent(code=200, name=_(u'The door is open'), rank='1', mark='').save()
                AccEvent(code=201, name=_(u'The door is closed'), rank='1', mark='').save()
                AccEvent(code=202, name=_(u'Going out the door to open the door'), rank='1', mark='').save()
                AccEvent(code=204, name=_(u'The end of the normally open time period'), rank='1', mark='').save()
                AccEvent(code=205, name=_(u'Remote opening is always open'), rank='1', mark='').save()
                AccEvent(code=206, name=_(u'Device startup'), rank='1', mark='').save()
                AccEvent(code=207, name=_(u'Password opens'), rank='1', mark='').save()
                AccEvent(code=208, name=_(u'Super user opens the door'), rank='1', mark='').save()
                AccEvent(code=209, name=_(u'Trigger exit button (locked)'), rank='1', mark='').save()
                AccEvent(code=215, name=_(u'First card opening (password)'), rank='1', mark='').save()
                AccEvent(code=216, name=_(u'In the normally open time period (by password)'), rank='1', mark='').save()
                AccEvent(code=220, name=_(u'Auxiliary input point disconnected'), rank='1', mark='').save()
                AccEvent(code=221, name=_(u'Auxiliary input point short circuit'), rank='2', mark='').save()
                AccEvent(code=225, name=_(u'The input point is back to normal'), rank='1', mark='').save()
                AccEvent(code=226, name=_(u'Input point alarm'), rank='2', mark='').save()
                AccEvent(code=222, name=_(u'Anti-submarine verification succeeded'), rank='1', mark='').save()
                AccEvent(code=223, name=_(u'anti-submarine verification'), rank='1', mark='').save()
                AccEvent(code=224, name=_(u'Press the doorbell'), rank='1', mark='').save()
                AccEvent(code=233, name=_(u'Activate Lockdown'), rank='1', mark='').save()
                AccEvent(code=232, name=_(u'Operation Success'), rank='1', mark='').save()
                AccEvent(code=234, name=_(u'Deactivate Lockdown'), rank='1', mark='').save()
                # 可视对讲
                AccEvent(code=239, name=_(u'Call Request'), rank='1', mark='').save()
                AccEvent(code=240, name=_(u'Cancel Call'), rank='1', mark='').save()
                AccEvent(code=300, name=_(u'Trigger Global Linkage'), rank='1', mark='').save()
                AccEvent(code=500, name=_(u'Global Anti-Passback(logical)'), rank='1', mark='').save()
    class Meta:
        default_permissions = ('browse','add', 'change','delete')
        verbose_name = _("AccEvent")


class timezones(models.Model):
    Name=models.CharField(_(u'time period name'), max_length=30,null=False,blank=True,db_column='name')
    remark = models.CharField(_(u'Remarks'), null=True, max_length=70, blank=True)
    tz = models.TextField(_(u'time'),null=True,editable=True)
    DelTag = models.IntegerField(_(u'delete tag'),default=0, editable=False, null=True, blank=True,db_column='deltag')

    def __unicode__(self):
        return u"%s"%(self.Name)
    def __str__(self):
        return u"%s"%(self.Name)
    @staticmethod
    def objByID(id):
        act = cache.get("%s_acc_timezones_%s"%(settings.UNIT, id))
        if act:
            return act
        try:
            act=timezones.objects.get(id=id)
        except:
            act=''
        if act:
            cache.set("%s_acc_timezones_%s"%(settings.UNIT,id),act)
        return act
    def save(self):
        try:
            tz = self.tz
            for i in range(10):
                for j in range(3):
                    ((int(tz[i * 36 + j * 12:i * 36 + j * 12 + 2]) * 100 + int(
                        tz[i * 36 + j * 12 + 3:i * 36 + j * 12 + 5])) << 16) + (
                                    int(tz[i * 36 + j * 12 + 6:i * 36 + j * 12 + 8]) * 100 + int(
                                tz[i * 36 + j * 12 + 9:i * 36 + j * 12 + 11]))
        except:
            raise Exception(u"%s" % (_(u"The time format is incorrect! Correct format: 00:00")))
        try:
            tz=timezones.objects.get(Name=self.Name)
            if tz.DelTag==1:
                tz.DelTag=0
                tz.tz=self.tz
                tz.remark=self.remark
                super(timezones,tz).save()
            else:
                super(timezones,self).save()
        except:
            super(timezones,self).save()
    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'tzid','sortable':False,'width':80,'label':u"%s"%(_(u'Numbering'))},
                {'name':'Name','width':200,'label':u"%s"%(timezones._meta.get_field('Name').verbose_name)},
                {'name':'remark','width':240,'label':u"%s"%(timezones._meta.get_field('remark').verbose_name)}
             #   {'name':'details','sortable':False,'width':100,'label':u"%s"%(_(u'operating'))}
                ]
    class Admin:
        search_fields=['Name']
        @staticmethod
        def initial_data():

            if timezones.objects.all().count()==0:
                tzs=''
                for i in range(10):
                    tzs=tzs+'00:00-23:59;00:00-00:00;00:00-00:00;'
                timezones(Name=_('24 hours all day traffic'),remark=_(u"Don&#39;t allow deletion, can be modified"),tz=tzs,DelTag=0).save()


    class Meta:
        verbose_name=_('ACTimeZones-table')
        verbose_name_plural=verbose_name
        unique_together = ("Name","DelTag")
        default_permissions = ('browse', 'add', 'change', 'delete')

ACGroupS_CHOICES=(
        (1,'1'),
        (2,'2'),
        (3,'3'),
        (4,'4'),
        (5,'5'),
        (6,'6'),
        (7,'7'),
        (8,'8'),
        (9,'9'),
        (10,'10'),
        (11,'11'),
        (12,'12'),
        (13,'13'),
        (14,'14'),
        (15,'15'),
        (16,'16'),
        (17,'17'),
        (18,'18'),
        (19,'19'),
        (20,'20'),
        (21,'21'),
        (22,'22'),
        (23,'23'),
        (24,'24'),
        (25,'25'),
        (26,'26'),
        (27,'27'),
        (28,'28'),
        (29,'29'),
        (30,'30'),
        (31,'31'),
        (32,'32'),
        (33,'33'),
        (34,'34'),
        (35,'35'),
        (36,'36'),
        (37,'37'),
        (38,'38'),
        (39,'39'),
        (40,'40'),
        (41,'41'),
        (42,'42'),
        (43,'43'),
        (44,'44'),
        (45,'45'),
        (46,'46'),
        (47,'47'),
        (48,'48'),
        (49,'49'),
        (50,'50'),
        (51,'51'),
        (52,'52'),
        (53,'53'),
        (54,'54'),
        (55,'55'),
        (56,'56'),
        (57,'57'),
        (58,'58'),
        (59,'59'),
        (60,'60'),
        (61,'61'),
        (62,'62'),
        (63,'63'),
        (64,'64'),
        (65,'65'),
        (66,'66'),
        (67,'67'),
        (68,'68'),
        (69,'69'),
        (70,'70'),
        (71,'71'),
        (72,'72'),
        (73,'73'),
        (74,'74'),
        (75,'75'),
        (76,'76'),
        (77,'77'),
        (78,'78'),
        (79,'79'),
        (80,'80'),
        (81,'81'),
        (82,'82'),
        (83,'83'),
        (84,'84'),
        (85,'85'),
        (86,'86'),
        (87,'87'),
        (88,'88'),
        (89,'89'),
        (90,'90'),
        (91,'91'),
        (92,'92'),
        (93,'93'),
        (94,'94'),
        (95,'95'),
        (96,'96'),
        (97,'97'),
        (98,'98'),
        (99,'99'),
)

ACUnlockCombS_CHOICES=(
        (1,'1'),
        (2,'2'),
        (3,'3'),
        (4,'4'),
        (5,'5'),
        (6,'6'),
        (7,'7'),
        (8,'8'),
        (9,'9'),
        (10,'10'),
)
ACGroup_VerifyType=(#门禁验证方式
        (0,_('FP/PW/RF/FACE')),
        (1,_('FP')),
        (2,_('PIN')),
        (3,_('PW')),
        (4,_('RF')),
        (5,_('FP/PW')),
        (6,_('FP/RF')),
        (7,_('PW/RF')),
        (8,_('PIN&FP')),
        (9,_('FP&PW')),
        (10,_('FP&RF')),
        (11,_('PW&RF')),
        (12,_('FP&PW&RF')),
        (13,_('PIN&FP&PW')),
        (14,_('FP&RF/PIN')),
        (15,_('FACE')),
        (16,_('FACE&FP')),
        (17,_('FACE&PW')),
        (18,_('FACE&RF')),
        (19,_('FACE&FP&RF')),
        (20,_('FACE&FP&PW')),
)
BOOLEANS=((0,_("No")),(1,_("Yes")),)
AC_Group_BOOLEANS=((0,_(u"Custom time period")),(1,_(u"Using Group Settings")),)

###该表未使用###
    ##门禁组类别设置表##
class ACGroup(models.Model):
    #GroupID=models.AutoField(primary_key=True,null=False, editable=False)
    GroupID=models.IntegerField(_('GroupID'),primary_key=True, editable=True,choices=ACGroupS_CHOICES,db_column='groupid')
    Name=models.CharField(_('ACGroup name'), max_length=30,null=True,blank=True,db_column='name')
    TimeZone1 = models.IntegerField(_('TimeZone1'),null=True, blank=True,default=0,db_column='timezone1')
    TimeZone2 = models.IntegerField(_('TimeZone2'),null=True, blank=True,default=0,db_column='timezone2')
    TimeZone3 = models.IntegerField(_('TimeZone3'),null=True,blank=True,default=0,db_column='timezone3')
    VerifyType=models.IntegerField(_('VerifyType'), null=True,editable=True,default=0,choices=ACGroup_VerifyType,db_column='verifytype')
    HolidayValid=models.SmallIntegerField(_("HolidayValid"), null=True,default=0, editable=True, choices=BOOLEANS,db_column='holidayvalid')
    def __unicode__(self):
        return u"%s"%(self.Name)
    def __str__(self):
        return u"%s"%(self.Name)
    @staticmethod
    def objByID(id):
        if id==None: return None
        g=cache.get("%s_iclock_acgroup_%s"%(settings.UNIT,id))
        if g: return g
        g=ACGroup.objects.get(GroupID=id)
        if g:
            cache.set("%s_iclock_acgroup_%s"%(settings.UNIT,id),g)
        return g
    def save(self):
        cache.delete("%s_iclock_acgroup_%s"%(settings.UNIT,self.GroupID))
        super(ACGroup,self).save()
    def delete(self):
        cache.delete("%s_iclock_acgroup_%s"%(settings.UNIT,self.GroupID))
        super(ACGroup, self).delete()

    @staticmethod
    def colModels():
        return [
                #{'name':'GroupID','hidden':True},
                {'name':'GroupID','width':80,'label':u"%s"%(ACGroup._meta.get_field('GroupID').verbose_name)},
                {'name':'Name','width':100,'label':u"%s"%(ACGroup._meta.get_field('Name').verbose_name)},
                {'name':'TimeZone1','width':100,'label':u"%s"%(ACGroup._meta.get_field('TimeZone1').verbose_name)},
                {'name':'TimeZone2','width':100,'label':u"%s"%(ACGroup._meta.get_field('TimeZone2').verbose_name)},
                {'name':'TimeZone3','width':100,'label':u"%s"%(ACGroup._meta.get_field('TimeZone3').verbose_name)},
                {'name':'VerifyType','width':100,'label':u"%s"%(ACGroup._meta.get_field('VerifyType').verbose_name)},
                {'name':'HolidayValid','width':100,'label':u"%s"%(ACGroup._meta.get_field('HolidayValid').verbose_name)}
                ]
    class Admin:
        search_fields=['Name']
    class Meta:
        db_table = 'accgroup'
        verbose_name=_('ACGroup-table')
        verbose_name_plural=verbose_name
        default_permissions = ()

###该表未使用###
    ##开锁组合类别设置表##
class ACUnlockComb(models.Model):
    #UnlockCombID=models.AutoField(primary_key=True,null=False, editable=False)
    UnlockCombID=models.IntegerField(_('UnlockCombID'),db_column='unlockcombid',primary_key=True,null=False, editable=True,choices=ACUnlockCombS_CHOICES)
    Name=models.CharField(_('ACUnlockComb name'), max_length=30,null=True,blank=True,db_column='name')
    Group01 = models.IntegerField(_('Group1'),null=True, blank=True,db_column='group01')
    Group02 = models.IntegerField(_('Group2'),null=True, blank=True,db_column='group02')
    Group03=models.IntegerField(_('Group3'),null=True,blank=True,db_column='group03')
    Group04=models.IntegerField(_('Group4'),null=True,blank=True,db_column='group04')
    Group05=models.IntegerField(_('Group5'),null=True,blank=True,db_column='group05')

    def __unicode__(self):
        return u"%s"%(self.Name)
    def __str__(self):
        return u"%s"%(self.Name)
    @staticmethod
    def colModels():
        return [
                #{'name':'UnlockCombID','hidden':True},
                {'name':'UnlockCombID','width':80,'label':u"%s"%(ACUnlockComb._meta.get_field('UnlockCombID').verbose_name)},
                {'name':'Name','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Name').verbose_name)},
                {'name':'Group01','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group01').verbose_name)},
                {'name':'Group02','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group02').verbose_name)},
                {'name':'Group03','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group03').verbose_name)},
                {'name':'Group04','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group04').verbose_name)},
                {'name':'Group05','width':100,'label':u"%s"%(ACUnlockComb._meta.get_field('Group05').verbose_name)}
                ]
    class Admin:
        search_fields=['Name']
    class Meta:
        db_table = 'acunlockcomb'
        verbose_name=_('ACUnlockComb-table')
        verbose_name_plural=verbose_name
        default_permissions = ()

###门禁组节假日时间段设置表##
#class ACCSetHoliday(models.Model):
#       id=models.AutoField(primary_key=True)
#       HolidayID=models.ForeignKey(holidays, db_column='HolidayID', verbose_name=_(u"假日"), on_delete=models.CASCADE)
#       TimeZoneID = models.ForeignKey(timezones,  verbose_name=_(u"time period"), on_delete=models.CASCADE)
#       EndTime = models.DateField(_('EndTime'), null=False, blank=False,editable=False)
#       Reserved=models.IntegerField(null=True,default=0,blank=True,editable=False)
#       Reserved1=models.FloatField(null=True,default=0,blank=True,editable=False)
#       Reserved2=models.CharField(max_length=30,null=False,editable=False)
#
#       def __unicode__(self):
#               return u"%s"%(u"%s"%(self.id))
#       def save(self):
#               self.EndTime=self.HolidayID.StartTime+datetime.timedelta(self.HolidayID.Duration)
#               models.Model.save(self)
#       @staticmethod
#       def colModels():
#               return [{'name':'id','hidden':True},
#                               {'name':'HolidayID','index':'HolidayID__HolidayID','width':100,'label':u"%s"%(_('HolidayID'))},
#                               {'name':'HolidayName','index':'HolidayID__HolidayName','width':100,'label':u"%s"%(_('HolidayName'))},
#                               {'name':'StartTime','index':'HolidayID__StartTime','width':100,'label':u"%s"%(_('StartTime'))},
#                               {'name':'EndTime','index':'HolidayID__EndTime','width':100,'label':u"%s"%(_('EndTime'))},
#                               {'name':'TimeZoneID','index':'TimeZoneID__TimeZoneID','width':100,'label':u"%s"%(_('TimeZone ID'))},
#                               {'name':'Name','index':'TimeZoneID__Name','width':100,'label':u"%s"%(_('ACTimeZones name'))}
#
#                               ]
#
#       class Admin:
#               search_fields=['HolidayID__HolidayName']
#       class Meta:
#               db_table = 'ACCSetHoliday'
#               verbose_name=_('ACCSetHoliday-table')
#               verbose_name_plural=verbose_name
#               unique_together = (( 'HolidayID', 'TimeZoneID'),)

###该表未使用###
    ##门禁权限类别设置表##
class UserACPrivilege(models.Model):
    UserID = models.ForeignKey(employee,db_column='userid', null=False,editable=True, verbose_name=u"%s"%(_(u"Employee")),default=None, on_delete=models.CASCADE)
    ACGroupID=models.ForeignKey(ACGroup, db_column='groupid', default=1,editable=True,verbose_name=u"%s"%(_(u"Access Control Group")), on_delete=models.CASCADE)
    IsUseGroup=models.IntegerField(_("IsUseGroup"), null=True, default=0, editable=True, choices=AC_Group_BOOLEANS,db_column='isusegroup')
    TimeZone1 = models.IntegerField(_('TimeZone1'),null=True, blank=True,editable=True,default=0,db_column='timezone1')
    TimeZone2 = models.IntegerField(_('TimeZone2'),null=True, blank=True,editable=True,default=0,db_column='timezone2')
    TimeZone3=models.IntegerField(_('TimeZone3'),null=True,blank=True,editable=True,default=0,db_column='timezone3')

    def __unicode__(self):
        return u"%s"%(self.UserID.__unicode__())
    def __str__(self):
        return u"%s"%(self.UserID.__str__())
    #@staticmethod
    #def objByID(id):
        #if id==None: return None
        #g=cache.get("%s_iclock_ACPrivilege_%s"%(settings.UNIT,id))
        #if g: return g
        #g=UserACPrivilege.objects.get(UserID=id)
        #if g:
            #cache.set("%s_iclock_ACPrivilege_%s"%(settings.UNIT,id),g)
        #return g
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def acgroup(self): #cached acgroup
        try:
            return ACGroup.objByID(self.ACGroupID_id)
        except:
            return None
    def save(self):
        cache.delete("%s_iclock_ACPrivilege_%s"%(settings.UNIT,self.UserID_id))
        super(UserACPrivilege,self).save()

    def userDevice(self):
        snlist=UserACCDevice.objects.filter(UserID=self.UserID)#.values_list('SN')
        snstr=''
        for sn in snlist:
            snstr+="%s,"%(sn.SN)#','.join(snlist)
        return snstr
    def delete(self):
        cache.delete("%s_iclock_ACPrivilege_%s"%(settings.UNIT,self.UserID_id))
        super(UserACPrivilege, self).delete()
    @staticmethod
    def clear():
        UserAC=UserACPrivilege.objects.all()
        UserAC.delete()
        #for e in UserAC:
            #e.delete()

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'PIN','index':'UserID__PIN','width':120,'label':u"%s"%(_('PIN'))},
                        {'name':'EName','index':'UserID__EName','width':120,'label':u"%s"%(_('EName'))},
                        {'name':'Name','index':'ACGroupID__Name','width':120,'label':u"%s"%(_('ACGroup-table name'))},
                        {'name':'IsUseGroup','width':80,'label':u"%s"%(UserACPrivilege._meta.get_field('IsUseGroup').verbose_name)},
                        {'name':'TimeZone1','width':80,'label':u"%s"%(UserACPrivilege._meta.get_field('TimeZone1').verbose_name)},
                        {'name':'TimeZone2','width':80,'label':u"%s"%(UserACPrivilege._meta.get_field('TimeZone2').verbose_name)},
                        {'name':'TimeZone3','width':80,'label':u"%s"%(UserACPrivilege._meta.get_field('TimeZone3').verbose_name)},
                        {'name':'SN','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Device List'))}
                        #{'name':'SN','width':100,'label':u"%s"%(UserACPrivilege._meta.userDevice())}
                        ]

    class Admin:
        lock_fields=['UserID']
        search_fields=['UserID__PIN','UserID__EName']
    class Meta:
        db_table = 'useracprivilege'
        verbose_name=_('UserACPrivilege-table')
        verbose_name_plural=verbose_name
        default_permissions = ()

###该表未使用###
    ##用户门禁设备类别设置表##
class UserACMachines(models.Model):
    id=models.AutoField(primary_key=True)
    UserID = models.ForeignKey(employee, db_column='userid', verbose_name=u"%s"%(_(u"Employee")), on_delete=models.CASCADE)
    SN = models.ForeignKey(iclock, db_column='sn', verbose_name=_('device'), null=False, on_delete=models.CASCADE)

    def __unicode__(self):
        return u"%s"%(self.UserID)
    def __str__(self):
        return u"%s"%(self.UserID)
    def Device(self):
        return getDevice(self.SN_id)
    def employee(self): #cached employee
        try:
            return employee.objByID(self.UserID_id)
        except:
            return None
    def userACPrivilege(self):
        try:
            return UserACPrivilege.objByID(self.UserID_id)
        except:
            return None
    def delete(self):
        try:
            UserACPrivilege.objects.filter(UserID=self.UserID).delete()
        except Exception as e:
            print ("====---%s"%e)
        super(UserACMachines, self).delete()
    @staticmethod
    def clear():
        UserAC=UserACMachines.objects.all()
        for e in UserAC:
            e.delete()

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                        {'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
                        {'name':'EName','index':'','width':100,'label':u"%s"%(_('EName'))},
                        {'name':'Name','index':'','width':100,'label':u"%s"%(_('ACGroup-table name'))},
                        {'name':'IsUseGroup','width':80,'label':u"%s"%(UserACPrivilege._meta.get_field('IsUseGroup').verbose_name)},
                        {'name':'TimeZone1','width':100,'label':u"%s"%(UserACPrivilege._meta.get_field('TimeZone1').verbose_name)},
                        {'name':'TimeZone2','width':100,'label':u"%s"%(UserACPrivilege._meta.get_field('TimeZone2').verbose_name)},
                        {'name':'TimeZone3','width':100,'label':u"%s"%(UserACPrivilege._meta.get_field('TimeZone3').verbose_name)},
                        {'name':'Device','sortable':False,'width':100,'label':u"%s"%(_('Device name'))}
                        ]

    class Admin:
        pass
    class Meta:
        db_table = 'useracmachines'
        verbose_name=_('UserACMachines-table')
        verbose_name_plural=verbose_name
        unique_together = (('UserID', 'SN'),)
        default_permissions = ()


###该表未使用###
#人员与门禁设备关联表
class UserACCDevice(models.Model):
    id=models.AutoField(primary_key=True)
    UserID=models.ForeignKey(employee, db_column='userid',  verbose_name=_(u"personnel"), on_delete=models.CASCADE)
    SN = models.ForeignKey(iclock, db_column='sn',  verbose_name=_(u"equipment"), on_delete=models.CASCADE)
    setTime = models.DateTimeField(_('SETTime'), null=True, blank=True,db_column='settime')
    Reserved=models.IntegerField(null=True,default=0,blank=True,editable=False,db_column='reserved')
    Reserved1=models.FloatField(null=True,default=0,blank=True,editable=False,db_column='reserved1')
    Reserved2=models.CharField(max_length=30,null=False,editable=False,db_column='reserved2')

    def __unicode__(self):
        return u"%s"%(self.id)
    def __str__(self):
        return u"%s"%(self.id)
    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                        {'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
                        {'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('Emp Name'))},
                        {'name':'DeptName','index':'UserID__DeptID__DeptName','width':100,'label':u"%s"%(_('DeptName'))},
                        {'name':'SN','index':'SN__SN ','width':100,'label':u"%s"%(_('serial number'))},
                        {'name':'Alias','index':'SN__Alias','width':100,'label':u"%s"%(_('Device Alias Name'))},
                        {'name':'IP','index':'SN__IPAddress','width':100,'label':u"%s"%(_('IPAddress'))}

                        ]

    class Admin:
        pass
    class Meta:
        db_table = 'useraccdevice'
        verbose_name=_('UserACCDevice-table')
        verbose_name_plural=verbose_name
        unique_together = (( 'UserID', 'SN'),)
        default_permissions = ()


###该表未使用###
#人员与门禁设备关联表  以后所有的下发门权限都从该表查询数据2017-03-14
class UserACDevice(models.Model):
    id=models.AutoField(primary_key=True)
    UserID=models.ForeignKey(employee, db_column='userid',  verbose_name=_(u"personnel"), on_delete=models.CASCADE)
    Card=models.CharField(max_length=20,null=False,editable=False,db_column='card')
    SN = models.ForeignKey(iclock, db_column='sn',  verbose_name=_(u"equipment"), on_delete=models.CASCADE)
    door_no=models.IntegerField(null=True,default=0,blank=True,editable=False)
    TimezoneId=models.IntegerField(null=True,default=0,blank=True,editable=False,db_column='timezoneid')
    setTime = models.DateTimeField(null=True, blank=True,db_column='settime')
    StartTime = models.DateTimeField( null=True, blank=True,db_column='starttime')
    EndTime = models.DateTimeField(null=True, blank=True,db_column='endtime')
    Reserved=models.CharField(max_length=30,null=False,editable=False,db_column='reserved')

    def __unicode__(self):
        return u"%s"%(self.id)
    def __str__(self):
        return u"%s"%(self.id)
    #@staticmethod
    #def colModels():
    #       return [{'name':'id','hidden':True},
    #                       {'name':'PIN','index':'UserID__PIN','width':100,'label':u"%s"%(_('PIN'))},
    #                       {'name':'EName','index':'UserID__EName','width':100,'label':u"%s"%(_('Emp Name'))},
    #                       {'name':'DeptName','index':'UserID__DeptID__DeptName','width':100,'label':u"%s"%(_('DeptName'))},
    #                       {'name':'SN','index':'SN__SN ','width':100,'label':u"%s"%(_('serial number'))},
    #                       {'name':'Alias','index':'SN__Alias','width':100,'label':u"%s"%(_('Device Alias Name'))},
    #                       {'name':'IP','index':'SN__IPAddress','width':100,'label':u"%s"%(_('IPAddress'))}
    #
    #                       ]

    class Admin:
        pass
    class Meta:
        verbose_name=_('UserACDevice-table')
        verbose_name_plural=verbose_name
        unique_together = (( 'UserID', 'SN','door_no','TimezoneId'),)
        default_permissions = ()


###该表未使用###
class IaccDevItemDefine(models.Model):
    ItemName=models.CharField(max_length=100,null=False,db_column='itemname')
    ItemType=models.CharField(max_length=20,null=True,db_column='itemtype')
    Author=models.ForeignKey(settings.AUTH_USER_MODEL, null=False,db_column='author_id', on_delete=models.CASCADE)
    ItemValue=models.TextField(max_length=100*1024,null=True,db_column='itemvalue')
    Published=models.IntegerField(null=True, choices=PUBLISHED, default=0,db_column='published')
    class Admin:
        pass
    class Meta:
        verbose_name=_("iaccdevitemdefine")
        verbose_name_plural=_("iaccdevitemdefine")
        db_table='iaccdevitemdefine'
        unique_together = (("ItemName","Author","ItemType"),)
        default_permissions = ()
        permissions = (
                                ('iaccMonitor_iaccdevitemdefine','iaccMonitor iaccdevitemdefine'),#监控记录表
                                ('iaccAlarm_iaccdevitemdefine','iaccAlarm iaccdevitemdefine'),#报警记录表
                                ('iaccUserRights_iaccdevitemdefine','iaccUserRights iaccdevitemdefine'),#用户权限表
        )
###该表未使用###
class IaccEmpItemDefine(models.Model):
    ItemName=models.CharField(max_length=100,null=False,db_column='itemname')
    ItemType=models.CharField(max_length=20,null=True,db_column='itemtype')
    Author=models.ForeignKey(settings.AUTH_USER_MODEL, null=False,db_column='author_id', on_delete=models.CASCADE)
    ItemValue=models.TextField(max_length=100*1024,null=True,db_column='itemvalue')
    Published=models.IntegerField(null=True, choices=PUBLISHED, default=0,db_column='published')
    class Admin:
        pass
    class Meta:
        verbose_name=_("iaccempitemdefine")
        verbose_name_plural=_("iaccempitemdefine")
        db_table='iaccempitemdefine'
        unique_together = (("ItemName","Author","ItemType"),)
        default_permissions = ()
        permissions = (
                                ('iaccRecordDetails_iaccempitemdefine','iaccRecordDetails iaccempitemdefine'),#记录明细
                                ('iaccSummaryRecord_iaccempitemdefine','iaccSummaryRecord iaccempitemdefine'),#记录汇总
                                ('iaccEmpUserRights_iaccempitemdefine','iaccEmpUserRights iaccempitemdefine'),#用户权限明细
                                ('iaccEmpDevice_iaccempitemdefine','iaccEmpDevice iaccempitemdefine'),#用户设备
        )

LEVEL_TYPE_CHOICES =(
    (0,_(u'Access Control Group')),
)


class AccMap(models.Model):
    """
    电子地图 top和left均为0
    """
    map_name = models.CharField(_(u'map name'), max_length=30, null=True, blank=False, default="", unique=True)
    map_path = models.CharField(_(u'map path'), max_length=50, null=True, blank=True, default="", editable=True)#路径
    #area = AreaForeignKey(verbose_name=_(u'Affiliated area'), null=True, blank=False, editable=True)# default=1,
    width = models.FloatField(_(u'width'), null=True, blank=True, default=0, editable=False)#单位px 0为无效值
    height = models.FloatField(_(u'height'), null=True, blank=True, default=0, editable=False)#px 0为无效值
    mulriple = models.IntegerField(_(u'multiple'), null=True, blank=True, default=5, editable=False)
    def __unicode__(self):
        return u"%s"%(self.map_name)
    def __str__(self):
        return u"%s"%(self.map_name)
    class Admin:
        pass
    class Meta:
        default_permissions = ('browse', 'add', 'change', 'delete')


class AccWiegandFmt(models.Model):
    u"""
    韦根卡格式
    """
    wiegand_name = models.CharField(_(u'Weiganka format name'), null=False, max_length=30, blank=False, default="")
    wiegand_count = models.IntegerField(_(u'Total number of digits'), null=True, blank=True, editable=True)
    odd_start = models.IntegerField(_(u'odd check start bit'), null=True, blank=True, editable=True)
    odd_count = models.IntegerField(_(u'odd check end bit'), null=True, blank=True, editable=True)
    even_start = models.IntegerField(_(u'even check start bit'), null=True, blank=True, editable=True)
    even_count = models.IntegerField(_(u'even check end bit'), null=True, blank=True, editable=True)
    cid_start = models.IntegerField(_(u'CID start bit'), null=True, blank=True, editable=True)#CID (Character identifier)就是字符识别码 or control id
    cid_count = models.IntegerField(_(u'CID end bit'), null=True, blank=True, editable=True)
    comp_start = models.IntegerField(_(u'Company code start bit'), null=True, blank=True, editable=True)
    comp_count = models.IntegerField(_(u'Company code end bit'), null=True, blank=True, editable=True)

    def __unicode__(self):
        return u"%s"%(self.wiegand_name)
    def __str__(self):
        return u"%s"%(self.wiegand_name)
    class Admin:
        list_display=("name","code", )
        search_fields=['code']
        @staticmethod
        def initial_data():
            if AccWiegandFmt.objects.all().count() == 0:
                AccWiegandFmt(wiegand_name=_(u'automatic matching'), wiegand_count=26, odd_start=1, odd_count=10,
                              even_start=11, even_count=26).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND26'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND26a'), wiegand_count=26, odd_start=1, odd_count=10,
                              even_start=11, even_count=26).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND34'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND34a'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND36'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND37'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND37a'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND50'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()
                AccWiegandFmt(wiegand_name=_(u'WIEGAND66'), wiegand_count=34, odd_start=1, odd_count=10,
                              even_start=11, even_count=34).save()

    class Meta:
        db_table = 'acc_wiegandfmt'
        verbose_name = _(u'Wegenka format')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')



"""
Door1InTimeAPB:比如设置10分钟，刷卡进去了，又出去了，没超过10分钟是不让进的,暂未实现
"""
class AccDoor(models.Model):
    u"""门表"""
    device = models.ForeignKey(iclock, verbose_name=_(u'device name'),  help_text=_(u'Not allowed to modify'),editable=True, null=True, blank=False, on_delete=models.CASCADE)
    door_no = models.IntegerField(_(u'door number'), null=True, blank=False, help_text=_(u'Not allowed to modify'),editable=True)
    door_name = models.CharField(_(u'door name'), null=True, max_length=30, blank=False, default="")
    lock_delay = models.IntegerField(_(u'Lock drive duration'), help_text=_(u'Seconds (range 0-254, 0 stands for normally closed)'), default=5, null=True, blank=False, editable=True)#锁驱动时长
    back_lock = models.BooleanField(_(u'closed door lock'), null=True,blank=True,default=True, editable=True)
    door_sensor_status = models.IntegerField(_(u'door magnetic type'),help_text=_(u'The door magnetic type is not available, the door magnetic delay is not editable'), default=0, null=True, blank=False, editable=True, choices=STATUS_CHOICES)
    sensor_delay = models.IntegerField(_(u'door magnetic delay'), help_text=_(u'Second (range 1-254), the door magnetic delay must be greater than the lock drive duration'), default=15, null=True, blank=True, editable=True)#门开超时---门磁报警延时
    opendoor_type = models.IntegerField(_(u'Ways of identifying'), null=True, default=OPENDOOR_CHOICES_DEFAULT, blank=False, editable=True, choices=OPENDOOR_CHOICES)#
    new_verify_style = models.CharField(max_length=32, null=True, blank=False, editable=True, choices=((0, 'or'), (1, 'and')))
    # 验证方式开门逻辑：或、与
    opendoor_logic = models.IntegerField(default=0, null=True, blank=True, editable=True, choices=((0, 'or'), (1, 'and')))
    rex_button_type = models.IntegerField(_(u'Exit switch input valid type'),default=0, null=True, blank=True, editable=True, choices=((0, _(u'nothing')), (1, _(u'Normally open and effective')), (1, _(u'Normally closed and effective'))))  # doorREXButtonType
    rex_button_input_mode = models.IntegerField(_(u'Exit switch input mode'),default=0, null=True, blank=True, editable=True, choices=((0, _(u'Normal input mode')), (1, _(u'Line detection input mode'))))  # doorREXButtonInputMode
    rex_button_supervised_resistor = models.IntegerField(_(u'Exit switch circuit detection resistance value'),default=1, null=True, blank=True, editable=True, choices=((1, _(u'1.2K resistance mode')), (2, _(u'2.2K resistance mode')), (3, _(u'4.7K resistance mode')), (4, _(u'10K resistance mode'))))  # doorREXButtonSupervisedResistor
    sensor_input_mode = models.IntegerField(_(u'Gate magnetic input mode'),default=0, null=True, blank=True, editable=True, choices=((0, _(u'Normal input mode')), (1, _(u'Line detection input mode'))))  # doorSensorInputMode
    sensor_supervised_resistor = models.IntegerField(_(u'Gate magnetic input line detection resistance value'),default=1, null=True, blank=True, editable=True, choices=((1, _(u'1.2K resistance mode')), (2, _(u'2.2K resistance mode')), (3, _(u'4.7K resistance mode')), (4, _(u'10K resistance mode'))))  # doorSensorSupervisedResistor
    #inout_state = models.IntegerField(_(u'Import and exit status'), default=0, null=True, blank=True, editable=True, choices=INOUT_CHOICES)#该字段暂时废弃 当前已写死
    lock_active = models.ForeignKey(timezones, verbose_name=_(u'door valid time period'),    related_name='lockactive_set',blank=False, editable=True, null=True,default=1, on_delete=models.CASCADE)#default=0,
    long_open = models.ForeignKey(timezones, verbose_name=_(u'Door open time period'),  blank=True, related_name='longopen_set',help_text=_(u'When the door is always in the normally open state, it is possible to release the normally open state by continuously swiping the card five times during this time period.'), editable=True, null=True, on_delete=models.CASCADE)#default=0,
    wiegand_fmt = models.ForeignKey(AccWiegandFmt, verbose_name=_(u'Wegenka format') ,  null=True, blank=False, editable=True,default=1, on_delete=models.CASCADE)#default=0,
    card_intervaltime = models.IntegerField(_(u'Swipe interval'), help_text=_(u'Seconds (range: 0-254, 0 means no interval)'), default=1, null=True, blank=False, editable=True)
    reader_type = models.IntegerField(_(u'reader type'), default=0, null=True, blank=True, editable=True, choices=READER_CHOICES)
    is_att = models.IntegerField(_(u'Attendance'), blank=True, null=True,editable=False)
    #force_pwd = models.CharField(_(u'duress password'), help_text=_(u'(maximum 8-digit integer)'), null=True, max_length=8, blank=True, default="")
    force_pwd = models.CharField(_(u'duress password'), help_text=_(u'(maximum 8-digit integer)'), null=True, max_length=18, blank=True, default="")#存储加密后的密码，将字段的最大长度增加为128
    #supper_pwd = models.CharField(_(u'emergency password'), help_text=_(u'(maximum 8-digit integer)'), null=True, max_length=8, blank=True, default="")
    supper_pwd = models.CharField(_(u'emergency password'), help_text=_(u'(maximum 8-digit integer)'), null=True, max_length=18, blank=True, default="")#存储加密后的密码，将字段的最大长度增加为128
    #video_linkageio = models.ForeignKey(Device, verbose_name=_(u'硬盘录像机'), related_name='videoserver_set', null=True, blank=True, editable=True, on_delete=models.CASCADE)
    #lchannel_num = models.SmallIntegerField(_(u'绑定通道'), default=0, null=True, blank=True, editable=True, choices=LCHANNEL_CHOICES)
    imap = models.ForeignKey(AccMap, verbose_name=_(u'affiliation map'), null=True, blank=True, editable=False, on_delete=models.CASCADE)#当前门所属地图
    duration_apb = models.IntegerField(_(u'Into the anti-submarine time'), help_text=_(u'minute(5-120)'), default=0, null=True, blank=True, editable=False)#反潜时长
    global_apb = models.IntegerField(_(u'Enable regional anti-submarine'), blank=True, null=True,editable=False, choices=IS_GLOBAL_ANTIPASSBACK)
    doorbtn_state = models.IntegerField(_(u'Out Door Button'), default=1, null=True, blank=True, editable=True, choices=DOOR_STATE_CHOICES)

    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'door_name','width':150,'label':u"%s"%(AccDoor._meta.get_field('door_name').verbose_name)},
                {'name':'door_no','sortable':False,'width':50,'align':'center','label':u"%s"%(AccDoor._meta.get_field('door_no').verbose_name)},
                {'name':'device','sortable':False,'width':150,'label':u"%s"%(AccDoor._meta.get_field('device').verbose_name)},
                {'name':'lock_active','sortable':False,'width':100,'label':u"%s"%(AccDoor._meta.get_field('lock_active').verbose_name)},
                {'name':'opendoor_type','sortable':False,'width':60,'label':u"%s"%(AccDoor._meta.get_field('opendoor_type').verbose_name)},
                {'name':'firstOpenCount','sortable':False,'width':70,'label':u"%s"%(_(u'The first person is always open'))},
                {'name':'combOpenCount','sortable':False,'width':90,'label':u"%s"%(_(u'Multiple people open the door'))}
                ]

    def __unicode__(self):
        return u"%s"%(self.door_name)
    def __str__(self):
        return u"%s"%(self.door_name)

    @property
    def get_opendoor_type_display(self):
        # 这里可以根据需要自定义显示的逻辑
        if self.new_verify_style:
            from mysite.core.zktools import trans_new_verify
            new_verify =  trans_new_verify(self.new_verify_style, NEW_VERIFY_STYLE_CHOICES)
            logic = new_verify.pop(0)  #逻辑位
            tuples_dict = {tup[0]: u'%s'%tup[1] for tup in NEW_VERIFY_STYLE_CHOICES}
            result_list = [tuples_dict.get(index, None) for index in new_verify]
            return str(logic).join(result_list)
        else:
            status_text = dict(OPENDOOR_CHOICES).get(self.opendoor_type)
        return status_text

    class Admin:
        lock_fields=["device"]
        #to_text_fields=["device","door_no"]
        search_fields = ['door_name']

    class Meta:
        db_table = 'acc_door'
        verbose_name = _(u'door')
        verbose_name_plural = verbose_name
        unique_together = ["device","door_no"]
        default_permissions = ('browse', 'add', 'change', 'delete')

    #对密码进行加密
    #def encrypt_password(self,password):
    #   return encryption(password)
    #
    #def check_password(self, raw_password,password):
    #   password = decryption(password)
    #   if raw_password == password:return True
    #   else: return False
    def save(self, *args, **kwargs):


        if self.door_sensor_status == 0:
            self.back_lock = 0#False
            self.sensor_delay = None

        # if self.force_pwd!="" or None:
        #    if accdoor[0].force_pwd == self.force_pwd:
        #        pass
        #    else:
        #        self.force_pwd = self.encrypt_password(self.force_pwd)
        #if self.supper_pwd!="" or None:
        #    if accdoor[0].supper_pwd == self.supper_pwd:
        #        pass
        #    else:
        #        self.supper_pwd = self.encrypt_password(self.supper_pwd)

        # 将前端传来的list数据转为协议要求的二进制字符
        if self.new_verify_style:
            from mysite.core.zktools import trans_to_new_verify
            self.new_verify_style = trans_to_new_verify(self.new_verify_style, self.opendoor_logic)

        cache.delete('%s_door_%s_%s'%(settings.UNIT,self.device_id,self.door_no))
        super(AccDoor, self).save(*args, **kwargs)

    def delete(self):
        try:
            cache.delete('%s_door_%s_%s' % (settings.UNIT, self.device_id, self.door_no))
            super(AccDoor, self).delete()
        except Exception as e:
            #print "=====%s"%e
            pass

    def firstOpenCount(self):
        firstopen = FirstOpen.objects.filter(door=self).count()
        if firstopen:
            return u"%s"%(_(u'Has been set'))
        else:
            return u"%s"%(_(u'not set'))

    def combOpenCount(self):
        #combopen_doors = combopen_door.objects.filter(door_id=self).values_list('id',flat=True)
        #combopen_combs = combopen_comb.objects.filter(combopen_door__in=combopen_doors).aggregate(Sum('opener_number'))['opener_number__sum']

        combopen_doors = combopen_door.objects.filter(door_id=self).count()
        if combopen_doors:
            return u"%s"%(_(u'Has been set'))
        else:
            return u"%s"%(_(u'not set'))


class AuxIn(models.Model):
    u"""输入"""
    device = models.ForeignKey(iclock, verbose_name=_(u'device name'),  help_text=_(u'Not allowed to modify'),editable=True, null=True, blank=False,default=None, on_delete=models.CASCADE)
    aux_no = models.IntegerField(_(u'Numbering'), null=True, blank=False, help_text=_(u'Not allowed to modify'),editable=True)
    aux_name = models.CharField(_(u'name'), null=True, max_length=30, blank=False, default="")
    printer_name = models.CharField(_(u'screen name'), null=True, max_length=30, blank=False, default="")
    remark = models.CharField(_(u'Remarks'), max_length=20, null=True, blank=True)
    input_type = models.IntegerField(_(u'有效类型'),default=0, null=True, blank=True, editable=True, choices=((0, _(u'nothing')), (1, _(u'Normally open and effective')), (1, _(u'Normally closed and effective'))))
    input_mode = models.IntegerField(_(u'模式'),default=0, null=True, blank=True, editable=True, choices=((0, _(u'Normal input mode')), (1, _(u'Line detection input mode'))))
    supervised_resistor = models.IntegerField(_(u'线路检测阻值'),default=1, null=True, blank=True, editable=True, choices=((1, _(u'1.2K resistance mode')), (2, _(u'2.2K resistance mode')), (3, _(u'4.7K resistance mode')), (4, _(u'10K resistance mode'))))

    @staticmethod
    def _need_trans_field():
        return ('aux_name', 'printer_name',)

    def __getattribute__(self, name):
        # print('### decorator calling')
        ret_val = super(AuxIn, self).__getattribute__(name)
        if not callable(ret_val) and name in self._need_trans_field():
            ret_val = ret_val.split('-')
            try:
                ret_val = "%s-%s"%(ret_val[0],ret_val[1])
            except:
                ret_val =ret_val[0]
        return ret_val

    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'aux_name','width':150,'label':u"%s"%(AuxIn._meta.get_field('aux_name').verbose_name)},
                {'name':'device','sortable':False,'width':150,'label':u"%s"%(AuxIn._meta.get_field('device').verbose_name)},
                {'name':'aux_no','sortable':False,'width':60,'align':'center','label':u"%s"%(AuxIn._meta.get_field('aux_no').verbose_name)},
                {'name':'printer_name','sortable':False,'width':100,'label':u"%s"%(AuxIn._meta.get_field('printer_name').verbose_name)},
                {'name':'remark','sortable':False,'width':100,'label':u"%s"%(AuxIn._meta.get_field('remark').verbose_name)}
                ]

    def __unicode__(self):
        return u"%s"%(self.aux_name)
    def __str__(self):
        return u"%s"%(self.aux_name)
    class Admin:
        lock_fields=['device']

    class Meta:
        verbose_name = _(u'enter')
        verbose_name_plural = verbose_name
        unique_together = (("device",'aux_no'),)
        default_permissions = ('browse', 'add', 'change', 'delete')

    #对密码进行加密
    #def encrypt_password(self,password):
    #   return encryption(password)
    #
    #def check_password(self, raw_password,password):
    #   password = decryption(password)
    #   if raw_password == password:return True
    #   else: return False
    def save(self, *args, **kwargs):



        # if self.force_pwd!="" or None:
        #    if accdoor[0].force_pwd == self.force_pwd:
        #        pass
        #    else:
        #        self.force_pwd = self.encrypt_password(self.force_pwd)
        #if self.supper_pwd!="" or None:
        #    if accdoor[0].supper_pwd == self.supper_pwd:
        #        pass
        #    else:
        #        self.supper_pwd = self.encrypt_password(self.supper_pwd)


        super(AuxIn, self).save(*args, **kwargs)

class AuxOut(models.Model):
    u"""输出"""
    device = models.ForeignKey(iclock, verbose_name=_(u'device name'),  help_text=_(u'Not allowed to modify'),editable=True, null=True, blank=False,default=None, on_delete=models.CASCADE)
    aux_no = models.IntegerField(_(u'Numbering'), null=True, blank=False, help_text=_(u'Not allowed to modify'),editable=True)
    aux_name = models.CharField(_(u'name'), null=True, max_length=30, blank=False, default="")
    printer_name = models.CharField(_(u'screen name'), null=True, max_length=30, blank=False, default="")
    remark = models.CharField(_(u'Remarks'), max_length=20, null=True, blank=True)

    @staticmethod
    def _need_trans_field():
        return ('aux_name', 'printer_name',)
    
    def __getattribute__(self, name):
        # print('### decorator calling')
        ret_val = super(AuxOut, self).__getattribute__(name)
        if not callable(ret_val) and name in self._need_trans_field():
            ret_val = ret_val.split('-')
            try:
                ret_val = "%s-%s"%(ret_val[0],ret_val[1])
            except:
                ret_val = ret_val[0]
            
        return ret_val
    
    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'aux_name','width':150,'label':u"%s"%(AuxOut._meta.get_field('aux_name').verbose_name)},
                {'name':'device','sortable':False,'width':150,'label':u"%s"%(AuxOut._meta.get_field('device').verbose_name)},
                {'name':'aux_no','sortable':False,'width':60,'align':'center','label':u"%s"%(AuxOut._meta.get_field('aux_no').verbose_name)},
                {'name':'printer_name','sortable':False,'width':100,'label':u"%s"%(AuxOut._meta.get_field('printer_name').verbose_name)},
                {'name':'remark','sortable':False,'width':100,'label':u"%s"%(AuxOut._meta.get_field('remark').verbose_name)}
                ]

    def __unicode__(self):
        return u"%s"%(self.aux_name)
    def __str__(self):
        return u"%s"%(self.aux_name)
    class Admin:
        lock_fields=['device']

    class Meta:
        verbose_name = _(u'enter')
        verbose_name_plural = verbose_name
        unique_together = (("device",'aux_no'),)
        default_permissions = ('browse', 'add', 'change', 'delete')

    #对密码进行加密
    #def encrypt_password(self,password):
    #   return encryption(password)
    #
    #def check_password(self, raw_password,password):
    #   password = decryption(password)
    #   if raw_password == password:return True
    #   else: return False
    def save(self, *args, **kwargs):



        # if self.force_pwd!="" or None:
        #    if accdoor[0].force_pwd == self.force_pwd:
        #        pass
        #    else:
        #        self.force_pwd = self.encrypt_password(self.force_pwd)
        #if self.supper_pwd!="" or None:
        #    if accdoor[0].supper_pwd == self.supper_pwd:
        #        pass
        #    else:
        #        self.supper_pwd = self.encrypt_password(self.supper_pwd)


        super(AuxOut, self).save(*args, **kwargs)


class TriggerList(models.Model):
    u"""反潜、互锁规则触发集合点"""
    id = models.AutoField(primary_key=True)  #触发点集合ID
    device = models.ForeignKey(iclock, editable=True, null=True, blank=False, on_delete=models.CASCADE)
    feature_type = models.CharField(max_length=32, null=True, blank=False, editable=True, choices=((0, 'door'), (4, 'reader')))
    # 触发点，这边实际存储将多个触发点(门或读头ID)一并保存，例 1,2,3 这样的数据格式（0 所有的门编号或者读头编号 >0 指定的门编号或者读头编号）
    trigger = models.CharField(_(u'Trigger'), null=True, max_length=100, blank=True, default="")

    def __str__(self):
        return u"%s"%(self.aux_name)

    class Meta:
        verbose_name = _(u'Trigger List')
        verbose_name_plural = verbose_name
        default_permissions = ('browse', 'add', 'change', 'delete')

    def save(self, *args, **kwargs):
        super(TriggerList, self).save(*args, **kwargs)
        # 触发下发到设备
        from mysite.core.zkcmdproc import zk_send_triggerlist
        zk_send_triggerlist(tl_obj=self)

    def delete(self):
        from mysite.core.zkcmdproc import zk_delete_triggerlist
        zk_delete_triggerlist(tl_obj=self)
        super(TriggerList, self).delete()


class level(models.Model):
    name = models.CharField(_(u'Privilege Group Name'),max_length=30, blank=False, null=False)
    timeseg = models.ForeignKey(timezones, verbose_name=_(u'period'), null=False, blank=False, editable=True,default=None, on_delete=models.CASCADE)
    itype = models.IntegerField(_(u'Permission Group Type'), choices=LEVEL_TYPE_CHOICES, default=0, null=True, blank=True, editable=False)
    is_visitor = models.IntegerField(_(u'Visitor Access Control Group'), default=0, choices=BOOLEANS, editable=True)
    # irange字段实际未使用
    irange = models.IntegerField(default=0, null=True, blank=True, editable=False) #特殊标记，当为-1时表示控制所有门，在leavel_door中不再保存门明细
    #        DelTag = models.IntegerField(default=0, editable=False, null=True, blank=True)
    def __unicode__(self):
        return u"%s"%(self.name)
    def __str__(self):
        return u"%s"%(self.name)
    class Admin:
        search_fields=['name']
    class Meta:
        verbose_name=_(u"Access Control Group")
        verbose_name_plural=_("AccLevel")
        unique_together = (("name",),)
        default_permissions = ('browse', 'add', 'change', 'delete', 'export')
        permissions = (
                                ('addemps_level','Add emps'),
                                ('delallemps_level','Delete allemps'),
        )
    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'width': 100, 'align': 'center', 'label': str(_('Privilege Group Number'))},
            {'name':'name', 'align': 'center', 'label': str(level._meta.get_field('name').verbose_name)},
            {'name': 'TimeZone', 'sortable': False, 'align': 'center', 'label': str(level._meta.get_field('timeseg').verbose_name)},
            {'name': 'doors', 'sortable': False, 'width': 80, 'align': 'center', 'label': str(_('Control gate number'))},
            {'name': 'emps', 'sortable': False, 'width': 80, 'align': 'center', 'label': str(_('Number of related personnel'))},
            {'name': 'is_visitor', 'sortable': False, 'width': 100, 'align': 'center', 'label': str(_('Visitor Access Control Group'))},
            {'name': 'action', 'sortable': False, 'width': 70, 'align': 'center', 'label': str(_('operating'))}
        ]
    def save(self, *args, **kwargs):
        super(level, self).save(*args, **kwargs)

    def timezones(self):
        tz = timezones.objByID(self.timeseg_id)
        if not tz:
            return ''
        if tz.DelTag == 1:
            return tz.Name + u"%s"%(_(u'(deleted)'))
        else:
            return tz.Name
        return tz

    def doorCount(self):
        if self.irange==-1:
            return AccDoor.objects.filter(device__DelTag=0).count()
        return level_door.objects.filter(level=self.id).count()

    def empCount(self):
        return level_emp.objects.filter(level=self.id).exclude(UserID__OffDuty=1).exclude(UserID__DelTag=1).count()



class AccDoorMap(models.Model):
    """
    门所在位置表
    """
    imap = models.ForeignKey(AccMap, verbose_name=_(u'affiliation map'), null=True, blank=True, editable=False, on_delete=models.CASCADE)
    door = models.ForeignKey(AccDoor, verbose_name=_(u"door"), editable=True, on_delete=models.CASCADE)
    left = models.CharField(_(u'left margin'),max_length=40,null=True, blank=True)
    top= models.CharField(_(u'top margin'),max_length=40,null=True, blank=True)

    def __unicode__(self):
        return u"%s"%(self.code)
    def __str__(self):
        return u"%s"%(self.code)
    class Admin:
        pass
    class Meta:
        default_permissions = ('browse', 'add', 'change', 'delete')

class AccMap_zone(models.Model):
    """
    电子地图与区域表，一个地图可以包含多个区域也可以是区域的一部分
    """
    imap = models.ForeignKey(AccMap, verbose_name=_(u'affiliation map'), null=True, blank=True, editable=False, on_delete=models.CASCADE)
    code = models.ForeignKey(zone,null=False, blank=False,default=None, on_delete=models.CASCADE)
    def __unicode__(self):
        return u"%s"%(self.code)
    def __str__(self):
        return u"%s"%(self.code)
    class Admin:
        pass

#门禁组人员明细
class level_emp(models.Model):
    level = models.ForeignKey(level, verbose_name=_(u"Access Control Group"), editable=False, on_delete=models.CASCADE)
    UserID = models.ForeignKey(employee, verbose_name=_(u"Access Control Team"), editable=False,db_column='userid_id', on_delete=models.CASCADE)
    def __unicode__(self):
        return u"%s"%(self.UserID)
    def __str__(self):
        return u"%s"%(self.UserID)
    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'level','width':200,'index':'level__name','label':u"%s"%(_(u'Rights Groups'))},
                {'name':'PIN','index':'UserID__PIN','width':140,'label':u"%s"%(_('PIN'))},
                {'name':'Card','index':'UserID__Card','width':140,'label':u"%s"%(_(u'card number'))},
                {'name':'EName','sortable':False,'width':120,'label':u"%s"%(employee._meta.get_field('EName').verbose_name)},
                {'name':'DeptName','sortable':False,'width':200,'label':u"%s"%(_('department name'))},
                {'name':'level_detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]
    class Admin:
        list_display=()
        search_fields = ['UserID__PIN','UserID__EName']
    class Meta:
        verbose_name=_(u'Access Control Staff')
        verbose_name_plural=_('level_emp')
        unique_together = (("level",'UserID'),)
        default_permissions = ('browse', 'add', 'change', 'delete')


#门禁组部门明细
class level_dept(models.Model):
    level = models.ForeignKey(level, verbose_name=_(u"Access Control Group"), editable=False, on_delete=models.CASCADE)
    DeptID = models.ForeignKey(department, db_column='dept_id', verbose_name=_(u"Access Control Team"), editable=False, on_delete=models.CASCADE)

    def __str__(self):
        return u"%s-%s" % (self.DeptID, self.level)

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'level','width':200,'index':'level__name','label':u"%s"%(_(u'Rights Groups'))},
                {'name':'DeptNumber','sortable':False,'width':120,'label':u"%s"%(_('Department Number'))},
                {'name':'DeptName','sortable':False,'width':200,'label':u"%s"%(_('department name'))},
                {'name':'level_detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]

    class Admin:
        list_display=()
        search_fields = ['DeptID__DeptNumber','DeptID__DeptName']

    class Meta:
        verbose_name=_(u'Access Control Dept')
        verbose_name_plural=_('level_dept')
        unique_together = (("level", 'DeptID'),)
        default_permissions = ('browse', 'add', 'change', 'delete')


#门禁组设备明细
class level_door(models.Model):
    level = models.ForeignKey(level, verbose_name=_(u"Access Control Group"), editable=False, on_delete=models.CASCADE)
    door = models.ForeignKey(AccDoor, verbose_name=_(u"door"), editable=False, on_delete=models.CASCADE)
    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'level','width':100,'index':'level__id','label':u"%s"%(_(u'Access Control Number'))},
                {'name':'level_detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]
    class Admin:
        list_display=()
        search_fields = []
    class Meta:
        verbose_name=_(u'Access Control Staff')
        verbose_name_plural=_('level_emp')
        unique_together = (("level",'door'),)
        default_permissions = ('browse', 'add', 'change', 'delete')


EVENT_LOG_AS_ATT = [0, 1, 2, 3, 14, 15, 16, 17, 18, 19, 21, 22, 23, 26, 32, 35,203,207]#用来作为考勤用的实时监控记录。20110518 lhj 增加多卡开门事件作为考勤原始记录

#事件选项
#公共事件，即C3和inbio公共的。
EVENT_CHOICES = (
    (-1, _(u'nothing')),
    (0, _(u'Normal opening')),#2 Door Opened Correctly  Normal Verify Open
    (1, _(u'Punch during Passage Mode Time Zone')),#1含门常开时段和首卡常开设置的开门时段 （开门后）  Verify During Passage Mode Time Zone
    (2, _(u'The first person opens the door (swipe the card)')),#2 First-Personnel Open
    (3, _(u'Multiple people open the door normally')),#2门打开 Multi-Personnel Open
    (4, _(u'Emergency password opens')),#2 Emergency Password Open
    (5, _(u'Open during Passage Mode Time Zone')),#Open during Passage Mode Time Zone
    (6, _(u'trigger linkage event')),#Linkage Event Triggered
    (7, _(u'Cancel the alarm')),#1远程开关门与扩展输出   ，至于远程开关门与扩展输出等动作执行后还有相应事件记录 Cancel Alarm
    (8, _(u'Open the door remotely')),#1 Remote Opening
    (9, _(u'remote closing')),#1 Remote Closing
    (10, _(u'Disable the usual opening time of the day')),#Disable Intraday Passage Mode Time Zone
    (11, _(u'Enable the usual opening time of the day')),#Enable Intraday Passage Mode Time Zone
    (12, _(u'Turn on auxiliary output')),# Auxiliary Output Remotely Open
    (13, _(u'Turn off auxiliary output')),# Auxiliary Output Remotely Close
    (14, _(u'Normally open the door by fingerprint')),
    (15, _(u'Multiple people open the door (by fingerprint)')),
    (16, _(u'Filling fingerprints during the normally open time period')),
    (17, _(u'Kajia Fingerprint Opens the Door')),
    (18, _(u'First card opens (by fingerprint)')),
    (19, _(u'First card opening (kajia fingerprint)')),
    (20, _(u'Swipe card interval is too short')),#2 Operation Interval too Short
    (21, _(u'Do not valid time period')),#2 Door Inactive Time Zone Verify Open
    (22, _(u'Illegal time period')),#2！！！有权限但是时间段不对。当前时段无合法权限 Illegal Time Zone
    (23, _(u'Unauthorized access')),#2当前时段无此门权限----卡已注册，但是没有该门的权限  Access Denied
    (24, _(u'anti-submarine')),#1 Anti-Passback
    (25, _(u'Interlock failed')),#1 Inerlock
    (26, _(u'Multiple verification waits')),#刷卡 Multi-Personnel Authentication Wait
    #(27, _(u'卡未注册')),#2
    (27, _(u'People are not registered')),#2 Unregistered Personnel
    (28, _(u'door open timeout')),#1 Open Door Timeout
    (29, _(u'The card has expired')),#2 Personnel Expired
    (30, _(u'wrong password')), #Password Error
    (31, _(u'The fingerprint interval is too short')),
    (32, _(u'Verificando multi-usuario con huellas')),#(32, _(u'多验证(按指纹)')),
    (33, _(u'Expired validity period')),
#    (34, _(u'指纹未注册')),
    (34, _(u'People are not registered')),#Unregisted Personnel
    (35, _(u'Do not valid time period')),
    (36, _(u'The door is not valid (press the exit button)')),#1 Door Inactive Time Zone(Press Exit Button)
    (37, _(u'Unable to close the door during the normal opening period')),#1 Failed to Close during Passage Mode Time Zone
    (38, _(u'The card has been lost')),#1
    (39, _(u'blacklist')),#1
    (41, _(u'Verification error')),#2 Verify Mode Error
    (42, _(u'The Wiegand Card format is wrong')),#2 Wiegand Format Error
    (44, _(u'Anti-submarine verification failed')),#Background Verify Failed
    (45, _(u'Anti-submarine verification timeout')), #后台验证超时
    (47, _(u'Failed to send command')), #发送命令失败
    (48, _(u'Multiple people fail to open the door')),#Multi-Personnel Verify Failed
    (49, _(u'Do not valid time period')), #门非有效时间段(密码)|门非有效时间段验证开门|21、35、49事件码合并,含义相同|
    (50, _(u'Swipe card interval is too short')), #按密码间隔太短|操作间隔太短|20、31、50事件码合并,含义相同|
    (51, _(u'Multi-person verification')),
    (52, _(u'Multiple authentication failed')), #多人验证失败(密码)|多人验证失败|40、48、52事件码合并,含义相同|
    (53, _(u'People are not registered')), #密码已过有效期|人已过有效期|29、33、53事件码合并,含义相同|
    (54, _(u'Battery voltage is too low')), #电池电压过低
    (55, _(u'Replace the battery immediately')), #立即更换电池
    (56, _(u'Illegal operation')), #非法操作
    (57, _(u'Backup power supply')), #后备电源
    (58, _(u'Normally open alarm')), #常开报警
    (59, _(u'Illegal management')), #非法管理
    (60, _(u'The door is locked')), #门被反锁
    (61, _(u'Repeat verification')), #重复验证
    (62, _(u'Disable Users')), #禁止用户



    (63, _(u'The door has been locked')),  #
    (64, _(u'The door button is not in the time period')),  #
    (65, _(u'Auxiliary input is not within the time period')),  #
    (74, _(u'The QR code has expired')),

    (100, _(u'tamper alarm')),  #1 Tamper alarm
    (101, _(u'Duress password to open the door')),  #2 Duress Open Alarm
    (102, _(u'The door was accidentally opened')),  #1  Opened Forcefully
    (104, _(u'Invalid card continuously swipe 5 times')),  #1
    #(105, _(u'网络掉线')),


    (110, _(u'Offline alarm for reading head')),  #读头离线报警
    


    (114, _(u'Circuit detection, fire alarm input disconnected')), # 线路检测，火警输入断开
    (115, _(u'Circuit detection, fire alarm input short circuit')), # 线路检测，火警输入短路
    (116, _(u'Circuit detection, auxiliary input disconnected')), # 线路检测，辅助输入断开
    (117, _(u'Circuit detection, auxiliary input short circuit')), # 线路检测，辅助输入短路
    (118, _(u'Circuit detection, exit switch disconnected')), # 线路检测，出门开关断开
    (119, _(u'Circuit detection, short circuit of the exit switch')), # 线路检测，出门开关短路

    (120, _(u'Circuit detection, door magnetic disconnection')), # 线路检测，门磁断开
    (121, _(u'Circuit detection, gate magnetic short circuit')), # 线路检测，门磁短路


    (200, _(u'The door is open')),#1 Door Opened Correctly
    (201, _(u'The door is closed')),#1 Door Closed Correctly
    (202, _(u'Going out the door to open the door')),#1 Exit Button Open

    (204, _(u'The end of the normally open time period')),#1 Passage Mode Time Zone Over
    (205, _(u'Remote opening is always open')),#1 Remote Normal Opening
    (206, _(u'Device startup')), #Device Started
    (207, _(u'Password opens')),#
    (208, _(u'Super user opens the door')), #Superuser Open
    (209, _(u'Trigger exit button (locked)')),#Exit Button triggered(Without Unlock)
   # (214, _(u'网络恢复')),
    (215,_(u'First card opening (password)')),     
    (216, _(u'In the normally open time period (by password)')),
    (220, _(u'Auxiliary input point disconnected')),#Auxilliary Input Disconnnected
    (221, _(u'Auxiliary input point short circuit')),#Auxilliary Input Shorted
    (225, _(u'The input point is back to normal')),
    (226, _(u'Input point alarm')),


    (222, _(u'Anti-submarine verification succeeded')),#Background Verify Success
    (223, _(u'anti-submarine verification')),#Background Verify
    (224, _(u'Press the doorbell')),# Ring the bell
    (233, _(u'Activate Lockdown')),  #
    (232, _(u'Operation Success')),  #
    (234, _(u'Deactivate Lockdown')),  #

    (237, _(u'Reading Head Online')),  #读头在线

    #以下两参数为可视对讲加入
    (239, _(u'Call Request')),  # 呼叫请求
    (240, _(u'Cancel Call')),  # 取消通话

    (243, _(u'Fire alarm input disconnected')),  # 火警输入断开  线路检测时表示常开
    (244, _(u'Fire alarm input short circuit')),  # 火警输入短路  线路检测时表示常闭

    (300, _(u'Trigger Global Linkage')),  #
    (500, _(u'Global Anti-Passback(logical)')),  #

    (4014, _(u'Fire input signal disconnected')),  #消防输入信号断开，结束门常开
    (4015, _(u'The door is online')), # 门已在线

    (5023, _(u'Restricted fire protection status')), # 消防状态受限中

    (6011, _(u'The door is offline')), # 门已离线
    (6012, _(u'Door dismantling alarm')), # 门拆机报警
    (6013, _(u'Fire input signal triggered, door open normally open')), # 消防输入信号触发，开启门常开
    (6015, _(u'Reset the power supply of the expansion device')), # 复位扩展设备电源
    (6016, _(u'Restore the factory settings of this machine')), # 恢复本机出厂设置

)
# VERIFYS=(
#     (0, _(u'卡或密码或指纹')),
#     (1, _(u'fingerprint only')),
#     (3, _(u'password only')),
#     (4, _(u'only card')),
#     (5, _(u'fingerprint or password')),
#     (6, _(u'card or fingerprint')),
#     (7, _(u'card or password')),
#     (10, _(u'卡+指纹')),
#     (11, _(u'card + password')),
#     (12, _(u'指纹加卡加密码')),
#
#     (16, _("Other")),
#     (200,_(u'other')),
#     (201, _(u'超级密码')),
#     (202, _(u'Open the door remotely')),
# )
STATE_CHOICES = (
    (0, _(u'in')),
    (1, _(u'out')),
    (2, _(u'nothing')),
    #(2, _(u'未知或无或其他')),#即其他
)
def get_EVENT_CHOICES_name(key):
    for t in EVENT_CHOICES:
        if (t[0]==key):
            return u'%s'%t[1]
    return key
class records(models.Model):
    pin = models.CharField(_(u'personnel number'), max_length=24, null=True, blank=True)
    name = models.CharField(_(u'name'), max_length=24, null=True, blank=True)
    card_no = models.CharField(_(u'card number'), max_length=10, null=True, blank=True)
    TTime = models.DateTimeField(_(u'time'),db_column='ttime')
    inorout = models.IntegerField(_(u'in and out'),null=True,choices=STATE_CHOICES)  # 门禁功能中表示进出
    verify = models.CharField(_(u'verification'), max_length=32, null=True, choices=OPENDOOR_CHOICES,default=200)
    SN = models.ForeignKey(iclock, verbose_name=_(u'device'), null=True, blank=True,db_column='sn_id', on_delete=models.CASCADE)
    #purpose = models.IntegerField(u'用途',null=True)#0考勤记录 1 会议记录    2 ...   3 ...
    event_no = models.IntegerField(_(u'Event Description'), null=True, choices=EVENT_CHOICES)
    eventaddr = models.CharField(_(u'work code'), max_length=30, null=True, blank=True)
    event_point_name = models.CharField(_(u'Event point'), max_length=30, null=True, blank=True)
    reader_name = models.CharField(_(u'reader name'), max_length=30, null=True, blank=True)
    Reserved = models.CharField(_(u'Reserved'), max_length=20, null=True, blank=True,db_column='reserved')
    dev_serial_num = models.IntegerField(_(u'Device serial number'),null=True,blank=True)
    temperature = models.DecimalField(_(u'Temperature'), decimal_places=2, max_digits=4, blank=True, null=True)
    mask_flag = models.IntegerField(_(u'Mask Flag'), null=True, blank=True, choices=BOOLEANS)  #0：表示没有佩戴口罩；1：表示有佩戴

    def employee(self): #cached employee
        try:
            if not self.pin or self.pin=='0':return ''
            return employee.objByPIN(self.pin)
        except Exception as e:
            #print "records==",e
            return ''

    def Time(self):
        return self.TTime

    def get_verify_display_ex(self):
        if hasattr(self.Device(), 'NewVFStyles'):
            from mysite.core.zktools import trans_new_verify
            new_verify =  trans_new_verify(self.verify, NEW_VERIFY_STYLE_CHOICES)
            logic = new_verify.pop(0)  #逻辑位
            tuples_dict = {tup[0]: u'%s'%tup[1] for tup in NEW_VERIFY_STYLE_CHOICES}
            result_list = [tuples_dict.get(index, None) for index in new_verify]
            return str(logic).join(result_list)
        else:
            tuples_dict = {tup[0]: u'%s'%tup[1] for tup in OPENDOOR_CHOICES}
            return tuples_dict.get(int(self.verify), None)

    def format_temperature(self):
        """
        格式化温度显示
        """
        try:
            if self.temperature:
                return '%.1f' % self.temperature
            else:
                return ''
        except:
            return ''

    def StrTime(self,diffSec=0):
        tm=self.Time()+datetime.timedelta(seconds=diffSec)
        return tm.strftime('%Y%m%d%H%M%S')
    @staticmethod
    def delOld(): return ("TTime", 365)
    def Device(self):
        return getDevice(self.SN_id)
    def __unicode__(self):
        return u"%s"%(str(self.event_no)+', '+self.TTime.strftime("%y-%m-%d %H:%M:%S"))
    def __str__(self):
        return u"%s"%(str(self.event_no)+', '+self.TTime.strftime("%y-%m-%d %H:%M:%S"))

    def getThumbnailUrl(self):
        return self.getImgUrl()


    def getImgUrl(self, default=None):
        import os
        from mysite.utils import getStoredFileURL,getStoredFileName
        from mysite.iclock.models import devicePIN
        if not self.pin: return default
        if os.path.exists(getStoredFileName("photo", None, devicePIN(self.pin)+".jpg")):
            url=getStoredFileURL("photo", None, devicePIN(self.pin)+".jpg")
            import random
            url += '?time=%s'%str(random.random()).split('.')[1]
            return url
        return default

    def getAccImgUrl(self, default=None):
        import os
        from mysite.utils import getUploadFileName,getUploadFileURL
        from mysite.iclock.models import formatPIN
        if GetParamValue('opt_users_rec_pic', '0') != '1': return ''
        device = self.Device()
        # emp = self.employee()
        # 为兼容访客抓拍照片的显示，直接用人员编号处理
        pin = self.pin
        if not pin or pin == '0':
            pin = ''
        if pin and device:
            pin = formatPIN(pin)
            fname = "%s-%s.jpg" % (self.StrTime(), pin)
            imgUrl = getUploadFileName("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            # 解决照片与记录时间差的问题
            fname = "%s-%s.jpg" % (self.StrTime(-1), pin)
            imgUrl = getUploadFileName("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            fname = "%s-%s.jpg" % (self.StrTime(-2), pin)
            imgUrl = getUploadFileName("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            fname = "%s-%s.jpg" % (self.StrTime(1), pin)
            imgUrl = getUploadFileName("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
            if os.path.exists(imgUrl):
                return getUploadFileURL("%s/%s" % (device.SN, self.TTime.strftime('%Y%m')), '', fname)
        return default
    @staticmethod
    def colModels(request=None):
        ret =  [
                {'name':'id','hidden':True},
                {'name':'TTime','width':120,'sortable':True,'search':False,'label':u"%s"%(records._meta.get_field('TTime').verbose_name)},
                {'name':'Device','index':'SN','width':150,'label':u"%s"%(_('Device name'))},
                {'name':'event_point','index':'','sortable':False,'width':90,'label':u"%s"%(_(u'Event point'))},
                {'name':'event_no','width':80,'search':False,'label':u"%s"%(records._meta.get_field('event_no').verbose_name)},
                {'name':'card_no','index':'','sortable':False,'width':80,'label':u"%s"%(_(u'card number'))},
                {'name':'pin','sortable':False,'width':120,'label':u"%s"%(_(u'personnel number'))},
				{'name':'name','sortable':False,'width':80,'label':u"%s"%(_(u'name'))},
                {'name':'deptName','sortable':False,'width':120,'label':u"%s"%(_(u'department'))},
                {'name':'inorout','sortable':False,'width':50,'label':u"%s"%(_(u'in/out'))},
                {'name':'verify','width':80,'sortable':False,'search':False,'label':u"%s"%(records._meta.get_field('verify').verbose_name)},
                {'name':'temperature','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Temperature')),'hidden':True},
                {'name':'mask_flag','search':False,'sortable':False,'width':80,'label':u"%s"%(_(u'Mask Flag')),'hidden':True},
                {'name': 'photo', 'search': False, 'sortable': False, 'width': 80, 'label': u"%s" % (_(u'Register photo'))},
                {'name':'thumbnailUrl','search':False,'sortable':False,'width':100,'label':u"%s"%(_(u'live photo'))}
               # {'name':'dev_serial_num','width':80,'sortable':False,'search':False,'label':u"%s"%(records._meta.get_field('dev_serial_num').verbose_name)}
                ]
        is_rec_pic=0
        if request:
            is_rec_pic = int(GetParamValue('opt_users_rec_pic', '0', request.user.id))
        is_emp_pic = GetParamValue('opt_basic_emp_pic', '0')
        for t in ret:
            if is_rec_pic != 1 and t['name'] == 'thumbnailUrl':
                t['hidden'] = True
            if is_emp_pic != '1' and t['name'] == 'photo':
                t['hidden'] = True
            if t['name'] in ('mask_flag','temperature'):
                if int(GetParamValue('opt_basic_antiepidemic','0')) == 1:
                    t['hidden']=False
        return ret


    class Meta:
        verbose_name=_(u"Access Control Record")
        verbose_name_plural=_(u"Access Control Record")
        unique_together = (("pin", "TTime","SN","dev_serial_num",'event_no','event_point_name'),)
        index_together = (('TTime'),)  #索引
        default_permissions=()

        permissions = (
                        ('monitor_oplog', 'Real Monitor'),
                        ('incoming_and_control', 'Incoming And Control'),
                        ('acc_records', 'Acc Records'),
                        ('acc_reports', 'Acc Reports'),
                        ('access_control_record_export', 'Access Control Record Export'),
                        ('access_reports_export', 'Access Reports Export'),
                        )

    class Admin:
        search_fields = ['pin','name']

INTERLOCK_CHOICES=(
(1, _(u"1-2 two-door interlocking")),
(2, _(u"3-4 two-door interlocking")),#为控制器
(3, _(u"1-2-3 three-door interlock")),

(4, _(u"1-2 two-door interlock and 3-4 two-door interlock")),
(5,_(u"1-2-3-4 four-door interlock"))
)

class InterLock(models.Model):
    u"""
    互锁门 1-2、3-4、1-2 3-4,1-2-3,1-2-3-4
    """
    device = models.ForeignKey(iclock, verbose_name=_(u'equipment'), editable=True, null=False, blank=False, on_delete=models.CASCADE)
    interlock_rule = models.IntegerField(_(u'Interlocking rules'), null=True, blank=False)  # 11, 12 用于P3000系列控制器, 采用新的互锁协议, 代表组内互锁, 组1与组2互锁
    initial_triggerlist = models.ForeignKey(TriggerList, editable=False, null=True, blank=True, related_name='il_itrigger', on_delete=models.CASCADE)
    target_triggerlist = models.ForeignKey(TriggerList, editable=False, null=True, blank=True, related_name='il_ttrigger', on_delete=models.CASCADE)
    remark = models.CharField(_(u'Remarks'), max_length=20, null=True, blank=True)

    class Admin:
        search_fields=['device__SN']
        lock_fields=['device']

    class Meta:
        verbose_name=_(u"Interlocking rules")
        verbose_name_plural=_("interlock")
        unique_together = (("device", "interlock_rule"),)
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = ()

    def __unicode__(self):
        return u"%s"%(self.device.SN)

    def __str__(self):
        return u"%s"%(self.device.SN)

    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'device','index':'device','width':180,'label':u"%s"%(_('Device name'))},
                {'name':'interlock_rule','index':'','sortable':False,'width':200,'label':u"%s"%(_(u'Interlocking rules'))},
                {'name':'remark','width':120,'sortable':False,'search':False,'label':u"%s"%(InterLock._meta.get_field('remark').verbose_name)}
                ]

    def get_details(self):
        result = []
        #names=AccDoor.objects.filter(device=self.device).order_by('id').values_list('door_no','door_name')

        door_name_1 = 1#u'%s' % (names[0][0])
        door_name_2 = 2#u'%s' % (names[1][0])
        if self.interlock_rule==1:
            result.append(_(u"%(a)s - %(b)s two-door interlock") % {"a": door_name_1, "b": door_name_2})
        if self.device.LockFunOn == 4:
            door_name_3 = 3#u'%s' % ( names[2][0])
            door_name_4 = 4#u'%s' % (names[3][0])

        if self.interlock_rule==2:
            result.append(_(u"%(a)s - %(b)s two-door interlock") % {"a": door_name_3, "b": door_name_4})
        if self.interlock_rule==3:
            result.append(_(u"%(a)s - %(b)s - %(c)s three-door interlock") % {"a": door_name_1, "b": door_name_2, "c": door_name_3})
        if self.interlock_rule==4:
            result.append(_(u"%(a)s - %(b)s two-door interlock with %(c)s - %(d)s two-door interlock") % {"a": door_name_1, "b": door_name_2, "c": door_name_3, "d": door_name_4})

        if self.interlock_rule==5:
            result.append(_(u"%(a)s - %(b)s - %(c)s - %(d)s four-door interlock") % {"a": door_name_1, "b": door_name_2, "c": door_name_3, "d": door_name_4})
        if self.interlock_rule==11:
            return _(u'intra-group interlock')
        if self.interlock_rule==12:
            return _(u'interlock between group 1 and group 2')
        return result and ','.join(result) or _(u'No interlock setting information')


class  FirstOpen(models.Model):
    u"""首人常开"""
    door = models.ForeignKey(AccDoor, verbose_name=_(u'door name'), editable=True, null=True, on_delete=models.CASCADE)
    timeseg = models.ForeignKey(timezones, verbose_name=_(u'Access Control Period'), editable=True, null=True, on_delete=models.CASCADE)#, default=1
    def __unicode__(self):
        return u"%s"%(self.door)
    def __str__(self):
        return u"%s"%(self.door)
    def delete(self):
        #sync_delete_firstcard(self.door)
        super(FirstOpen, self).delete()
        #self.door.device.check_firstcard_options(self.door)
    class Meta:
        verbose_name = _(u'The first person is always open')
        verbose_name_plural = verbose_name
        unique_together = (("door",'timeseg'),)
        default_permissions = ('browse', 'add', 'change', 'delete', 'export')
        permissions = (
                        ('addemps_firstopen','Add emps'),
                        ('delallemps_firstopen','Delete allemps'),
        )
    class Admin:
        lock_fields=['door']
        search_fields=['door__door_name']

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'door','width':200,'index':'door','label':u"%s"%(_(u'door name'))},
                {'name':'device','sortable':False,'index':'','width':200,'label':u"%s"%(_(u'device name'))},
                {'name':'timeseg','sortable':False,'width':120,'label':u"%s"%(_(u'Normally open time period'))},
                {'name':'empCount','sortable':False,'width':120,'label':u"%s"%(_(u'Number of people'))},
                {'name':'firstopen_detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]
    def empCount(self):
        return FirstOpen_emp.objects.filter(firstopen=self).exclude(UserID__DelTag=1).count()

#首人常开人员明细
class FirstOpen_emp(models.Model):
    firstopen = models.ForeignKey(FirstOpen, verbose_name=_(u"door"), editable=False, on_delete=models.CASCADE)
    UserID = models.ForeignKey(employee, verbose_name=_(u"personnel"), editable=False,db_column='userid_id', on_delete=models.CASCADE)
    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'door','width':200,'index':'','sortable':False,'label':u"%s"%(_(u'door name'))},
                {'name':'device','width':200,'index':'','sortable':False,'label':u"%s"%(_(u'device name'))},
                {'name':'PIN','index':'UserID__PIN','sortable':False,'width':100,'label':u"%s"%(_('PIN'))},
                {'name':'EName','sortable':False,'width':80,'label':u"%s"%(employee._meta.get_field('EName').verbose_name)},
                {'name':'Card','sortable':False,'width':80,'label':u"%s"%(employee._meta.get_field('Card').verbose_name)},
                {'name':'DeptName','sortable':False,'width':200,'label':u"%s"%(_('department name'))},
                {'name':'firstopen_details','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]
    class Admin:
        list_display=()
        search_fields = []
    class Meta:
        verbose_name=_(u'The first person is always open')
        verbose_name_plural=_('FirstOpen_emp')
        unique_together = (("firstopen",'UserID'),)
        default_permissions = ('browse', 'add', 'change', 'delete')


class linkage(models.Model):
    u"""联动"""
    device = models.ForeignKey(iclock, verbose_name=_(u'device name'),  help_text=_(u'Not allowed to modify'),editable=True, null=True, blank=False, on_delete=models.CASCADE)
    name = models.CharField(_(u'Linked name'), null=True, max_length=30, blank=False, default="")
    remark = models.CharField(_(u'Remarks'), max_length=20, null=True, blank=True)

    class Admin:
        list_display=()
        search_fields = ['name']
        lock_fields=['device']

    class Meta:
        verbose_name=_(u'Linkage')
        verbose_name_plural=_('linkage')
        unique_together = (('name'),)
        default_permissions = ('browse', 'add', 'change', 'delete')

    def __unicode__(self):
        try:
            return u'%s(%s)'%(self.device.Alias,self.device.SN)
        except:
            return  u'%s'%(self.name)

    def __str__(self):
        return u'%s(%s)'%(self.device.Alias,self.device.SN)

    def save(self, *args, **kwargs):
        super(linkage, self).save(*args, **kwargs)

    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'name','index':'','sortable':False,'width':200,'label':u"%s"%(linkage._meta.get_field('name').verbose_name)},
                {'name':'device','index':'device','width':240,'label':u"%s"%(_('Device name'))},
                {'name':'cond','index':'linkage_trigger','sortable':False,'width':240,'label':u"%s"%(_(u'Linkage trigger condition'))},
                {'name':'remark','width':120,'sortable':False,'search':False,'label':u"%s"%(linkage._meta.get_field('remark').verbose_name)}
                ]


class linkage_inout(models.Model):
    u"""联动输入输出input_type:0-any  1-AccDoor  2-Reader  3-AuxIn     output_type:0-AccDoor 1-AuxOut"""
    linkage = models.ForeignKey(linkage,   help_text=_(u'Not allowed to modify'),editable=True, null=True, blank=False, on_delete=models.CASCADE)
    input_type = models.IntegerField( null=True, blank=False)
    input_id = models.IntegerField( null=True, blank=False)
    output_type = models.IntegerField( null=True, blank=False)
    output_id = models.IntegerField( null=True, blank=False)
    action_type = models.IntegerField( null=True, blank=False)
    action_time = models.IntegerField( null=True, blank=False)
    class Admin:
        list_display=()
        search_fields = []
    class Meta:
        verbose_name=_(u'Linkage')
        verbose_name_plural=_('linkage_inout')
        default_permissions = ('browse', 'add', 'change', 'delete')
        #unique_together = (("device",'name'),)
    def __unicode__(self):
        return u"%s"%(self.linkage)
    def __str__(self):
        return u"%s"%(self.linkage)
    def save(self, *args, **kwargs):
        super(linkage_inout, self).save(*args, **kwargs)



class linkage_trigger(models.Model):
    u"""联动触发条件"""
    trigger_cond = models.IntegerField( null=True, blank=False)
    linkage_index = models.IntegerField( null=True, blank=False)
    linkage = models.ForeignKey(linkage, editable=True, null=True, blank=False, on_delete=models.CASCADE)
    linkage_inout = models.ForeignKey(linkage_inout, editable=True, null=True, blank=False, on_delete=models.CASCADE)

    def save(self, *args, **kwargs):
        super(linkage_trigger, self).save(*args, **kwargs)



    class Admin:
        list_display=()
        search_fields = []
    class Meta:
        verbose_name=_(u'Linkage')
        verbose_name_plural=_('linkage_trigger')
        #unique_together = (("device",'name'),)
    def __unicode__(self):
        return u"%s"%(self.trigger_cond)
    def __str__(self):
        return u"%s"%(self.trigger_cond)


class combopen(models.Model):
    u"""多人开门人员组"""
    name = models.CharField(_(u'Opening staff group name'), null=True, max_length=30, blank=False, default="")
    remark = models.CharField(_(u'Remarks'), max_length=20, null=True, blank=True)
    class Admin:
        list_display=()
        search_fields = ['name']

    class Meta:
        verbose_name=_(u'group name')
        verbose_name_plural=_('combopen')
        unique_together = (('name'),)
        default_permissions = ('browse', 'add', 'change', 'delete')
        permissions = (
                    ('addemps_combopen','Add emps'),
                    ('delallemps_combopen','Delete allemps'),
        )
    def __unicode__(self):
        return u"%s"%(self.name)
    def __str__(self):
        return u"%s"%(self.name)
    def save(self, *args, **kwargs):
        super(combopen, self).save(*args, **kwargs)

    @staticmethod
    def colModels():
        return [
                {'name':'id','hidden':True},
                {'name':'name','index':'','sortable':False,'width':220,'label':u"%s"%(combopen._meta.get_field('name').verbose_name)},
                {'name':'empCount','sortable':False,'width':80,'label':u"%s"%(_('EmpCount'))},
                {'name':'remark','width':120,'sortable':False,'search':False,'label':u"%s"%(combopen._meta.get_field('remark').verbose_name)},
                {'name':'detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))}
                ]

    def empCount(self):
        return combopen_emp.objects.filter(combopen=self.id).exclude(UserID__DelTag=1).count()


#开门组合人员明细
class combopen_emp(models.Model):
    combopen = models.ForeignKey(combopen, verbose_name=_(u"Open door combination"), editable=False, on_delete=models.CASCADE)
    UserID = models.ForeignKey(employee, verbose_name=_(u"personnel"), editable=False,db_column='userid_id', on_delete=models.CASCADE)
    def __unicode__(self):
        return u"%s"%(self.UserID)
    def __str__(self):
        return u"%s"%(self.UserID)
    @staticmethod
    def colModels():
        return  [
                        {'name':'id','hidden':True},
                        {'name':'PIN','index':'UserID__PIN','width':80,'label':u"%s"%(employee._meta.get_field('PIN').verbose_name),'frozen':True},
                        {'name':'EName','sortable':False,'width':80,'label':u"%s"%(employee._meta.get_field('EName').verbose_name),'frozen': True},
                        {'name':'DeptName','sortable':False,'index':'DeptID__DeptName','width':200,'label':u"%s"%(_('department name'))},
                        {'name':'detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))}
                ]
    class Admin:
        list_display=()
        search_fields = ['UserID_PIN']
    class Meta:
        verbose_name=_(u'Opening the door combination')
        verbose_name_plural=_('combopen_emp')
        unique_together = (("combopen",'UserID'),)
        default_permissions = ('browse', 'add', 'change', 'delete')


#开门组合
class combopen_door(models.Model):
    name = models.CharField(_(u'Open door combination name'),null=True, max_length=30, blank=False, default="")
    door = models.ForeignKey(AccDoor, verbose_name=_(u"door"), editable=True, on_delete=models.CASCADE)
    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'name','width':130,'index':'name','label':u"%s"%(_(u'Open door combination name'))},
                {'name':'door_no','sortable':False,'width':80,'align':'center','label':u"%s"%(AccDoor._meta.get_field('door_no').verbose_name)},
                {'name':'door_name','index':'door','width':150,'label':u"%s"%(AccDoor._meta.get_field('door_name').verbose_name)},
                {'name':'device','sortable':False,'width':150,'label':u"%s"%(AccDoor._meta.get_field('device').verbose_name)},
                {'name':'detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]
    def __unicode__(self):
            return self.name
    def __str__(self):
            return self.name
    class Admin:
        list_display=()
        search_fields = ['name']
        lock_fields=['door']

    class Meta:
        verbose_name=_(u'Open door combination name')
        verbose_name_plural=_('combopen_door')
        default_permissions = ('browse', 'add', 'change', 'delete')
        #unique_together = (("name",),)
    def device(self):
        return combopen_door.objects.get(id=self.id).door.device
#开门组合明细
class combopen_comb(models.Model):
    combopen = models.ForeignKey(combopen,  editable=False, on_delete=models.CASCADE)
    combopen_door = models.ForeignKey(combopen_door, editable=False, on_delete=models.CASCADE)
    opener_number = models.IntegerField( null=True, blank=False)
    sort = models.IntegerField( null=True, blank=False)


    class Admin:
        list_display=()
        search_fields = []
    class Meta:
        verbose_name=_(u'Open door combination')
        verbose_name_plural=_('combopen_comb')
        default_permissions = ('browse', 'add', 'change', 'delete')

SUPER_AUTH = (
        (0, _(u'No')),
        (15, _(u'Yes')),
)

class acc_employee(models.Model):
    #门禁区
    UserID = models.ForeignKey(employee, verbose_name=_(u"personnel"), editable=False,db_column='userid_id', on_delete=models.CASCADE)
    morecard_group = models.ForeignKey(combopen, verbose_name=_(u'Multiple Openers'), blank=True, editable=True, null=True, on_delete=models.CASCADE)
    set_valid_time = models.BooleanField(_(u'Set ValidTime'), default=False, null=False, blank=True)
    acc_startdate = models.DateTimeField(_(u'Start time'), null=True, blank=True)
    acc_enddate = models.DateTimeField(_(u'End Time'), null=True, blank=True)
    acc_super_auth = models.SmallIntegerField(_(u'Superuser'), default=0, null=True, blank=True, editable=True, choices=SUPER_AUTH)
    isblacklist = models.BooleanField(_(u'Open Blacklist'), default=False,null=False, blank=True, editable=True)

    class Admin:
        list_display=()
        search_fields = []

    @staticmethod
    def objByUserID(userid):
        if not userid:
            return None
        obj = cache.get("%s_acc_employee_%s"%(settings.UNIT, userid))
        if obj:
            return obj
        try:
            obj = acc_employee.objects.get(UserID=userid)
        except:
            obj = None
        if obj:
            cache.set("%s_acc_employee_%s"%(settings.UNIT, userid), obj)
        return obj

    def save(self, *args, **kwargs):
        cache.delete("%s_acc_employee_%s"%(settings.UNIT, self.UserID_id))
        super(acc_employee, self).save(*args, **kwargs)

    class Meta:
        verbose_name=_(u'Access Control Settings')
        verbose_name_plural=_('acc_employee')
        db_table='acc_employee'


#ONEDOOR_MODE = (
#    ('one_mode', _(u'1号门读头间反潜')),#单门双向（1）
#)
#
#TWODOORS_MODE = (
#    ('one_mode', _(u'1号门读头间反潜')),#两门双向（1）
#    ('two_mode', _(u'2号门读头间反潜')),#两门双向（2）
#    ('three_mode', _(u'1,2号门间反潜')),#两门双向（4）+ 两门单向（1）
#)
#
#FOURDOORS_MODE = (
#    ('one_mode', _(u'1-2门反潜')),#四门单向（1）
#    ('two_mode', _(u'3-4门反潜')),#四门单向（2）
#    ('three_mode', _(u'1/2-3/4门反潜')),#四门单向（4）
#    ('four_mode', _(u'1-2/3门反潜')),#四门单向（5）
#    ('five_mode', _(u'1-2/3/4门反潜')),#四门单向（6）

#)

#综上，五种mode只有four_mode可能存在多个值 ，1和4，即两门双向和四门单向为4，两门单向为1

class AntiPassBack(models.Model):
    u"""
    反潜 两门：1（1-1）、2（2-2）、(1-1，2-2)、1-2互锁 四门：1-2门反潜、3-4门反潜、（1-2 and 3-4）、1/2-3/4门反潜、1-2/3门反潜、1-2/3/4门反潜 ，单门：门自身反潜
    """
    device = models.ForeignKey(iclock, verbose_name=_(u'equipment'), null=True, blank=False, editable=True, on_delete=models.CASCADE)
    apb_rule = models.IntegerField(_(u'anti-submarine mode'), null=True, blank=False, editable=True)  # 当值为1001，1002时，为软件为兼容新反潜协议，自定义的值
    initial_triggerlist = models.ForeignKey(TriggerList, editable=False, null=True, blank=True, related_name='apb_itrigger', on_delete=models.CASCADE)
    target_triggerlist = models.ForeignKey(TriggerList, editable=False, null=True, blank=True, related_name='apb_ttrigger', on_delete=models.CASCADE)

    def __unicode__(self):
        return u"%s"%(u"%s-%s"%(self.device.SN,self.apb_rule))

    def __str__(self):
        return u"%s"%(u"%s-%s"%(self.device.SN,self.apb_rule))

    class Admin:
        list_display=()
        search_fields = ['=device__SN']
        lock_fields=['device']

    class Meta:
        verbose_name=_(u'anti-submarine setting')
        verbose_name_plural=_('antiback')
        default_permissions = ('browse', 'add', 'change', 'delete')
        #unique_together = (("device"),)

    def delete(self):
        if self.apb_rule in [1001, 1002]:  # 新反潜规则（门反潜，读头）
            # 下发删除反潜规则命令
            from mysite.core.zkcmdproc import zk_delete_antipassback
            zk_delete_antipassback(self)
            # 删除触发点集合时, delete会同步下发命令
            itriger = self.initial_triggerlist
            itriger.delete() 
            ttriger = self.target_triggerlist
            ttriger.delete()
        return super(AntiPassBack,self).delete()

    def getantibackoption(self):
        anti = 0#无反潜
        #if self.one_mode and self.two_mode: #双门双向
           # anti += 3
        #if self.one_mode:#单门双向，双门双向，四门单向
        #    anti += 1
        #if self.two_mode:
        #    anti += 2
        #if self.three_mode:
        #    #if self.device.accdevice.reader_count == 4:#两门双向+四门单向（均为四个读头）
        #    anti += 4
        #    #elif self.device.accdevice.reader_count == 2:#两门单向（两个读头，如C4-200）
        #       # anti = 1
        #if self.four_mode:
        #    anti += 5
        #if self.five_mode:
        #    anti += 6
        #if self.six_mode:
        #    anti += 16
        #if self.seven_mode:
        #    anti += 32
        #if self.eight_mode:
        #    anti += 64
        #if self.nine_mode:
        #    anti += 128
        anti=self.apb_rule
        return anti

    def get_details(self):
        result = []
        names=AccDoor.objects.filter(device=self.device).order_by('id').values_list('door_no','door_name')
        door_name_1 = u'%s(%s)' % (names[0][1], names[0][0])
        reader_Count=1
        #if self.device.LockFunOn == 2:
        obj=device_options.objects.filter(SN=self.device,ParaName='ReaderCount')
        if obj:
            reader_Count=int(obj[0].ParaValue)

        if self.device.ProductType == 25:
            #专业门禁一体机反潜
            # AntiPassback参数：
            # 0：无
            # 1：出反潜
            # 2：入反潜
            # 3：出入反潜
            # 4：无且保存记录
            if self.apb_rule==1:
                result.append(u'%s'%_(u'antiback-out'))
            if self.apb_rule==2:
                result.append(u'%s'%_(u'antiback-in'))
            if self.apb_rule==3:
                result.append(u'%s'%_(u'antiback-inout'))
            if self.apb_rule==4:
                result.append(u'%s'%_(u'antiback-no&save record'))
                
        elif self.device.LockFunOn == 1:#单门双向
            if self.apb_rule==1:
                result.append(_(u"%s read head anti-submarine") % door_name_1)

        elif self.device.LockFunOn == 2 and reader_Count == 4:#两门双向 含C4-200和C3400-200
            door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
            if self.apb_rule==1:
                result.append(_(u"%s read head anti-submarine") % door_name_1)
            if self.apb_rule==2:
                result.append(_(u"%s read head anti-submarine") % door_name_2)
            if self.apb_rule==3:
                result.append(_(u"%s read head anti-submarine") % door_name_1)
                result.append(_(u"%s read head anti-submarine") % door_name_2)

            if self.apb_rule==4:
                result.append(_(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2})

        elif self.device.LockFunOn == 2 and reader_Count == 2:#两门单向
            door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
            if self.apb_rule==4:
                result.append(_(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2})

        elif self.device.LockFunOn == 4:
            door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
            door_name_3 = u'%s(%s)' % (names[2][1], names[2][0])
            door_name_4 = u'%s(%s)' % (names[3][1], names[3][0])
            if self.apb_rule==1:
                result.append(_(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2})
            if self.apb_rule==2:
                result.append(_(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_3, "b": door_name_4})
            if self.apb_rule==3:
                result.append(_(u"%(a)s with %(b)s or %(c)s and %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2, "c": door_name_3, "d": door_name_4})
            if self.apb_rule == 4:
                result.append(
                    _(u"%(a)s or %(b)s and %(c)s or %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                                "c": door_name_3, "d": door_name_4})
            if self.apb_rule==5:
                result.append(_(u"%(a)s with %(b)s or %(c)s anti-submarine") % {"a": door_name_1, "b": door_name_2, "c": door_name_3})
            if self.apb_rule==6:
                result.append(_(u"%(a)s with %(b)s or %(c)s or %(d)s anti-submarine") % {"a":door_name_1, "b": door_name_2, "c": door_name_3, "d": door_name_4})
            if self.apb_rule==16:
                result.append(_(u"%s read head anti-submarine") % door_name_1)
            if self.apb_rule==32:
                result.append(_(u"%s read head anti-submarine") % door_name_2)
            if self.apb_rule==64:
                result.append(_(u"%s read head anti-submarine") % door_name_3)
            if self.apb_rule==128:
                result.append(_(u"%s read head anti-submarine") % door_name_4)

        if self.apb_rule==1001:
            return _(u"Group 1 and Group 2's door anti-submarine")
        if self.apb_rule==1002:
            return _(u"Group 1 and Group 2's reader anti-submarine")

        return result and u','.join(result) or _(u'No anti-submarine setting information')

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'device','width':200,'index':'device','label':u"%s"%(_(u'device name'))},
                {'name':'apb_rule','sortable':False,'width':400,'align':'center','label':u"%s"%(AntiPassBack._meta.get_field('apb_rule').verbose_name)},
                {'name':'detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
                ]

        # if interlock_rule==1:
        #     result.append()
        # if self.device.LockFunOn == 4:
        #     door_name_3 = u'%s(%s)' % (names[2][1], names[2][0])
        #     door_name_4 = u'%s(%s)' % (names[3][1], names[3][0])
        #
        # if interlock_rule==2:
        #     result.append(_(u"%(a)s 与 %(b)s 互锁") % {"a": door_name_3, "b": door_name_4})
        # if interlock_rule==3:
        #     result.append()
        # if interlock_rule==4:
        #     result.append()
        # return result and ','.join(result) or _(u'No interlock setting information')

def getInterLockInfo(device):
    result = []
    if device.Style in ['30']:  # 1U项目 INBIO P3000系列
        d={}
        d['id']=0
        d['name']='--------------------------'
        result.append(d.copy())
        d['id'] = 11
        d['name'] = u"%s" % (_(u"intra-group interlock"))
        result.append(d.copy())
        d['id'] = 12
        d['name'] = u"%s" % (_(u"interlock between group 1 and group 2"))
        result.append(d.copy())
        return result
    #names=AccDoor.objects.filter(device=device).order_by('id').values_list('door_no','door_name')
    door_name_1 = 1#u'%s(%s)' % (names[0][1], names[0][0])
    door_name_2 = 2#u'%s(%s)' % (names[1][1], names[1][0])
    d={}
    d['id']=0
    d['name']='--------------------------'
    result.append(d.copy())
    if device.LockFunOn == 2:
        d['id']=1
        d['name']=_(u"%(a)s - %(b)s two-door interlock") % {"a": door_name_1, "b": door_name_2}
        result.append(d.copy())
    elif device.LockFunOn == 4:
        d['id']=1
        d['name']=_(u"%(a)s - %(b)s two-door interlock") % {"a": door_name_1, "b": door_name_2}
        result.append(d.copy())
        d['id']=2
        d['name']=_(u"%(a)s - %(b)s two-door interlock") % {"a": 3, "b": 4}
        result.append(d.copy())
        d['id']=3
        d['name']=_(u"%(a)s - %(b)s - %(c)s three-door interlock") % {"a": door_name_1, "b": door_name_2, "c": 3}
        result.append(d.copy())
        d['id']=4
        d['name']=_(u"%(a)s - %(b)s two-door interlock, %(c)s - %(d)s two-door interlock") % {"a": door_name_1, "b": door_name_2, "c": 3,"d": 4}
        result.append(d.copy())


        d['id']=5
        d['name']=_(u"%(a)s - %(b)s - %(c)s - %(d)s interlock") % {"a": door_name_1, "b": door_name_2, "c": 3, "d": 4}
        result.append(d.copy())
    return result


def getAntiPassBackInfo(device):
    result = []
    if device.Style in ['30']:  # 1U项目 INBIO P3000系列
        d={}
        d['rule']=0
        d['name']='--------------------------'
        result.append(d.copy())
        d['rule'] = 1001
        d['name'] = u"%s" % (_(u"door anti-submarine"))
        result.append(d.copy())
        d['rule'] = 1002
        d['name'] = u"%s" % (_(u"reader anti-submarine"))
        result.append(d.copy())
        return result

    names=AccDoor.objects.filter(device=device).order_by('id').values_list('door_no','door_name')
    door_name_1 = u'%s(%s)' % (names[0][1], names[0][0])
    reader_Count=1
    d={}
    d['rule']=0
    d['name']='--------------------------'
    result.append(d.copy())
    #if device.LockFunOn == 2:
    obj=device_options.objects.filter(SN=device,ParaName='ReaderCount')
    if obj:
        reader_Count=int(obj[0].ParaValue)
    if device.ProductType == 25:
        #专业门禁一体机反潜
        # AntiPassback参数：
        # 0：无
        # 1：出反潜
        # 2：入反潜
        # 3：出入反潜
        # 4：无且保存记录
        d['rule']=1
        d['name']=u'%s'%_(u'antiback-out')
        result.append(d.copy())

        d['rule']=2
        d['name']=u'%s'%_(u'antiback-in')
        result.append(d.copy())

        d['rule']=3
        d['name']=u'%s'%_(u'antiback-inout')
        result.append(d.copy())

        d['rule']=4
        d['name']=u'%s'%_(u'antiback-no&save record')
        result.append(d.copy())

    elif device.LockFunOn == 1:#单门双向
        d['rule']=1
        d['name']=_(u"%s read head anti-submarine") % door_name_1
        result.append(d.copy())

    elif device.LockFunOn == 2 and reader_Count == 4:#两门双向 含C4-200和C3400-200
        door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
        d['rule']=1
        d['name']=_(u"%s read head anti-submarine") % door_name_1
        result.append(d.copy())

        d['rule']=2
        d['name']=_(u"%s read head anti-submarine") % door_name_2
        result.append(d.copy())

        d['rule']=3
        d['name']=_(u"%s read head anti-submarine") % door_name_1+','+_(u"%s read head anti-submarine") % door_name_2
        result.append(d.copy())

        d['rule']=4
        d['name']=_(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2}
        result.append(d.copy())

    elif device.LockFunOn == 2 and reader_Count == 2:#两门单向
        door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
        d['rule']=4
        d['name']=_(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2}
        result.append(d.copy())

    elif device.LockFunOn == 4 and reader_Count == 4:  # C3-400
        door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
        door_name_3 = u'%s(%s)' % (names[2][1], names[2][0])
        door_name_4 = u'%s(%s)' % (names[3][1], names[3][0])

        d['rule'] = 1
        d['name'] = _(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2}
        result.append(d.copy())

        d['rule'] = 2
        d['name'] = _(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_3, "b": door_name_4}
        result.append(d.copy())

        d['rule'] = 3
        d['name'] = _(u"%(a)s with %(b)s or %(c)s and %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                                "c": door_name_3, "d": door_name_4}
        result.append(d.copy())

        d['rule'] = 4
        d['name'] = _(u"%(a)s or %(b)s and %(c)s or %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                              "c": door_name_3, "d": door_name_4}
        result.append(d.copy())

        d['rule'] = 5
        d['name'] = _(u"%(a)s with %(b)s or %(c)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                      "c": door_name_3}
        result.append(d.copy())

        d['rule'] = 6
        d['name'] = _(u"%(a)s with %(b)s or %(c)s or %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                               "c": door_name_3, "d": door_name_4}
        result.append(d.copy())

        d['rule'] = 16
        d['name'] = _(u"%s read head anti-submarine") % door_name_1
        result.append(d.copy())

        d['rule'] = 32
        d['name'] = _(u"%s read head anti-submarine") % door_name_2
        result.append(d.copy())

        d['rule'] = 64
        d['name'] = _(u"%s read head anti-submarine") % door_name_3
        result.append(d.copy())

        d['rule'] = 128
        d['name'] = _(u"%s read head anti-submarine") % door_name_4
        result.append(d.copy())
    elif device.LockFunOn == 4 and reader_Count == 8:  #inbio540
        door_name_2 = u'%s(%s)' % (names[1][1], names[1][0])
        door_name_3 = u'%s(%s)' % (names[2][1], names[2][0])
        door_name_4 = u'%s(%s)' % (names[3][1], names[3][0])

        d['rule'] = 1
        d['name'] = _(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_1, "b": door_name_2}
        result.append(d.copy())

        d['rule'] = 2
        d['name'] = _(u"%(a)s and %(b)s anti-submarine") % {"a": door_name_3, "b": door_name_4}
        result.append(d.copy())

        d['rule'] = 3
        d['name'] = _(u"%(a)s with %(b)s or %(c)s and %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                                "c": door_name_3, "d": door_name_4}
        result.append(d.copy())

        d['rule'] = 4
        d['name'] = _(u"%(a)s or %(b)s and %(c)s or %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,"c": door_name_3, "d": door_name_4}
        result.append(d.copy())

        d['rule'] = 5
        d['name'] = _(u"%(a)s with %(b)s or %(c)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                      "c": door_name_3}
        result.append(d.copy())

        d['rule'] = 6
        d['name'] = _(u"%(a)s with %(b)s or %(c)s or %(d)s anti-submarine") % {"a": door_name_1, "b": door_name_2,
                                                                               "c": door_name_3, "d": door_name_4}
        result.append(d.copy())

        d['rule'] = 16
        d['name'] = _(u"%s read head anti-submarine") % door_name_1
        result.append(d.copy())

        d['rule'] = 32
        d['name'] = _(u"%s read head anti-submarine") % door_name_2
        result.append(d.copy())

        d['rule'] = 64
        d['name'] = _(u"%s read head anti-submarine") % door_name_3
        result.append(d.copy())

        d['rule'] = 128
        d['name'] = _(u"%s read head anti-submarine") % door_name_4
        result.append(d.copy())

    return result

class searchs(models.Model):
    SN = models.CharField(_('serial number'), max_length=20,null=False,db_column='sn')
    Alias = models.CharField(_('Device Alias Name'),max_length=20,null=True, blank=True,db_column='alias')
    State = models.IntegerField(_(u'status'),default=1, editable=False,db_column='state')
    Protype = models.CharField(max_length=10, null=True, blank=True, editable=False,help_text=_(u'Communication Type'),db_column='protype')
    MAC = models.CharField(_('MAC'),max_length=20, null=True, blank=True,editable=False,db_column='mac')
    IPAddress = models.CharField(_('IPAddress'),max_length=20, null=True, blank=True,editable=True,db_column='ipaddress')
    NetMask = models.CharField(_('NetMask'),max_length=20, null=True, blank=True,editable=True,db_column='netmask')
    GATEIPAddress = models.CharField(_('GATEIPAddress'),max_length=20, null=True, blank=True,editable=True,db_column='gateipaddress')
    DeviceName = models.CharField(_('Device Name'),max_length=30, null=True, blank=True,editable=False,db_column='devicename')
    FWVersion = models.CharField(_('FW Version'),max_length=50, null=True, blank=True,editable=False,db_column='fwversion')
    WebServerIP = models.CharField(_('WebServerIP'),max_length=20, null=True, blank=True,editable=True,db_column='webserverip')
    WebServerURL = models.CharField(_('WebServerURL'),max_length=100, null=True, blank=True,editable=True,db_column='webserverurl')
    WebServerPort = models.CharField(_('WebServerPort'),max_length=20, null=True, blank=True,editable=True,db_column='webserverport')
    IsSupportSSL = models.IntegerField(_('IsSupportSSL'), null=True, blank=True,editable=False,default=0,db_column='issupportssl')
    DNSFunOn = models.IntegerField(_('DNSFunOn'), null=True, blank=True,editable=False,default=0,db_column='dnsfunon')
    DNS = models.CharField(_('WebServerURL'),max_length=100, null=True, blank=True,editable=True,db_column='dns')
    OpStamp = models.DateTimeField(null=True, blank=True,editable=False,db_column='opstamp')
    Style = models.CharField(_('style'),max_length=20, null=True, blank=True, default="", editable=False,db_column='style')#门禁中表示不同设备，见文件开头定义
    isAdd = models.IntegerField( null=True, blank=True,editable=False,default=0,db_column='isadd')
    signature = models.CharField(max_length=100,null=True, blank=True,db_column='signature')  #标识设备是否允许空密码
    Reserved = models.CharField(max_length=210,null=True, blank=True,db_column='reserved')

    @staticmethod
    def colModels():
        return [{'name':'id','hidden':True},
                {'name':'SN','width':120,'label':u"%s"%(_(u'serial number')),'frozen':True},
                {'name':'DeviceName','width':100,'label':u"%s"%(_('Device Name'))},
                #{'name':'Alias','sortable':False,'width':100,'label':u"%s"%(_(u'设备别名'))},
#                {'name':'MAC','sortable':False,'width':100,'label':u"%s"%(_(u'MAC地址'))},
                {'name':'IPAddress','width':90,'editable':True,'label':u"%s"%(_('IPAddress'))},
                {'name':'NetMask','sortable':False,'editable':True,'width':90,'label':u"%s"%(_(u'subnet mask'))},
                {'name':'GATEIPAddress','width':90,'editable':True,'label':u"%s"%(_(u'gateway'))},
                {'name':'WebServerIP','width':100,'editable':True,'label':u"%s"%(_(u'server IP'))},
                {'name':'WebServerPort','width':50,'editable':True,'label':u"%s"%(_(u'The port number'))},
                {'name':'COMKey','width':60,'editable':True,'label':u"%s"%(_(u'Connection password'))},
                {'name':'signature','width':60,'editable':True,'hidden':True},
                {'name':'OP','width':180,'sortable':False,'label':u"%s"%(_(u'operating'))},

                {'name':'WebServerURL','width':100,'label':u"%s"%(_(u'server URL'))},
                
                {'name':'FWVersion','width':120,'label':u"%s"%(_('FW Version'))},
#                {'name':'State','sortable':False,'index':'State','width':60,'label':u"%s"%(_(u'status'))},
                #{'name':'IsSupportSSL','width':60,'label':u"%s"%(_(u'支持SSL'))},
                {'name':'Protype','sortable':False,'width':80,'label':u"%s"%(_(u'Communication Type'))},
                {'name':'DNSFunOn','width':60,'label':u"%s"%(_(u'Open DNS'))},
                {'name':'isAdd','width':60,'label':u"%s"%(_(u'added'))},
                {'name':'DNS','width':100,'label':u"%s"%(_(u'DNS'))},
                {'name':'Reserved','width':500,'label':u"%s"%(_(u'Remarks'))},
                ]

    class Admin:
        list_display=()
        search_fields = ['SN']

    class Meta:
        verbose_name=_(u'equipment')
        verbose_name_plural=_('device')
        unique_together = (('SN'),)
    def __unicode__(self):
        return u"%s"%(self.SN)
    def __str__(self):
        return u"%s"%(self.SN)
    def getDynState(self):
        from mysite.iclock.models import DEV_STATUS_OFFLINE,DEV_STATUS_OK,DEV_STATUS_PAUSE
        try:
            if self.State==1:
                return DEV_STATUS_OK
            aObj=getDevice(self.SN)#cache.get("iclock_"+self.SN)
            if aObj:
                return aObj.getDynState()
            else:
                return DEV_STATUS_PAUSE

            if aObj and not aObj.LastActivity: return DEV_STATUS_OFFLINE
            if aObj and not self.LastActivity:self.LastActivity=aObj.LastActivity
            if aObj  and aObj.LastActivity>self.LastActivity:
                self.LastActivity=aObj.LastActivity
#               if self.SN in ["521463"]: print "LastActivity", self.LastActivity
            d=datetime.datetime.now()-self.LastActivity
            if d>datetime.timedelta(0,settings.MAX_DEVICES_STATE):
                return DEV_STATUS_OFFLINE
            else:
                #if len(deviceCmd(self))==0:
                if aObj and aObj.State!=2:
                    return DEV_STATUS_OK


            #if len(deviceCmd(self))>0:#if devcmds.objects.filter(SN=self,CmdOverTime__isnull=True).count()>0:
            if aObj and aObj.State==2:
                return DEV_STATUS_TRANS

            return DEV_STATUS_OK
        except:
            if self.State==1:
                return DEV_STATUS_OK
            return DEV_STATUS_OFFLINE

class empZone(models.Model):
    UserID = models.ForeignKey(employee,db_column='userid', on_delete=models.CASCADE)
    zone = models.ForeignKey(zone, on_delete=models.CASCADE)

    def __unicode__(self):
        return u"%s" % (self.UserID.__unicode__())
    def __str__(self):
        return u"%s" % (self.UserID.__str__())
    def Dept(self):
        return department.objByID(self.UserID.DeptID_id)

    def getZone(self):
        r = ""
        j = 0
        zoneids = empZone.objects.filter(UserID=self.UserID_id).values_list('zone_id', flat=True).order_by("zone")
        for t in zoneids:
            if j > 5:
                r += "..."
                break
            obj = zone.objByID(t)
            r += u'%s'%obj.name+","
            j += 1
        return r[:-1]

    def employee(self):
        return employee.objByID(self.UserID_id)

    @staticmethod
    def colModels():
        return [
            {'name': 'id', 'hidden': True},
            {'name': 'ZoneCode', 'index': 'zone__code', 'width': 100, 'label': u"%s"%(_('area number'))},
            {'name': 'ZoneName', 'index': 'zone__name', 'width': 100, 'label': u"%s"%(_('AreaName'))},
            {'name': 'PIN', 'index': 'UserID__PIN', 'width': 100, 'label': u"%s"%(_('PIN'))},
            {'name': 'Name', 'sortable': False, 'width': 100, 'align': 'center', 'label': u"%s"%(_(u'EName'))},
            {'name': 'DeptNumber', 'sortable': False, 'width': 100, 'label': u"%s"%(_(u'Department Number'))},
            {'name': 'DeptName', 'sortable': False, 'width': 200, 'label': u"%s"%(_(u'DeptName'))}
        ]

    def save(self):
        cache.delete("%s_iclock_emp_zoneids_%s"%(settings.UNIT, self.UserID_id))
        super(empZone, self).save()

    class Admin:
        list_display=()
        search_fields = ['UserID__PIN', 'UserID__EName']

    class Meta:
        db_table = 'empzone'
        verbose_name = _(u'EmpZone')
        verbose_name_plural = verbose_name
        unique_together = (("UserID", "zone"),)
        default_permissions = ('browse', 'add', 'change', 'delete', 'export')

#APP签到可设置多个区域
class ZoneLocation(models.Model):
    location = models.ForeignKey(applocation,  null=False, blank=False, on_delete=models.CASCADE)
    zone = models.ForeignKey(zone, null=False, blank=False, on_delete=models.CASCADE)
    class Admin:
        list_display = ("address", "zone",)

    class Meta:
        db_table = 'app_locationzone'
        verbose_name = _("zonelocation")
        verbose_name_plural = verbose_name

