#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import string
from django.contrib import auth
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.template import loader, Context, RequestContext
from django.conf import settings
from django.contrib.auth.decorators import permission_required, login_required
from mysite.iclock.models import *
from mysite.acc.models import *
#from mysite.iclock.datasproc import *
from django.core.paginator import Paginator
#from mysite.iclock.datas import *
#from mysite.core.menu import *
from mysite.core.zktools import *
from mysite.iclock.models import *

import sys
from mysite.core.zkcmdproc import *


@login_required
def index(request):
    if request.method=='GET':
        tmpFile='acc/'+'combopen.html'
        tmpFile=request.GET.get('t', tmpFile)
        cc={}


        settings.PAGE_LIMIT=GetParamValue('opt_users_page_limit','30',str(request.user.id))
        limit= int(request.GET.get('l', settings.PAGE_LIMIT))
        colmodels_level=combopen_emp.colModels()
        colmodels= combopen.colModels()


        cc['limit']=limit
        cc['colModel']=dumps1(colmodels)
        cc['colModel_level']=dumps1(colmodels_level)
        request.model=level


        return render(request,tmpFile,cc)#render(tmpFile,cc,RequestContext(request, {}))



def get_combopen_emp(request):
    #print request.POST
    id=request.GET.get('id')
    limit= int(request.POST.get('rows', 0))
    sidx=request.POST.get('sidx')
    sord=request.POST.get('sord')
    try:
        offset = int(request.POST.get('page', 1))
    except:
        offset=1
    if sord=='desc':
        objs=combopen_emp.objects.filter(combopen=id).exclude(UserID__DelTag=1).order_by('-'+sidx)
    else:
        objs=combopen_emp.objects.filter(combopen=id).exclude(UserID__DelTag=1).order_by(sidx)
    p=Paginator(objs, limit)
    iCount=p.count
    if iCount<(offset-1)*limit:
        offset=1
    page_count=p.num_pages
    pp=p.page(offset)
    objs=pp.object_list
    re=[]
    Result={}
    Result['datas']=re
    for t in objs:
        d={}
        d['id']=t.id
        d['PIN']=t.UserID.PIN
        d['EName']=t.UserID.EName
        d['DeptName']=t.UserID.Dept().DeptName


        re.append(d.copy())
    if offset>page_count:offset=page_count
    item_count =iCount
    Result['item_count']=item_count
    Result['page']=offset
    Result['limit']=limit
    Result['from']=(offset-1)*limit+1
    Result['page_count']=page_count
    Result['datas']=re
    rs="{"+""""page":"""+str(Result['page'])+","+""""total":"""+str(Result['page_count'])+","+""""records":"""+str(Result['item_count'])+","+""""rows":"""+dumps(Result['datas'])+"""}"""
    return getJSResponse(rs)

@login_required	
def SaveEmployee_combopen(request):
    from mysite.acc.accviews import sync_multiPerson
    ID=request.GET.get('id','')
    deptIDs=request.POST.get('deptIDs',"")
    userIDs=request.POST.get('UserIDs',"")
    isContainedChild=request.POST.get('isContainChild',"")
    if ID=='':
        return getJSResponse({"ret":1,"message":u"%s"%_('Save failed')})
    #ID=ID.split(',')
    if userIDs == "":
        isAllDept=False
        deptidlist=deptIDs.split(',')
        deptids=deptidlist
        if isContainedChild=="1":
            deptids,isAllDept=getContainedDept(request, deptidlist)

        if isAllDept:
            emps=employee.objects.all().exclude(OffDuty=1).exclude(DelTag=1)
        else:
            emps=employee.objects.filter(DeptID__in=deptids).exclude(OffDuty=1).exclude(DelTag=1)
    else:
        userids=userIDs.split(',')
        emps=employee.objects.filter(pk__in=userids)
    emplist = list(emps.values_list('id', flat=True))
    combhasemplist = list(combopen_emp.objects.filter(UserID__in=emplist).values_list('UserID', flat=True))
    combhasemp = list(emps.filter(pk__in=combhasemplist).values_list('PIN', flat=True))

    # this_group_count=acc_employee.objects.filter(morecard_group=ID).count()
    # if this_group_count+len(emps)>=10:
    #     return getJSResponse({"ret": 1, "message": u"%s" % _('Multiple Openers should not be more than 10,please recheck')})

    for emp in emps:
        try:
            combopen_emp.objects.get(UserID=emp)
            continue
        except:
            sql,params=getSQL_insert_new(combopen_emp._meta.db_table,combopen_id=ID,UserID_id=emp.id)
            try:
                customSql(sql,params)
            except Exception as e:
                #print (e)
                pass
        try:
            tmp=acc_employee.objByUserID(emp.id)
            tmp.morecard_group_id=ID
            tmp.save()
            emp_acc=employee.objects.get(id = tmp.UserID_id)
            emp_acc=u'%s'%emp_acc
            adminLog(time=datetime.datetime.now(),User_id=request.user.id,object=emp_acc, model=combopen_emp._meta.verbose_name, action=_(u"Add emps")).save(force_insert=True)#
        except:
            acc_employee(UserID=emp,morecard_group_id=ID).save()
    doors=combopen_comb.objects.filter(combopen=ID).distinct().values_list('combopen_door__door',flat=True)
    snlist=list(AccDoor.objects.filter(id__in=doors).distinct().values_list('device_id',flat=True))

    if snlist:
        sync_multiPerson(snlist)
    if combhasemp:
        return getJSResponse({"ret": 1, "message": _(u'Number: %s already exists') %(', '.join(combhasemp[:10]))})
    return getJSResponse({"ret":0,"message":u"%s"%_('Operation Success')})

