## 会议模块
### 会议模块-实时数据功能点会议实时显示
#### 会议模块实时数据功能点RAG检索锚点
<!-- RAG检索锚点: 会议实时显示、实时数据、会议监控、实时状态、会议进度 -->

#### 会议实时显示功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[加载实时数据界面]
    D --> E[获取当前会议列表]
    E --> F[查询会议状态]
    F --> G[获取参会人员签到情况]
    G --> H[统计会议数据]
    H --> I[显示实时信息]
    I --> J[设置自动刷新]
    J --> K{用户操作}
    K -->|刷新数据| L[重新获取数据]
    K -->|查看详情| M[显示会议详情]
    K -->|导出数据| N[生成报表]
    K -->|退出监控| O[结束监控]
    L --> F
    M --> P[返回实时界面]
    N --> Q[下载报表文件]
    P --> I
    Q --> I
    C --> R[结束]
    O --> R
```
#### 会议实时显示功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动会议实时显示业务流程。
2. 权限验证：验证当前用户是否具有实时监控权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行实时监控操作
3. 实时数据界面加载：
   - 显示会议实时监控界面
   - 初始化数据显示区域
   - 设置界面布局和控件
4. 当前会议列表获取：
   - 从Meet表中查询当前时间段的会议
   - 过滤已删除的会议记录（DelTag=0）
   - 按照会议开始时间排序显示
5. 会议状态查询：
   - 判断会议的当前状态（未开始、进行中、已结束）
   - 根据当前时间与会议开始结束时间比较
   - 显示会议的进度百分比
6. 参会人员签到情况获取：
   - 查询Meet_details表获取应参会人员列表
   - 统计已签到人员数量（present字段）
   - 统计迟到人员数量（late字段）
   - 统计缺席人员数量（absent字段）
   - 统计早退人员数量（early字段）
7. 会议数据统计：
   - 计算会议的总体参与率
   - 统计各会议室的使用情况
   - 分析会议的时间分布
   - 生成实时统计图表
8. 实时信息显示：
   - 显示会议列表和基本信息
   - 展示参会人员签到统计
   - 显示会议室使用状态
   - 展示实时数据图表
9. 自动刷新设置：
   - 设置定时刷新机制（如每30秒刷新一次）
   - 自动更新会议状态和签到数据
   - 保持数据的实时性和准确性
10. 用户操作处理：
    - 手动刷新：立即更新所有实时数据
    - 查看详情：显示特定会议的详细信息
    - 导出数据：生成当前数据的报表文件
    - 退出监控：关闭实时监控界面
11. 数据刷新：
    - 重新查询数据库获取最新数据
    - 更新界面显示的所有信息
    - 刷新统计图表和数据
12. 会议详情显示：
    - 显示选定会议的详细信息
    - 展示参会人员的具体签到情况
    - 显示会议的进行状态和时间信息
13. 报表生成：
    - 根据当前显示的数据生成报表
    - 支持多种格式的报表导出
    - 提供报表下载功能
14. 流程完成：根据用户操作完成相应的功能流程。

#### 会议实时显示功能点与其他模块及子模块业务关联说明
1. **与会议安排模块的关联**：
   - 实时显示的会议数据来源于会议安排
   - 会议状态的变化影响实时显示内容
   - 会议信息的修改需要实时反映

2. **与参会人员模块的关联**：
   - 实时显示需要获取参会人员信息
   - 签到状态的变化需要实时更新
   - 参会人员的变更影响统计数据

3. **与设备管理模块的关联**：
   - 签到数据来源于会议室的考勤设备
   - 设备状态影响数据的准确性
   - 设备故障可能影响实时数据获取

4. **与报表系统的关联**：
   - 实时数据可以导出为各种报表
   - 报表模板影响数据的展示格式
   - 实时数据为报表提供数据源

5. **与权限管理模块的关联**：
   - 实时监控功能需要相应权限
   - 不同用户可查看的数据范围不同
   - 敏感数据需要权限控制访问

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 会议模块
### 会议模块-实时数据功能点刷新数据
#### 会议模块实时数据功能点RAG检索锚点
<!-- RAG检索锚点: 刷新数据、数据更新、实时刷新、数据同步、状态更新 -->

#### 刷新数据功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[触发刷新操作]
    D --> E[清理缓存数据]
    E --> F[重新查询会议数据]
    F --> G[更新会议状态]
    G --> H[刷新签到统计]
    H --> I[重新计算统计数据]
    I --> J[更新界面显示]
    J --> K[刷新图表数据]
    K --> L[更新时间戳]
    L --> M[记录刷新日志]
    M --> N[返回刷新结果]
    C --> O[结束]
    N --> O
```
#### 刷新数据功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动数据刷新业务流程。
2. 权限验证：验证当前用户是否具有数据刷新权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行数据刷新操作
3. 刷新操作触发：
   - 响应用户的手动刷新请求
   - 或者由系统定时刷新机制触发
   - 记录刷新操作的触发时间和方式
4. 缓存数据清理：
   - 清理过期的缓存数据
   - 释放内存中的临时数据
   - 确保获取最新的数据库数据
5. 会议数据重新查询：
   - 从Meet表中重新查询当前会议列表
   - 获取最新的会议信息和配置
   - 过滤无效和已删除的会议记录
6. 会议状态更新：
   - 根据当前时间重新计算会议状态
   - 更新会议的进行状态（未开始、进行中、已结束）
   - 计算会议的剩余时间和进度
7. 签到统计刷新：
   - 重新统计参会人员的签到情况
   - 更新已签到、迟到、缺席、早退人数
   - 计算最新的参与率和出勤率
8. 统计数据重新计算：
   - 重新计算会议室使用率
   - 更新会议时间分布统计
   - 刷新各类汇总数据和指标
9. 界面显示更新：
   - 更新会议列表的显示内容
   - 刷新统计数据的显示
   - 更新状态指示器和进度条
10. 图表数据刷新：
    - 更新实时统计图表的数据
    - 刷新趋势图和分布图
    - 重新渲染可视化组件
11. 时间戳更新：
    - 更新数据的最后刷新时间
    - 显示数据的时效性信息
    - 记录刷新操作的完成时间
12. 刷新日志记录：
    - 记录数据刷新操作的详细日志
    - 包括刷新时间、数据量、耗时等信息
    - 用于系统监控和性能分析
13. 刷新结果返回：
    - 返回刷新操作的执行结果
    - 提示用户数据已更新
    - 显示刷新完成的时间信息

#### 刷新数据功能点与其他模块及子模块业务关联说明
1. **与数据库管理模块的关联**：
   - 数据刷新需要重新查询数据库
   - 数据库性能影响刷新速度
   - 数据库连接状态影响刷新成功率

2. **与缓存管理模块的关联**：
   - 刷新操作需要清理相关缓存
   - 缓存策略影响数据的实时性
   - 缓存失效机制影响刷新频率

3. **与系统监控模块的关联**：
   - 刷新操作需要记录系统日志
   - 刷新频率影响系统性能
   - 刷新失败需要触发告警

4. **与用户界面模块的关联**：
   - 刷新结果需要更新界面显示
   - 界面响应速度影响用户体验
   - 刷新状态需要给用户反馈

5. **与权限管理模块的关联**：
   - 数据刷新权限控制操作范围
   - 不同用户的刷新权限不同
   - 敏感数据的刷新需要特殊权限

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
