# -*- coding: utf-8 -*-
# Generated by Django 1.11.10 on 2018-03-10 13:37
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('acc', '0009_auto_20180306_1428'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='accdoor',
            name='door_sensor_status',
            field=models.IntegerField(choices=[(0, '\u65e0'), (1, '\u5e38\u5f00'), (2, '\u5e38\u95ed')], default=0, help_text='\u95e8\u78c1\u7c7b\u578b\u4e3a\u65e0\u65f6\uff0c\u95e8\u78c1\u5ef6\u65f6\u4e0d\u53ef\u7f16\u8f91', null=True, verbose_name='\u95e8\u78c1\u7c7b\u578b'),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='records',
            name='verify',
            field=models.Integer<PERSON>ield(choices=[(0, '\u81ea\u52a8\u8bc6\u522b(\u5361\u6216\u5bc6\u7801\u6216\u6307\u7eb9\u6216\u6307\u9759\u8109)'), (1, '\u4ec5\u6307\u7eb9'), (2, '\u4ec5\u5de5\u53f7'), (3, '\u4ec5\u5bc6\u7801'), (4, '\u4ec5\u5361'), (5, '\u6307\u7eb9\u6216\u5bc6\u7801'), (6, '\u5361\u6216\u6307\u7eb9'), (7, '\u5361\u6216\u5bc6\u7801'), (8, '\u5de5\u53f7\u52a0\u6307\u7eb9'), (9, '\u6307\u7eb9\u52a0\u5bc6\u7801'), (10, '\u5361\u52a0\u6307\u7eb9'), (11, '\u5361\u52a0\u5bc6\u7801'), (12, '\u6307\u7eb9\u52a0\u5bc6\u7801\u52a0\u5361'), (13, '\u5de5\u53f7\u52a0\u6307\u7eb9\u52a0\u5bc6\u7801'), (14, '\u5de5\u53f7\u52a0\u6307\u7eb9\u6216\u5361\u52a0\u6307\u7eb9'), (15, '\u4eba\u8138'), (16, '\u4eba\u8138\u52a0\u6307\u7eb9'), (17, '\u4eba\u8138\u52a0\u5bc6\u7801'), (18, '\u4eba\u8138\u52a0\u5361'), (19, '\u4eba\u8138\u52a0\u6307\u7eb9\u52a0\u5361'), (20, '\u4eba\u8138\u52a0\u6307\u7eb9\u52a0\u5bc6\u7801'), (21, '\u6307\u9759\u8109'), (22, '\u6307\u9759\u8109\u52a0\u5bc6\u7801'), (23, '\u6307\u9759\u8109\u52a0\u5361'), (24, '\u6307\u9759\u8109\u52a0\u5361\u52a0\u5bc6\u7801'), (200, '\u5176\u4ed6')], default=200, null=True, verbose_name='verification'),
        ),
    ]
