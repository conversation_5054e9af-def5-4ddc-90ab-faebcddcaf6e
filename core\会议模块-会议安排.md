## 会议模块
### 会议模块-会议安排功能点新增会议安排
#### 会议模块会议安排功能点RAG检索锚点
<!-- RAG检索锚点: 新增会议安排、会议安排、会议创建、会议管理、会议组织 -->

#### 新增会议安排功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示安排表单]
    D --> E[填写会议基本信息]
    E --> F[选择会议室]
    F --> G[设置会议时间]
    G --> H[配置签到时间]
    H --> I[添加参会人员]
    I --> J[设置会议内容]
    J --> K[验证安排数据]
    K --> L{数据验证}
    L -->|验证失败| M[显示错误信息]
    L -->|验证通过| N[检查时间冲突]
    N --> O{是否有冲突}
    O -->|有冲突| P[提示时间冲突]
    O -->|无冲突| Q[检查会议室状态]
    Q --> R{会议室是否可用}
    R -->|不可用| S[提示会议室不可用]
    R -->|可用| T[保存会议安排]
    T --> U[同步到设备]
    U --> V[发送会议通知]
    V --> W[记录操作日志]
    W --> X[返回成功信息]
    M --> E
    P --> G
    S --> F
    C --> Y[结束]
    X --> Y
```
#### 新增会议安排功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增会议安排业务流程。
2. 权限验证：验证当前用户是否具有会议安排权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行会议安排操作
3. 安排表单显示：
   - 显示会议安排表单界面
   - 提供必填和可选字段的输入控件
   - 显示会议安排规则和注意事项
4. 会议基本信息填写：
   - 会议编号（MeetID）：系统自动生成或手动输入，必须唯一
   - 会议标题（conferenceTitle）：必填，会议的主题名称
   - 会议内容（MeetContents）：可选，会议的详细内容描述
   - 会议类型：设置会议的重要程度和分类
   - 组织者信息：自动获取当前登录用户信息
5. 会议室选择：
   - 从可用会议室列表中选择（LocationID字段）
   - 显示会议室的基本信息、容量和设施
   - 检查会议室的当前状态和可用性
   - 确保会议室未被暂停使用（State≠2）
6. 会议时间设置：
   - 会议开始时间（Starttime）：必填，会议的开始时间
   - 会议结束时间（Endtime）：必填，会议的结束时间
   - 验证开始时间必须早于结束时间
   - 确保会议时间在合理的工作时间范围内
7. 签到时间配置：
   - 签到开始时间（Enrolmenttime）：参会人员可以开始签到的时间
   - 签到结束时间（LastEnrolmenttime）：签到截止时间
   - 早退签离时间（EarlySignOfftime）：允许早退的最早时间
   - 最晚签离时间（LastSignOfftime）：必须签离的最晚时间
8. 参会人员添加：
   - 选择参会人员列表
   - 支持按部门批量添加人员
   - 支持使用预定义的参会人员模板
   - 可以设置参会人员的角色和权限
9. 会议内容设置：
   - 详细描述会议的议题和目标
   - 设置会议的重要程度和优先级
   - 添加会议的特殊要求和注意事项
   - 配置会议的保密级别
10. 安排数据验证：
    - 验证必填字段是否完整
    - 检查时间格式和逻辑的正确性
    - 验证参会人员的有效性
    - 确保会议编号的唯一性
11. 时间冲突检查：
    - 检查选定时间段是否与现有会议冲突
    - 通过Meet表查询时间重叠的会议
    - 确保同一会议室同一时间只能有一个会议
    - 排除已删除的会议记录（DelTag=0）
12. 会议室状态检查：
    - 验证会议室是否处于可用状态
    - 检查会议室是否被暂停使用
    - 确保会议室设备正常可用
13. 会议安排保存：
    - 将会议信息保存到Meet表
    - 将参会人员信息保存到Meet_details表
    - 设置会议状态和相关统计信息
    - 生成唯一的会议标识
14. 设备同步：
    - 将会议信息同步到会议室的显示设备
    - 通过updateMeetAdDev函数同步会议数据
    - 向信息屏设备发送会议信息显示命令
    - 配置考勤设备的会议签到功能
15. 会议通知发送：
    - 向参会人员发送会议通知
    - 通知会议室管理员会议安排
    - 发送会议提醒和准备事项
16. 操作日志记录：记录会议安排操作的详细日志。
17. 流程完成：返回安排成功信息，完成会议安排流程。

#### 新增会议安排功能点与其他模块及子模块业务关联说明
1. **与会议室管理模块的关联**：
   - 会议安排需要选择具体的会议室
   - 会议室状态影响安排的可用性
   - 会议室设施信息影响会议形式选择

2. **与设备管理模块的关联**：
   - 会议安排成功后需要配置会议室设备
   - 会议信息需要同步到会议室显示设备
   - 设备状态影响会议的正常进行

3. **与参会人员模块的关联**：
   - 会议安排需要添加具体的参会人员
   - 支持使用预定义的参会人员模板
   - 参会人员信息影响会议的规模和形式

4. **与通知系统的关联**：
   - 会议安排后需要通知所有参会人员
   - 会议提醒需要提前发送给相关人员
   - 会议变更需要及时通知相关人员

5. **与考勤管理模块的关联**：
   - 会议安排影响参会人员的考勤记录
   - 会议签到数据用于考勤统计
   - 会议时间影响工作时间的计算

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 会议模块
### 会议模块-会议安排功能点编辑会议安排
#### 会议模块会议安排功能点RAG检索锚点
<!-- RAG检索锚点: 编辑会议安排、修改会议安排、会议安排编辑、会议信息修改 -->

#### 编辑会议安排功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[加载会议信息]
    D --> E[检查会议状态]
    E --> F{会议是否可编辑}
    F -->|不可编辑| G[提示无法编辑]
    F -->|可编辑| H[显示编辑表单]
    H --> I[修改会议信息]
    I --> J[验证修改数据]
    J --> K{数据验证}
    K -->|验证失败| L[显示错误信息]
    K -->|验证通过| M[检查时间冲突]
    M --> N{是否有冲突}
    N -->|有冲突| O[提示时间冲突]
    N -->|无冲突| P[保存修改信息]
    P --> Q[同步设备更新]
    Q --> R[发送变更通知]
    R --> S[记录操作日志]
    S --> T[返回成功信息]
    L --> I
    O --> I
    C --> U[结束]
    G --> U
    T --> U
```
#### 编辑会议安排功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动编辑会议安排业务流程。
2. 权限验证：验证当前用户是否具有会议编辑权限。
   - 权限不足时，提示权限不足并结束流程
   - 只有会议组织者或管理员可以编辑会议
3. 会议信息加载：
   - 从Meet表中加载指定会议的信息
   - 获取会议的当前状态和详细配置
   - 加载参会人员列表和相关设置
4. 会议状态检查：
   - 检查会议的当前状态
   - 验证会议是否可以被编辑
   - 已开始或已结束的会议通常不允许编辑
5. 可编辑性验证：
   - 未开始的会议：允许编辑
   - 正在进行的会议：限制编辑内容
   - 已结束的会议：通常不允许编辑
6. 编辑表单显示：
   - 显示会议的当前信息
   - 提供可编辑的字段界面
   - 标识必填字段和限制编辑的字段
7. 会议信息修改：
   - 允许修改会议标题、内容、时间等信息
   - 支持更换会议室和调整参会人员
   - 可以修改签到时间和会议要求
8. 修改数据验证：
   - 验证修改后的数据格式正确性
   - 检查必填字段是否完整
   - 确保时间逻辑的合理性
9. 时间冲突检查：
   - 检查修改后的时间是否与其他会议冲突
   - 排除当前会议本身的时间占用
   - 确保会议室在新时间段内可用
10. 修改信息保存：
    - 将修改后的信息更新到Meet表
    - 更新参会人员信息到Meet_details表
    - 保持数据的完整性和一致性
11. 设备同步更新：
    - 将修改后的会议信息同步到设备
    - 更新会议室显示设备的会议信息
    - 重新配置考勤设备的签到参数
12. 变更通知发送：
    - 通知参会人员会议信息变更
    - 通知会议室管理员时间或地点调整
    - 更新相关人员的日程安排
13. 操作日志记录：记录会议编辑操作的详细日志。
14. 流程完成：返回编辑成功信息，完成会议编辑流程。

#### 编辑会议安排功能点与其他模块及子模块业务关联说明
1. **与会议室管理模块的关联**：
   - 会议编辑可能涉及会议室的更换
   - 会议室状态变更影响会议的可编辑性
   - 需要检查新会议室的可用性和设施

2. **与设备管理模块的关联**：
   - 会议信息变更需要同步到相关设备
   - 会议室变更需要重新配置设备
   - 设备状态影响会议信息的同步

3. **与参会人员模块的关联**：
   - 会议编辑可能涉及参会人员的调整
   - 需要通知新增和移除的参会人员
   - 参会人员的变更影响会议规模

4. **与通知系统的关联**：
   - 会议变更需要通知所有相关人员
   - 时间变更需要更新日程提醒
   - 重要变更需要发送紧急通知

5. **与考勤管理模块的关联**：
   - 会议时间变更影响考勤记录
   - 签到时间调整影响考勤统计
   - 参会人员变更影响考勤范围

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
