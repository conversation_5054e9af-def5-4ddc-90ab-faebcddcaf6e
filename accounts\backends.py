﻿from __future__ import unicode_literals
import re
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.core.cache import cache
from mysite.iclock.models import *
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from mysite.base.models import GetParamValue
from mysite.utils import aes_decrypt

def create_session_age():
    """根据设置产生动态的sesssion过期时间，对整个django生效"""
    is_session_set = int(GetParamValue('opt_basic_session_restrict','0'))
    if is_session_set and is_session_set!=-1000:
        session_limit_time = int(GetParamValue('opt_basic_session_limit_time'))
        session_unit = GetParamValue('opt_basic_session_unit')
        if session_unit == '1':
            value = 60 * session_limit_time
        elif session_unit == '2':
            value = 60 * 60 * session_limit_time
        else:
            value = 60 * 60 * 24 * session_limit_time
        settings.SESSION_COOKIE_AGE = value
    else:
        settings.SESSION_COOKIE_AGE = 60*60*24*7*2

def online_user(user,sessions):
    """在线用户"""
    uid_list = []
    # online_users = cache.get("%s_%s" % (settings.UNIT, 'online_users')) or []
    # 获取session中的userid
    for session in sessions:
        data = session.get_decoded()
        # 过滤employee人员自助登录
        if data.get('employee',0):
            continue
        uid = data.get('_auth_user_id', 0)
        # 过滤一些异常数据
        if uid:
            uid_list.append(int(uid))

    # 本身当前登录的用户id一起加进去
    if user.pk not in uid_list:
        uid_list.append(user.pk)

    return list(set(uid_list))




def  check_user_limit(user):
    """检测管理员人数是不是超过"""
    # 对于admin不限制登录
    from django.contrib.sessions.models import Session
    user_limit = settings.ONLINE_USER_NUMS
    # 用扩展授权的，以授权数为准
    ext_lic = settings.SALE_EXTENS
    if 'limitation' in ext_lic:
        user_limit = ext_lic['limitation'].get('adminLimit', user_limit)
    # admin登录或限制数为0时，不检测数量
    if user.username == 'admin' or user_limit == 0:
        return
    sessions = Session.objects.filter(expire_date__gte=datetime.datetime.now())
    userlist = online_user(user,sessions)
    # 判断当前在线人员数量,排除同一人不同客户端登录次数累计
    if len(userlist) > user_limit:
        raise Exception(_(u'The number of online administrators exceeded the limit'))









class ModelBackend(object):
    """
    Authenticates against settings.AUTH_USER_MODEL.
    """

    def authenticate(self, request=None, username=None, password=None, **kwargs):
        try:
            username = aes_decrypt(username)
            password = aes_decrypt(password)
        except:
            pass
        UserType='user'
        try:
            UserType=kwargs['UserType']
        except:
            pass
        if UserType in ('user','employee'):
            self.check_account_locked(UserType,username)  #检查账户是否被锁定
        if UserType=='user':
            UserModel = get_user_model()
            verify_type = kwargs.get('verify_type', 0)  #验证类型 0 密码，1，指纹
            try:
                if verify_type == 1:
                    template = kwargs.get('template', '')
                    if not template:
                        return None
                    user = self.get_user_from_template(template)
                    if not user:
                        return None
                else:
                    if username is None:
                        username = kwargs.get(UserModel.USERNAME_FIELD)
                    user = UserModel._default_manager.get_by_natural_key(username)
                if user.DelTag==1:
                    return None
                if verify_type == 1 or user.check_password(password):
                    if password:
                        self.change_password_popup_check(user, username, password)
                    self.clear_pwd_error_count(UserType,username)
                    user.logintype=0
                    try:
                        user.logincount += 1
                    except:
                        user.logincount = 0
                    user.save(update_fields=['logincount'])
                    create_session_age()
                    # 检查是否有超过限制的管理员人数
                    # check_user_limit(user)
                    return user
                self.set_pwd_error_count(UserType,username)
            except UserModel.DoesNotExist:
                # Run the default password hasher once to reduce the timing
                # difference between an existing and a non-existing user (#20760).
                return None
                
                #UserModel().set_password(password)
        elif UserType=='employee':
            try:
                    User=get_user_model()
                    u=User.objects.get(username='employee')
            except:
                    return None
            try:
                    emp=employee.objects.get(PIN=username)
            except:
                    return None
            if emp.DelTag==1 or emp.OffDuty==1:
                return None
            pwd=emp.sysPass
            if not pwd: pwd=username
            if pwd!=password:
                self.set_pwd_error_count(UserType,username)
                return None
            self.clear_pwd_error_count(UserType,username)
            u.employee={'id':emp.id,'name':emp.EName,'pin':emp.PIN,'sysPass':emp.sysPass,'deptid':emp.DeptID_id}
            u.logintype=1
            #u.loginid=e.id
            u.is_staff=True
            create_session_age()
            return u
        else:
            return None

    def get_user_from_template(self, template):
        """
        验证模板
        """
        import os
        from ctypes import windll, CDLL, create_string_buffer
        if os.name == "nt":
            match = windll.LoadLibrary("matchdll.dll")
        else:
            so_root = os.path.join(settings.FILEPATH, 'zkeco_dlls')
            sys.path.append(so_root)
            match = CDLL(os.path.join(so_root, 'libmatchdll.so'))
        tmp_buff = create_string_buffer(template.encode())
        biodata_queryset = OperatorBiodata.objects.filter(valid=1)#.values('bio_tmp', 'user_id')
        for biodata in biodata_queryset:
            registered_buff = create_string_buffer(biodata.bio_tmp.encode())
            result = match.process(registered_buff, tmp_buff)
            if result:
                return biodata.user
        return None

    def get_group_permissions(self, user_obj, obj=None):
        """
        Returns a set of permission strings that this user has through his/her
        groups.
        """
        if user_obj.is_anonymous or obj is not None:
            return set()
        if not hasattr(user_obj, '_group_perm_cache'):
            if user_obj.is_superuser:
                perms = Permission.objects.all()
            else:
                user_groups_field = get_user_model()._meta.get_field('groups')
                user_groups_query = 'group__%s' % user_groups_field.related_query_name()
                perms = Permission.objects.filter(**{user_groups_query: user_obj})
            perms = perms.values_list('content_type__app_label', 'codename').order_by()
            user_obj._group_perm_cache = set("%s.%s" % (ct, name) for ct, name in perms)
        return user_obj._group_perm_cache

    def get_all_permissions(self, user_obj, obj=None):
        if user_obj.is_anonymous or obj is not None:
            return set()
        if not hasattr(user_obj, '_perm_cache'):
            user_obj._perm_cache = set("%s.%s" % (p.content_type.app_label, p.codename) for p in user_obj.user_permissions.select_related())
            user_obj._perm_cache.update(self.get_group_permissions(user_obj))
        return user_obj._perm_cache

    def has_perm(self, user_obj, perm, obj=None):
        if not user_obj.is_active:
            return False
        return perm in self.get_all_permissions(user_obj, obj)

    def has_module_perms(self, user_obj, app_label):
        """
        Returns True if user_obj has any permissions in the given app_label.
        """
        if not user_obj.is_active:
            return False
        for perm in self.get_all_permissions(user_obj):
            if perm[:perm.index('.')] == app_label:
                return True
        return False

    def get_user(self, user_id):
        UserModel = get_user_model()
        try:
            # user =  UserModel._default_manager.get(pk=user_id)
            user =  UserModel.objByID(user_id)
            return user
        except UserModel.DoesNotExist:
            return None

    def check_account_locked(self, user_type, username):
        """
        检查账户是否已锁定
        """
        if GetParamValue("opt_basic_password_error_limit", '0') == '1':
            username = base64.b64encode(username.encode('utf-8'))  #用户名可能含空格,memcached不支持,先转base64
            locked_key = "%s_ACCOUNT_LOCKED_%s_%s" % (settings.UNIT, user_type, username)
            if cache.get(locked_key):
                unlocked_time = cache.get(locked_key) + datetime.timedelta(minutes=int(GetParamValue("opt_basic_account_locked_time", '0')))
                raise Exception(u"%s" % (_(u"The number of password input errors exceeds the limit, the account has been locked, and will be unlocked automatically after %s"
                    )) % unlocked_time.strftime("%Y-%m-%d %H:%M:%S")) 

    def set_pwd_error_count(self, user_type, username):
        """
        密码验证失败计数
        """
        if GetParamValue("opt_basic_password_error_limit", '0') == '1':
            error_count = 1
            username = base64.b64encode(username.encode('utf-8'))  #用户名可能含空格,memcached不支持,先转base64
            error_count_key = "%s_PASSWORD_ERROR_COUNT_%s_%s" % (settings.UNIT, user_type, username)
            locked_key = "%s_ACCOUNT_LOCKED_%s_%s" % (settings.UNIT, user_type, username)
            if cache.get(error_count_key):
                error_count = cache.incr(error_count_key, 1)
                if error_count >= int(GetParamValue("opt_basic_max_pwd_error_count", '0')):
                    cache.set(locked_key, datetime.datetime.now(), timeout=int(GetParamValue("opt_basic_account_locked_time", '0'))*60)
                    cache.delete(error_count_key)
            else:
                cache.set(error_count_key, 1, timeout=int(GetParamValue("opt_basic_max_trial_time", '0'))*60)

    def clear_pwd_error_count(self, user_type, username):
        """
        清除密码错误计数
        """
        username = base64.b64encode(username.encode('utf-8'))
        cache_key = "%s_PASSWORD_ERROR_COUNT_%s_%s" % (settings.UNIT, user_type, username)
        cache.delete(cache_key)
    
    def change_password_popup_check(self, user, username, password):
        """
        首页是否弹窗修改密码标记
        """
        cache.delete(settings.UNIT + '_change_password_popup_%s' % user.id)  # 确保旧的标记被删除
        # 当启用账号密码复杂度校验时，判断密码复杂度，看是否在首页弹窗修改密码
        if GetParamValue("opt_basic_strong_password", '0') == '1':
            pattern = '^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])).{8,16}$'
            if re.match(pattern, password) is None:
                # 不符合密码要求，弹窗修改
                cache.set(settings.UNIT + '_change_password_popup_%s' % user.id, '1')
        # 当用户名密码相同时，默认弹窗修改密码
        if username == password:
            if not (settings.DEMO or settings.DEBUG):  # 演示公网或测试情况，不需要校验
                cache.set(settings.UNIT + '_change_password_popup_%s' % user.id, '1')


class RemoteUserBackend(ModelBackend):
    """
    This backend is to be used in conjunction with the ``RemoteUserMiddleware``
    found in the middleware module of this package, and is used when the server
    is handling authentication outside of Django.

    By default, the ``authenticate`` method creates ``User`` objects for
    usernames that don't already exist in the database.  Subclasses can disable
    this behavior by setting the ``create_unknown_user`` attribute to
    ``False``.
    """

    # Create a User object if not already in the database?
    create_unknown_user = True

    def authenticate(self, remote_user):
        """
        The username passed as ``remote_user`` is considered trusted.  This
        method simply returns the ``User`` object with the given username,
        creating a new ``User`` object if ``create_unknown_user`` is ``True``.

        Returns None if ``create_unknown_user`` is ``False`` and a ``User``
        object with the given username is not found in the database.
        """
        if not remote_user:
            return
        user = None
        username = self.clean_username(remote_user)

        UserModel = get_user_model()

        # Note that this could be accomplished in one try-except clause, but
        # instead we use get_or_create when creating unknown users since it has
        # built-in safeguards for multiple threads.
        if self.create_unknown_user:
            user, created = UserModel.objects.get_or_create(**{
                UserModel.USERNAME_FIELD: username
            })
            if created:
                user = self.configure_user(user)
        else:
            try:
                user = UserModel.objects.get_by_natural_key(username)
            except UserModel.DoesNotExist:
                pass
        return user

    def clean_username(self, username):
        """
        Performs any cleaning on the "username" prior to using it to get or
        create the user object.  Returns the cleaned username.

        By default, returns the username unchanged.
        """
        return username

    def configure_user(self, user):
        """
        Configures a user after creation and returns the updated user.

        By default, returns the user unmodified.
        """
        return user
