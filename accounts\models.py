#coding=utf-8
import datetime
import re
from django.db import models
from django import forms
from django.contrib.auth.models import BaseUserManager, AbstractBaseUser, PermissionsMixin
from django.utils.translation import gettext_lazy as _
from django.core import validators
from django.utils import timezone
#from django.contrib.auth.models import  Permission, Group
from django.conf import settings
from django.core.cache import cache
from mysite.base.models import GetParamValue, SetParamValue
from mysite.utils import escape

class MyUserManager(BaseUserManager):
    
    def _create_user(self, username, email, password,
                     is_staff, is_superuser, **extra_fields):
        """
        Creates and saves a User with the given username, email and password.
        """
        now = timezone.now()
        if not username:
            raise ValueError('The given username must be set')
        email = self.normalize_email(email)
        user = self.model(username=username, email=email,
                          is_staff=is_staff, is_active=True,
                          is_superuser=is_superuser, last_login=now,
                          date_joined=now, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_user(self, username, email, password, **extra_fields):
        #if not email:
            #raise ValueError('Users must have an email address')
        return self._create_user(username, email, password, True, False,
                                 **extra_fields)
 
    def create_superuser(self, username, password, **extra_fields):
        return self._create_user(username, '',password, True, True,
                                 **extra_fields)


class MyUser(AbstractBaseUser,PermissionsMixin):
    username = models.CharField(_('username'), max_length=30, unique=True,
        help_text=_('Required. 30 characters or fewer. Letters, digits and '
                    '@/./+/-/_ only.'),
        validators=[
            validators.RegexValidator(r'^[\w.@+-]+$', _('Enter a valid username.'), 'invalid')
        ])
    first_name = models.CharField(_(u'name'), max_length=30, blank=True)
    last_name = models.CharField(_('last name'), max_length=30, blank=True,editable=False)
    email = models.EmailField(_('email address'), blank=True)
    is_staff = models.BooleanField(_('staff status'), default=False,
        help_text=_('Designates whether the user can log into this admin '
                    'site.'))
    is_active = models.BooleanField(_('active'), default=True,
        help_text=_('Designates whether this user should be treated as '
                    'active. Unselect this instead of deleting accounts.'))
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)
    is_alldept=models.BooleanField(_(u'Authorize all departments'),default=False, blank=True,editable=True, help_text=_(u"Authorize whether to manage all departments."))
    is_allzone = models.BooleanField(_(u'Authorize all zones'), default=False, blank=True, editable=True,
                                     help_text=_(u"Authorize whether to manage all zones."))
    is_all_restaurant = models.BooleanField(_(u'Authorize all restaurants'), default=False, blank=True, editable=True,
                                     help_text=_(u"Authorize whether to manage all restaurants."))
    AutheTimeDept=models.IntegerField(_(u'authorized use'),db_column='authetimedept',null=True, default=0,blank=True,editable=True,help_text=_(u"The time period and shift. If all units use the same set of time slots and shifts, ignore this item"))#授权管理选择部门的时段和班次，不填默认为管理所有时段和班次，建议不填写。仅限设置一二级部门，暂时不开放，仅当有企业需要时
    Tele = models.CharField(_(u'contact number'),db_column="ophone",max_length=30, null=True, blank=True)
    is_public=models.BooleanField(_(u'Is it public?'),default=False, blank=True,editable=False, help_text=_(u"Open super administrator name, phone number, EMAIL, convenient to contact."))
    logintype = models.IntegerField(null=True, blank=True, default=0) #新增字段，表示是管理员登陆还是员工登陆，不需要保存。1=员工登陆
    loginid = models.IntegerField(null=True, blank=True, default=0)
    logincount = models.IntegerField(_(u'Number of logins'),null=True, blank=True, default=0)
    DelTag = models.IntegerField(_(u'logout mark'),db_column="deltag",default=0, editable=False, null=True, blank=True)
    emp_pin = models.CharField(_(u'personnel number'),max_length=30, null=True, blank=True,editable=False)
    login_to_visualization = models.BooleanField(_(u'login to visualization'), default=False, editable=True)
    objects = MyUserManager()

    USERNAME_FIELD = 'username'
    #REQUIRED_FIELDS = ['email']
    class Admin:
            list_filter = ()
            search_fields = ['username','first_name', 'Tele','email']

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        db_table='auth_user'
        default_permissions = ('browse', 'add', 'change', 'delete')

    def get_full_name(self):
        """
        Returns the first_name plus the last_name, with a space in between.
        """
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        "Returns the short name for the user."
        return self.first_name

    @staticmethod
    def objByID(id):
        if id==None: return None
        user = cache.get("%s_iclock_user_%s"%(settings.UNIT, id))
        if user:
            return user
        try:
            user = MyUser.objects.get(pk=id)
        except:
            user = None
        if user:
            cache.set("%s_iclock_user_%s"%(settings.UNIT, id), user)
        return user

    def save(self, *args, **kwargs):
        self.username = escape(self.username)
        try:
            cache.delete("%s_iclock_user_%s"%(settings.UNIT, self.id))
        except:
            pass
        super(MyUser, self).save(*args, **kwargs)

    def delete(self):
        try:
            cache.delete("%s_iclock_user_%s"%(settings.UNIT, self.id))
        except:
            pass
        super(MyUser, self).delete()

    #def email_user(self, subject, message, from_email=None, **kwargs):
    #    """
    #    Sends an email to this User.
    #    """
    #    send_mail(subject, message, from_email, [self.email], **kwargs)
  
    def __unicode__(self):
        return self.username
    def __str__(self):
        return self.username
    #def is_authenticated(self):  
    #    return True  
    @staticmethod	
    def colModels():
        return    [{'name':'id','sortable':False,'hidden':True,'frozen': True},
            {'name':'username','index':'username','width':100,'label':u"%s"%(MyUser._meta.get_field('username').verbose_name),'frozen': True},
            #{'name':'last_name','width':80,'sortable':False,,'label':u"%s"%(MyUser._meta.get_field('username').verbose_name)},
            {'name':'first_name','width':100,'sortable':False,'label':u"%s"%(MyUser._meta.get_field('first_name').verbose_name)},
            {'name':'is_staff','width':80,'sortable':False,'label':u"%s"%(_(u"staff status"))},
            {'name':'is_superuser','width':80,'sortable':False,'label':u"%s"%(_(u"Supervisor"))},
            {'name':'deptadmin_set','width':200,'sortable':False,'label':u"%s"%(_(u"granted department"))},
            {'name': 'zone', 'width': 200, 'sortable': False, 'label': u"%s" % (_(u"authorized area")),'hidden':True},
            {'name': 'ding_set', 'width': 200, 'sortable': False,'label': u"%s" % (_(u"authorized restaurant")),'hidden':True},
            {'name': 'iclock_set', 'width': 200, 'sortable': False, 'label': u"%s" % (_(u"authorized device")),'hidden':True},
            {'name':'groups','width':150,'sortable':False,'label':u"%s"%(_(u"Groups"))},
            {'name':'Userroles','width':100,'sortable':False,'label':u"%s"%(_(u"Userroles"))},
            {'name':'Creator','width':100,'sortable':False,'label':u"%s"%(_(u"creator"))},
            {'name':'TimeDept','width':160,'sortable':False,'label':u"%s"%(_(u"Authorization of the time period of the department"))},
            {'name':'logincount','width':80,'sortable':False,'label':u"%s"%(MyUser._meta.get_field('logincount').verbose_name)},
            {'name':'last_login','width':120,'sortable':False,'label':u"%s"%(_(MyUser._meta.get_field('last_login').verbose_name))},
            {'name':'Tele','width':100,'sortable':False,'label':u"%s"%(_(u"contact number"))},
            # {'name':'emp_pin','width':100,'sortable':False,'label':u"%s"%(_(u"personnel number"))},

            {'name':'email','width':120,'sortable':False,'label':u"%s"%(MyUser._meta.get_field('email').verbose_name)},
            {'name':'date_joined','width':120,'sortable':False,'label':u"%s"%(MyUser._meta.get_field('date_joined').verbose_name)},
            ]




  
    """ 
    # 这个函数可以实现自定义的用户密码检验，除非你想跳过Django的才实现。
    def check_password(self, password):  
        if self.hashed_password(password) == self.pwd:  
            return True
        print ('debug: password(%s), hashed %s, self.pwd %s' % (password, self.hashed_password(password), self.pwd))
        return False
    """

    """
    # 这里可以实现自己的密码管理
    def set_password(self, raw_password):
        print ('set_password called')
        self.pwd = self.my_hashed_pwd(raw_password)
        super(User, self).set_password(raw_password)
    """
    def set_password(self, raw_password):
        if self.id is None and self.username != 'employee':
            if GetParamValue("opt_basic_strong_password", '0')=='1' and not raw_password in ('111111','admin'):  #此处用于新建用户设置密码时的判断
                if not re.match('^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])).{8,16}$',raw_password):
                    raise forms.ValidationError(
                        u"%s"%(_(u'The password must contain uppercase letters, lowercase letters and digits, with a length of 8 to 16 characters.')),
                        code='password_incorrect',
                    )
            SetParamValue(f'change_password_time_{self.id}', datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        super(MyUser, self).set_password(raw_password)

##########################################################################################
def __permission_unicode__(self):
    ct_id=self.content_type_id
    ckey="%s_ct_%s"%(settings.UNIT,ct_id)
    ct=cache.get(ckey)
    #ct=None
    if not ct:
        ct=self.content_type
        cache.set(ckey, ct)

    #print  u"%s | %s | %s" % (u"%s"%(ct.app_label),u"%s"%(ct.model),u"%s"%(self.codename))

    return u"%s|%s|%s" % ( u"%s"%(ct.app_label), u"%s"%(ct.model), u"%s"%(self.codename))
from django.contrib.auth.models import  Permission
import sys
if sys.version[0]=='3':
    Permission.__str__=__permission_unicode__
else:
    Permission.__unicode__=__permission_unicode__
Permission._meta.ordering = ('content_type__app_label', 'content_type__model','id')
