#!/usr/bin/env python
#coding=utf-8
import datetime
from django.contrib.auth.decorators import permission_required, login_required
from django.utils.translation import gettext_lazy as _
from django.utils.translation import gettext as _trans
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.views.decorators.csrf import csrf_protect
from mysite.utils import *
from mysite.acc.models import *
from mysite.iclock.models import *
from mysite.iclock.iutils import *

@csrf_protect
@login_required
def getData(request):
    funid = request.GET.get("func", "")
    if funid == 'get_level':  # 模糊查询权限组
        from mysite.iclock.datautils import hasPerm
        if (not hasPerm(request.user,level,"browse")):
            return getJSResponse({'msg':"No permission"})

        if request.method == 'POST':
            inputValue = request.POST['inputValue']
            pin = request.POST['pin']
            msg = {}
            html = "<input id='id_levels' type='hidden' name='levels' /><ul id='levelSingleBrowser' style='margin:5px 2px 3px 5px;'>"
            levels = list(level_emp.objects.filter(UserID__PIN=pin).values_list("level_id", flat=True))
            levobj = level.objects.filter(name__contains=inputValue)
            if not (request.user.is_superuser or request.user.is_allzone):
                zas = ZoneAdmin.objects.filter(user=request.user).values('code')#区域
                iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zas).values('SN')#控制器
                ld = level_door.objects.filter(door__device__in=iz).values('level')
                ex_level = level_door.objects.filter(level__in=ld).filter(~Q(door__device__in=iz)).values('level')
                levobj =list(levobj.filter(id__in=ld).exclude(id__in=ex_level).values_list('id', 'name'))
            else:
                levobj = levobj.values_list('id', 'name')
            if levobj:
                for lev in levobj:
                    if lev[0] not in levels:  #未勾选的门禁权限
                        html += "<label><li><span><input type='checkbox'  name='level' value='%s'/>%s</span></li></label>" % (lev[0], lev[1])
            else:
                html += "<label class='<label>'>%s</label>" % u"%s"%(_(u'No relevant permission group can be found'))
            leo = level.objects.filter(id__in=levels)
            for le in leo:
                if le.id not in [x[0] for x in levobj]:
                    html += "<label><li><input type='checkbox' checked='checked' disabled='disabled' name='level' value='%s'/><span>%s</span></li></label>" % (le.id, le.name)
                else:
                    html += "<label><li><input type='checkbox' checked='checked' name='level' value='%s'/><span>%s</span></li></label>" % (le.id, le.name)
            msg['html'] = html
            return getJSResponse(msg)
    if funid == 'timezones_edit':
        if request.method=='GET':
            d={}
            colModels=[
                {'name':'id','width':100,'sortable':False,'align':'right','title':False,'label':""},
                {'name':'StartTime0','width':100,'sortable':False,'title':False,'align':'center','label':u"%s"%(_(u'Starting time'))},
                {'name':'EndTime0','width':100,'sortable':False,'title':False,'align':'center','label':u"%s"%(_(u'End Time'))},
                {'name':'StartTime1','width':100,'sortable':False,'title':False,'align':'center','label':u"%s"%(_(u'Starting time'))},
                {'name':'EndTime1','width':100,'sortable':False,'title':False,'align':'center','label':u"%s"%(_(u'End Time'))},
                {'name':'StartTime2','width':100,'sortable':False,'title':False,'align':'center','label':u"%s"%(_(u'Starting time'))},
                {'name':'EndTime2','width':100,'sortable':False,'title':False,'align':'center','label':u"%s"%(_(u'End Time'))}
            ]
            groupHeaders=[
                {'startColumnName': 'StartTime0', 'numberOfColumns': 2, 'titleText': '<em><font color="red">{}1</font></em>'.format(_('Time Period'))},
                {'startColumnName': 'StartTime1', 'numberOfColumns': 2, 'titleText': '<em><font color="red">{}2</font></em>'.format(_('Time Period'))},
                {'startColumnName': 'StartTime2', 'numberOfColumns': 2, 'titleText': '<em><font color="red">{}3</font></em>'.format(_('Time Period'))},

            ]

            d['colModel']=colModels
            d['groupHeaders']=groupHeaders

            return getJSResponse(d)
        else:
            key=request.GET.get('key','')
            rows=[]
            s_input="<input type='text'   id='timezone%s_%s' maxlength='2'  value='%s'  class='hour acctimezone'/>:<input type=text  id='timezone%s_%s' maxlength='2' value='%s' class='min acctimezone'/>"
            weeks=[u"%s"%(_(u'Mon')),u"%s"%(_(u'Tue')),u"%s"%(_(u'Wed')),u"%s"%(_(u'Thu')),u"%s"%(_(u'Fri')),u"%s"%(_(u'Sat')),u"%s"%(_(u'Sun')),u"%s"%(_(u'Festival Type 1')),u"%s"%(_(u'Festival Type 2')),u"%s"%(_(u'Festival Type 3'))]
            if key=='_new_':
                for i in range(10):
                    row={'id':weeks[i]}
                    for j in range(3):
                        row['StartTime%s'%j]=s_input%(i,j*4,'00',i,j*4+1,'00')
                        row['EndTime%s'%j]=s_input%(i,j*4+2,'00',i,j*4+3,'00')
                    rows.append(row.copy())
            else:
                tzobj=timezones.objects.get(id=key)
                tz=tzobj.tz
                c=0
                for i in range(10):
                    row={'id':weeks[i]}
                    for j in range(3):
                        s1=tz[c*12:c*12+2]
                        s2=tz[c*12+3:c*12+3+2]
                        row['StartTime%s'%j]=s_input%(i,j*4,s1,i,j*4+1,s2)
                        e1=tz[c*12+6:c*12+6+2]
                        e2=tz[c*12+9:c*12+9+2]


                        row['EndTime%s'%j]=s_input%(i,j*4+2,e1,i,j*4+3,e2)
                        c+=1
                    rows.append(row.copy())



            #print "----------",rows
            t_r={}
            #rows=[{'id':"1",'StartTime1':"<input type='text'   id='timezone11_1' maxlength='2'  value='00'  class='hour acctimezone'/>:<input type=text value=23 id='timezone11_2' maxlength='2'  class='hour acctimezone'/>",'EndTime1':"<input type='text'  id='timezone12_1' maxlength='2'  value='00'  class='hour acctimezone'/>:<input type=text id='timezone12_2' value=23 maxlength='2'  class='hour acctimezone'/>"},{'id':'22','StartTime1':'12:00','EndTime1':'1221'}]
            t_r['page']=1
            t_r['total']=1
            t_r['records']=10
            t_r['rows']=rows
            return getJSResponse(t_r)
    elif funid=='devs':
        if request.method=='POST':
            q=request.POST.get("q","")
            if q=="":
                q=request.GET.get("q","")
            if request.user.is_superuser or request.user.is_alldept:
                if q!="":
                    iclocks=iclock.objects.filter(Q(SN__contains=q)|Q(Alias__contains=q)).exclude(DelTag=1)
                else:
                    iclocks=iclock.objects.all()
            else:
                sns=userIClockList(request.user)
                if q!="":
                    iclocks=iclock.objects.filter(SN__in=sns).filter(Q(SN__contains=q)|Q(Alias__contains=q)).exclude(DelTag=1)
                else:
                    iclocks=iclock.objects.filter(SN__in=sns)
            items=iclocks.filter(ProductType__in=[4,5,15]).exclude(DelTag=1)
            re=[]
            r={}
            for dev in items:
                r['SN']=dev.SN
                r['Alias']=dev.Alias
                re.append(r.copy())
            return getJSResponse(dumps(re))
    elif funid=='devs_tree':
        d={}
        dd={}
        ptype=request.GET.get('ptype','')
        mod = request.GET.get('mod','')

        if request.user.is_superuser or request.user.is_alldept or request.user.is_allzone:
            iclocks=iclock.objects.all()
        else:
            zonelist=userZoneList(request.user)
            sn_list=IclockZone.objects.filter(zone__in =zonelist).values_list("SN",flat=True)
            iclocks=iclock.objects.filter(SN__in=sn_list)
        if ptype in ['acc','AntiPassBack']:
            if ptype=='acc':
                iclocks=iclocks.filter(ProductType__in=[5,15,25])
                # 过滤不能互锁设备
                if mod == 'interlock':
                    iclocks = iclocks.filter(ProductType__in=[5,15]).exclude(LockFunOn=1)  #lockfunon为1代表只有一个锁
            else:
                devs=AntiPassBack.objects.all().values('device')
                iclocks=iclocks.filter(ProductType__in=[5,15,25]).exclude(SN__in=devs)

        #else:
        #	iclocks=iclocks.filter(Q(ProductType__isnull=True)|Q(ProductType=0))
        iclocks=iclocks.exclude(DelTag=1).exclude(State=0)
        d=[]
        for t in iclocks:
            dev = getDevice(t.SN)
            if dev.ProductType == 25 and int(getattr(dev, 'APBFO', 0)) != 1:
                continue
            dd["id"]=t.SN
            dd["name"]=u'%s(%s)'%(t.Alias or '',t.SN)
            dd["pid"]=0
            dd["value"]=t.SN
            dd["open"]=False
            dd["isParent"]=False
            if t.Style in ['101', '102']:  # 一体机
                dd['icon'] = '/media/img/device101.png'
            else:
                dd['icon'] = '/media/img/device1.png'
            d.append(dd.copy())
        return getJSResponse(d)
    elif funid=='devstree':
        d={}
        dd={}
        ptype=request.GET.get('ptype','')
        deptName=u'%s'%_(u'all devices')

        if request.user.is_superuser or request.user.is_alldept:
            iclocks=iclock.objects.all()
        else:
            zonelist=userZoneList(request.user)
            sn_list=IclockZone.objects.filter(zone__in =zonelist).values_list("SN",flat=True)
            iclocks=iclock.objects.filter(SN__in=sn_list)
        if ptype=='acc':
            iclocks=iclocks.filter(ProductType__in=[4,5,15,25])
        elif ptype=='patrol':
            iclocks=iclocks.filter(ProductType__in=[2])

        else:
            iclocks=iclocks.exclude(ProductType__in=[2,4,5,15,25])
        #	iclocks=iclocks.filter(Q(ProductType__isnull=True)|Q(ProductType=0))
        iclocks=iclocks.exclude(DelTag=1)
        d["id"]=0
        d["name"]=deptName
        d["pid"]=-1
        d["value"]=0
        d["open"]=True
        d["isParent"]=False
        d['icon']="/media/img/icons/home.png"
        d['nocheck']=True
        d["children"]=[]
        #meets=participants_tpl.objects.all().exclude(DelTag=1).order_by('-id')
        for t in iclocks:
            d["isParent"]=True

            dd["id"]=t.SN
            dd["name"]="%s(%s)"%(t.SN,t.Alias or '')
            dd["pid"]=0
            dd["value"]=t.SN
            dd["open"]=False
            dd["isParent"]=False
            d["children"].append(dd.copy())
        return getJSResponse(d)


    elif funid=='door_first':
        dd={}
        ptype=request.GET.get('ptype','')

        if request.user.is_superuser or request.user.is_alldept:
            iclocks=iclock.objects.all().exclude(DelTag=1).exclude(State=0).values("SN")
            accd=AccDoor.objects.filter(device__in=iclocks)
        else:
            zas = ZoneAdmin.objects.filter(user=request.user).values('code')
            iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zas).values('SN')
            accd=AccDoor.objects.filter(device__in=iz)
        if  ptype=='FirstOpen':
            doors=FirstOpen.objects.all().values_list("door",flat=True)
            accd=accd.exclude(id__in=doors)
        d=[]
        for t in accd:

            dd["id"]=t.id
            dd["name"]=t.door_name
            dd["pid"]=0
            dd["value"]=t.door_name
            dd["open"]=False
            dd["isParent"]=False
            dd["icon"] = '/media/img/door.png'
            d.append(dd.copy())
        return getJSResponse(d)

    elif funid=='ACTimeZones':
        timezone = timezones.objects.exclude(DelTag=1).order_by('id')
        re = []
        ss = {}
        for t in timezone:
            ss['TimeZoneID'] = t.id
            ss['Name'] = t.Name
            t = ss.copy()
            re.append(t)
        return getJSResponse(dumps(re))
    elif funid=='ACGroup':
        group=ACGroup.objects.all().order_by('GroupID')
        re = []
        ss = {}
        for g in group:
            ss['GroupID'] = g.GroupID
            ss['Name'] = g.Name
            g = ss.copy()
            re.append(g)
        return getJSResponse(dumps(re))
    elif funid=='level_door' or funid == 'storage_device':#获取区域设备 门列表 可以通过page执行不页面的需求
        key=request.GET.get('key','')#权限组id
        page=request.GET.get('page','')#服务的页面

        ExistedDoor=[]
        chk=False
        if key:
            obj=level.objects.get(id=key)
            if obj.irange==-1:
                chk=True	#表示所有门
            else:
                objs=level_door.objects.filter(level=key)
                for t in objs:
                    try:
                        ExistedDoor.append(t.door_id)
                    except:
                        continue



        if not request.user.is_superuser:
            if not request.user.is_allzone:
                za = ZoneAdmin.objects.filter(user=request.user)
                iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=za.values('code'))
                zacode = za.values_list('code__code', flat=True)
                zo = zone.objects.filter(DelTag=0, code__in=zacode)
            else:
                zo = zone.objects.filter(DelTag=0)
                iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zo.values('id'))

            sns = iz.values('SN')
            accs=AccDoor.objects.filter(device__DelTag=0,device__SN__in=sns).order_by('id')
            if page == 'storage':
                iclo = iclock.objects.filter(ProductType=30,DelTag=0,SN__in=sns).order_by('SN')
            else:
                iclo=iclock.objects.filter(ProductType__in=[4,5,15,25],DelTag=0,SN__in=sns).order_by('SN')
            if page=='iclock_door':#门禁权限页面，点击区域获取控制器
                zoneid=int(request.POST.get('id',0))
                if zoneid:
                    zoneids=[]
                    isContainChild=request.GET.get('isContainChild')
                    if isContainChild=='1':
                        AllChildrenZone(zoneid,zoneids)
                    zoneids.append(zoneid)
                    iclks=iz.filter(zone__in=zoneids).values('SN')
                    accs=accs.filter(device__in=iclks)
                    iclo=iclo.filter(SN__in=iclks)



        else:
            iz=IclockZone.objects.filter(zone__DelTag=0)
            accs=AccDoor.objects.filter(Q(device__DelTag=0) & Q(device__ProductType__in=[4,5,15,25])).order_by('id')  # 去除设备用途由门禁变更为考勤
            if page == 'storage':
                iclo = iclock.objects.filter(ProductType=30, DelTag=0).order_by('SN')
            else:
                iclo=iclock.objects.filter(ProductType__in=[4,5,15,25],DelTag=0).order_by('SN')
            zo=zone.objects.filter(DelTag=0)
            if page=='iclock_door':#门禁权限页面，点击区域获取控制器
                zoneid=int(request.POST.get('id',0))
                if zoneid:
                    zoneids=[]
                    isContainChild=request.GET.get('isContainChild')
                    if isContainChild=='1':
                        AllChildrenZone(zoneid,zoneids)
                    zoneids.append(zoneid)
                    iclks=iz.filter(zone__in=zoneids).values('SN')
                    accs=accs.filter(device__in=iclks)
                    iclo=iclo.filter(SN__in=iclks)
        if page=='iclock_door':#不需要区域
            zo=[]
        zlist={}
        for z in iz:
            try:
                zlist[z.SN_id]=z.zone_id
            except:
                continue

        nozone_dict = {'id':'z_-1','name':u'%s'%_(u'unowned area'),'pid':0,'open':True,'checked':chk,'Attribute':'','nocheck':True,'icon':'/media/img/zone.png'}
        re=[]
        ss = {}
        if page=='acc_monitor':accs=[]#实时监控页面不需要显示门信息
        if page == "checked_door":#获取已选择的门
            for t in accs:
                try:
                    device = getDevice(t.device_id)
                    if device.ProductType not in [4, 5, 15, 25]:
                        continue
                except:
                    continue
                ss_id = t.id if page != 'records' else t.door_no
                device = getDevice(t.device_id)
                name = t.door_name + u' %s:'%_(u'device') + '%s' % device.SN
                if device.Alias:
                    name=name+'(%s)'%(device.Alias)
                if ss_id in ExistedDoor or chk:
                    ss = {}
                    ss['id'] = ss_id
                    ss['name'] = name
                    ss['pid'] = t.device_id
                    ss['icon'] = '/media/img/door.png'
                    ss['checked'] = True
                    ss['Attribute'] = 'door'
                    re.append(ss.copy())

            return getJSResponse(re)




        nozone=0
        if funid=='level_door':
            if isinstance(accs, list):
                pass  #page=='acc_monitor':accs=[]#实时监控页面不需要显示门信息
            else:
                accs = accs.values('id', 'door_no', 'door_name', 'device_id')  #优化查询效率
            for t in accs:
                ss={}
                ss['id']=t['id'] if page!='records' else t['door_no']
                ss['name']=t['door_name']
                ss['pid']=t['device_id']
                ss['icon'] = '/media/img/door.png'
                if ss['id'] in ExistedDoor or chk:
                    ss['checked']=True
                ss['Attribute']='door'
                re.append(ss.copy())
        iclo = iclo.values('SN', 'Alias', 'Style')  #优化查询效率
        for t in iclo:
            ss={}
            ss['id']=t['SN']
            ss['name']='%s %s'%(t['SN'],t['Alias'] or '')
            if t['Style'] in ['101','102']:#一体机
                ss['icon'] = '/media/img/device101.png'
            else:
                ss['icon']='/media/img/device1.png'
            pid=-1
            try:
                pid=str(zlist[t['SN']])
                pid='z_'+pid
            except:
                nozone=1
                pid='z_-1'

            ss['pid']=pid
            ss['checked']=False
            ss['open']=True if page not in ['records'] else False
            ss['Attribute']='iclock'
            re.append(ss.copy())

        for t in zo:
            ss={}
            ss['id']='z_'+str(t.id)
            ss['name']=t.name
            ss['icon']='/media/img/zone.png'
            pid=-1
            if t.parent:
                pid='z_'+str(t.parent)
            ss['pid']=pid
            ss['checked']=False
            ss['open']=True
            ss['Attribute']='zone'
            ss['nocheck']=True
            re.append(ss.copy())
        if nozone:
            re.append(nozone_dict)
        return getJSResponse(re)
    elif funid=='antipassback':
        dd={}
        flag=request.GET.get('flag','')
        key=request.GET.get('keys','')
        linkid=request.GET.get('id','')
        isExistEvents=[]
        isExistInputs=[]
        isExistOutputs=[]
        re=[]
#		if flag=='add':
        dev=iclock.objects.get(SN=key)
        re=getAntiPassBackInfo(dev)

        return getJSResponse(re)
    elif funid=='interlock':
        dd={}
        flag=request.GET.get('flag','')
        key=request.GET.get('keys','')
        linkid=request.GET.get('id','')
        isExistEvents=[]
        isExistInputs=[]
        isExistOutputs=[]
        re=[]
#		if flag=='add':
        dev=iclock.objects.get(SN=key)
        re=getInterLockInfo(dev)
        #print "---------",re
        return getJSResponse(re)


    elif funid=='door_events':
        #from mysite.acc.models import EVENT_CHOICES
        dd={}
        flag=request.GET.get('flag','')
        key=request.GET.get('keys','')
        linkid=request.GET.get('id','')
        isExistEvents=[]
        isExistInputs=[]
        isExistOutputs=[]

        isExistEvents_in=[]
        isExistInputs_in=[]
        isExistOutputs_in=[]

        isExistEvents_reader=[]
        isExistInputs_reader=[]
        isExistOutputs_reader=[]
        door_events=[5,7,8,9,24,25,28,36,37,38,100,102,200,201,202,204,205,209]
        door_reader_events=[0,2,3,4,20,21,22,23,27,29,41,42,101]

        action_time=0
        if flag=='edit' and linkid!='':
            trigobjs=linkage_trigger.objects.filter(linkage_id=linkid)
            for t in trigobjs:
                if t.trigger_cond in door_events:
                    isExistEvents.append(t.trigger_cond)
                elif t.trigger_cond in [220,221]:
                    isExistEvents_in.append(t.trigger_cond)
                elif t.trigger_cond in door_reader_events:
                    isExistEvents_reader.append(t.trigger_cond)


            inoutobjs=linkage_inout.objects.filter(linkage_id=linkid)
            for t in inoutobjs:
                if t.input_type in [0,1]:#暂未考虑读头输入
                    isExistInputs.append(t.input_id)

                    if t.output_type==0:
                        isExistOutputs.append(t.output_id)
                    if t.output_type==1:
                        isExistOutputs.append(t.output_id*(-1))

                elif t.input_type in [2,100]:#辅助输入
                    if t.input_type==100:
                        isExistInputs_reader.append(0)
                    else:
                        isExistInputs_reader.append(t.input_id)

                    if t.output_type==0:
                        isExistOutputs_reader.append(t.output_id)
                    if t.output_type==1:
                        isExistOutputs_reader.append(t.output_id*(-1))


                elif t.input_type in [3,1000]:#辅助输入
                    if t.input_type==1000:
                        isExistInputs_in.append(0)
                    else:
                        isExistInputs_in.append(t.input_id)

                    if t.output_type==0:
                        isExistOutputs_in.append(t.output_id)
                    if t.output_type==1:
                        isExistOutputs_in.append(t.output_id*(-1))

                #print "----------------------",t.action_time,linkid
                action_time=t.action_time
        d = {'id':-10,'name':u'%s'%_(u'door event'),'pid':-100,'open':True,'checked':False,'children':[]}
        d_in = {'id':-10,'name':u'%s'%_(u'Auxiliary Input Event'),'pid':-100,'open':True,'checked':False,'children':[]}
        reader = {'id':-10,'name':u'%s'%_(u'Door and Reader Event'),'pid':-100,'open':True,'checked':False,'children':[]}
        re={}
        #ex_events=[-1,5,6,10,11,12,13,14,15,16,17,18,19,25,26,30,31,32,33,34,35,206,222,223]
        for t in EVENT_CHOICES:
            dd={}
            dd["id"]=t[0]
            dd["name"]=u'%s'%t[1]
            dd["pid"]=-10
            dd["value"]=t[0]

            dd["open"]=False
            dd["isParent"]=False
            if t[0] in [220,221]:
                if dd['id'] in isExistEvents_in:
                    dd['checked']=True
                d_in['children'].append(dd.copy())
            elif t[0] in door_events:
                if dd['id'] in isExistEvents:
                    dd['checked']=True

                d['children'].append(dd.copy())
            elif t[0] in door_reader_events:
                if dd['id'] in isExistEvents_reader:
                    dd['checked']=True

                reader['children'].append(dd.copy())


        re['ret']=len(d['children'])
        re['events']=d
        re['in_events']=d_in
        re['reader_events']=reader
        #dev=getDevice(key)
        inputs = {'id':-10,'name':u'%s'%_(u'input point'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}

        doors=AccDoor.objects.filter(device=key).order_by('door_no')
        for t in doors:
            dd={}
            dd["id"]=t.id
            dd["name"]=u'%s'%t.door_name
            dd["pid"]=-10
            dd["value"]=t.id
            dd["open"]=False
            dd["isParent"]=False


            inputs['children'].append(dd.copy())
        for t in inputs['children']:
            t['checked']=False
            if t['id'] in isExistInputs:
                t['checked']=True

        re['inputs']=copy.deepcopy(inputs)


        in_inputs = {'id':-10,'name':u'%s'%_(u'input point'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}

        auxins=AuxIn.objects.filter(device=key).order_by('aux_no')
        #in_inputs['children']=in_inputs['children'][1:100]
        for t in auxins:
            dd["id"]=t.id
            dd["name"]=u'%s'%t.aux_name
            dd["pid"]=-10
            dd["value"]=t.id
            dd["open"]=False
            dd["isParent"]=False
            in_inputs['children'].append(dd.copy())
        for t in in_inputs['children']:
            t['checked']=False
            if t['id'] in isExistInputs_in:
                t['checked']=True
        re['in_inputs']=in_inputs

        reader_inputs = {'id':-10,'name':u'%s'%_(u'input point'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}

        readers=device_options.objects.filter(SN=key,ParaName='ReaderCount')
        readerCount=0
        if readers:
            readerCount=int(readers[0].ParaValue)
        #in_inputs['children']=in_inputs['children'][1:100]
        for i in range(readerCount):
            dd["id"]=i+1
            dd["name"]=u'Reader %s'%(i+1)
            dd["pid"]=-10
            dd["value"]=i+1
            dd["open"]=False
            dd["isParent"]=False
            reader_inputs['children'].append(dd.copy())
        for t in reader_inputs['children']:
            t['checked']=False
            if t['id'] in isExistInputs_reader:
                t['checked']=True
        re['reader_inputs']=reader_inputs









        auxouts=AuxOut.objects.filter(device=key).order_by('aux_no')
        outputs = {'id':-10,'name':u'%s'%_(u'output point'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
        #inputs['name']=u'输出点'
        outputs['children']=copy.deepcopy(inputs['children'][1:100])
        for t in auxouts:
            dd["id"]=t.id*(-1)
            dd["name"]=u'%s'%t.aux_name
            dd["pid"]=-10
            dd["value"]=t.id
            dd["open"]=False
            dd["isParent"]=False
            outputs['children'].append(dd.copy())

        outputs_in=copy.deepcopy(outputs)
        outputs_reader=copy.deepcopy(outputs)
        for t in outputs['children']:
            t['checked']=False
            if t['id'] in isExistOutputs:
                t['checked']=True

        for t in outputs_in['children']:
            t['checked']=False
            if t['id'] in isExistOutputs_in:
                t['checked']=True

        for t in outputs_reader['children']:
            t['checked']=False
            if t['id'] in isExistOutputs_reader:
                t['checked']=True

        re['outputs']=outputs
        re['in_outputs']=outputs_in
        re['reader_outputs']=outputs_reader






        re['action_time']=action_time
        return getJSResponse(re)

    elif funid=='interlock_door':
        re = {}
        isExistInputs = []
        isExistOutputs = []
        SN = request.GET.get('SN','')
        key = request.GET.get('interlock_rule','')
        flag = request.GET.get('flag','')
        inputs = {'id':-10,'name':u'%s'%_(u'Group 1'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
        doors=AccDoor.objects.filter(device=SN).order_by('door_no')
        for t in doors:
            dd={}
            dd["id"]=t.door_no
            dd["name"]=u'%s'%t.door_name
            dd["pid"]=-10
            dd["value"]=t.door_no
            dd["open"]=False
            dd["isParent"]=False
            inputs['children'].append(dd.copy())
        # 回显到前端的数据
        il_objs = InterLock.objects.filter(device=SN)
        if il_objs:
            il_obj = il_objs[0]
            isExistInputs = [ int(i) for i in il_obj.initial_triggerlist.trigger.split(',')]
            if il_obj.target_triggerlist:
                isExistOutputs = [ int(i) for i in il_obj.target_triggerlist.trigger.split(',')]
        for t in inputs['children']:
            t['checked']=False
            if t['id'] in isExistInputs:
                t['checked']=True
        re['inputs']=copy.deepcopy(inputs)
        inputs = {'id':-10,'name':u'%s'%_(u'Group 2'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
        for t in doors:
            dd={}
            dd["id"]=t.door_no
            dd["name"]=u'%s'%t.door_name
            dd["pid"]=-10
            dd["value"]=t.door_no
            dd["open"]=False
            dd["isParent"]=False
            inputs['children'].append(dd.copy())
        for t in inputs['children']:
            t['checked']=False
            if t['id'] in isExistOutputs:
                t['checked']=True
        re['outputs']=copy.deepcopy(inputs)
        re['ret'] = 2
        return getJSResponse(re)
    elif funid=='antipassback_triggerlist':
        re = {}
        isExistInputs = []
        isExistOutputs = []
        isExistInputs_reader = []
        isExistOutputs_reader = []
        SN = request.GET.get('SN','')
        apb_rule = request.GET.get('apb_rule','')
        flag = request.GET.get('flag','')
        inputs = {'id':-10,'name':u'%s'%_(u'Group 1'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
        if apb_rule == '1001':  # 门反潜
            doors=AccDoor.objects.filter(device=SN).order_by('door_no')
            for t in doors:
                dd={}
                dd["id"]=t.door_no
                dd["name"]=u'%s'%t.door_name
                dd["pid"]=-10
                dd["value"]=t.door_no
                dd["open"]=False
                dd["isParent"]=False
                inputs['children'].append(dd.copy())
            # 回显到前端的数据
            ap_obj = AntiPassBack.objects.filter(device=SN)
            if ap_obj:
                isExistInputs = [ int(i) for i in ap_obj[0].initial_triggerlist.trigger.split(',')]
                isExistOutputs = [ int(i) for i in ap_obj[0].target_triggerlist.trigger.split(',')]

            for t in inputs['children']:
                t['checked']=False
                if t['id'] in isExistInputs:
                    t['checked']=True
            re['inputs']=copy.deepcopy(inputs)
            inputs = {'id':-10,'name':u'%s'%_(u'Group 2'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
            for t in doors:
                dd={}
                dd["id"]=t.door_no
                dd["name"]=u'%s'%t.door_name
                dd["pid"]=-10
                dd["value"]=t.door_no
                dd["open"]=False
                dd["isParent"]=False
                inputs['children'].append(dd.copy())
            for t in inputs['children']:
                t['checked']=False
                if t['id'] in isExistOutputs:
                    t['checked']=True
            re['outputs']=copy.deepcopy(inputs)
            re['ret'] = 2
        else:  # apb_rule = 1002  # 读头反潜
            reader_inputs = {'id':-10,'name':u'%s'%_(u'Group 1'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
            readers=device_options.objects.filter(SN=SN,ParaName='ReaderCount')
            readerCount=0
            if readers:
                readerCount=int(readers[0].ParaValue)
            # 回显到前端的数据
            ap_obj = AntiPassBack.objects.filter(device=SN)
            if ap_obj:
                isExistInputs_reader = [ int(i) for i in ap_obj[0].initial_triggerlist.trigger.split(',')]
                isExistOutputs_reader = [ int(i) for i in ap_obj[0].target_triggerlist.trigger.split(',')]
            for i in range(readerCount):
                dd={}
                dd["id"]=i+1
                dd["name"]=u'Reader %s'%(i+1)
                dd["pid"]=-10
                dd["value"]=i+1
                dd["open"]=False
                dd["isParent"]=False
                reader_inputs['children'].append(dd.copy())
            for t in reader_inputs['children']:
                t['checked']=False
                if t['id'] in isExistInputs_reader:
                    t['checked']=True
            re['inputs']=reader_inputs
            reader_outputs = {'id':-10,'name':u'%s'%_(u'Group 2'),'pid':-100,'open':True,'chkDisabled':True,'children':[{'id':0,'name':u'%s'%_(u'arbitrary'),'pid':-10,'value':0}]}
            readers=device_options.objects.filter(SN=SN,ParaName='ReaderCount')
            readerCount=0
            if readers:
                readerCount=int(readers[0].ParaValue)
            for i in range(readerCount):
                dd={}
                dd["id"]=i+1
                dd["name"]=u'Reader %s'%(i+1)
                dd["pid"]=-10
                dd["value"]=i+1
                dd["open"]=False
                dd["isParent"]=False
                reader_outputs['children'].append(dd.copy())
            for t in reader_outputs['children']:
                t['checked']=False
                if t['id'] in isExistOutputs_reader:
                    t['checked']=True
            re['outputs']=reader_outputs
            re['ret'] = 2
        return getJSResponse(re)
    elif funid=='zonetree':
        #d={}
        rt=[]

        #设置区域时，显示已设区域，暂仅用于系统用户设置区域
        zoneids = []
        model_name = request.GET.get('modelName','')
        k = request.GET.get('K','')
        if model_name == 'User' and k:
            zoneids = ZoneAdmin.objects.filter(user__exact=k).values_list('code',flat=True)
        elif model_name == 'employee' and k:
            zoneids = empZone.objects.filter(UserID=k).values_list('zone', flat=True)
        code=request.GET.get('code','')
        child=[]

        if request.user.is_superuser or request.user.is_allzone:
            objs=zone.objects.all().order_by('parent','code').exclude(DelTag=1)
        else:
            zonelist=userZoneList(request.user)
            objs=zone.objects.filter(id__in=zonelist).order_by('parent','code').exclude(DelTag=1)
        if code:
            AllChildrenZone(code,child)
            objs=objs.exclude(pk=code)
        ll={}
        zones={}
        for o in objs:
            if o in child:continue
            zones['%s_code'%o.id]=o.code
            zones['%s_id'%o.id]=o.id
            zones['%s_name'%o.id]=o.name
            zones['%s_parent'%o.id]=o.parent
            try:
                ll[int(o.parent)].append(o.id)
            except:
                ll[int(o.parent)]=[]
                ll[int(o.parent)].append(o.id)
        usedzone=[]
        for z in objs:
            d={}
            if z.id in usedzone or z.id in child:
                continue
            usedzone.append(z.id)
            d["id"]=z.id
            d["name"]=z.name
            d["pid"]=z.parent
            d["icon"]="/media/img/zone.png"
            d["value"]=0
            d["open"]=True
            d["isParent"]=False
            if z.id in zoneids:
                d['checked'] = True
            else:
                d['checked'] = False
            #if d['pid']==0:
            #	d['icon']="/media/img/icons/home.png"
            try:
                d["children"]=get_zone_list(z.id,ll,zones,usedzone,zoneids)
            except:
                d["children"]=[]
            rt.append(d.copy())
        return getJSResponse(rt)
#     elif funid=='zonedevicetree':
#         #d={}
#         rt=[]
#         code=request.GET.get('code','')
#         child=[]
#         if code:
#             AllChildrenZone(code,child)
#         if request.user.is_superuser:
#             objs=zone.objects.all().order_by('parent','code').exclude(DelTag=1).exclude(code=code)
#         else:
#             zonelist=userZoneList(request.user)
#             objs=zone.objects.filter(id__in=zonelist).order_by('parent','code').exclude(DelTag=1).exclude(code=code)
#         ll={}
#         zones={}
#         for o in objs:
#             if o in child:continue
#             zones['%s_code'%o.id]=o.code
#             zones['%s_id'%o.id]=o.id
#             zones['%s_name'%o.id]=o.name
#             zones['%s_parent'%o.id]=o.parent
#             try:
#                 ll[int(o.parent)].append(o.id)
#             except:
#                 ll[int(o.parent)]=[]
#                 ll[int(o.parent)].append(o.id)
#         usedzone=[]
#         for z in objs:
#             d={}
#             if z.id in usedzone or z in child:
#                 continue
#             usedzone.append(z.id)
#             d["id"]=z.id
#             d["name"]=z.name
#             d["pid"]=z.parent
#             d["value"]=0
#             d["open"]=True
#             d["isParent"]=False
#             d["type"]="zone"
#             d['icon']="/media/img/dept.png"
#             #if d['pid']==0:
#             #	d['icon']="/media/img/icons/home.png"
#             try:
#                 d["children"]=get_zone_device_list(z.id,ll,zones,usedzone)
#             except:
#                 d["children"]=[]
#             snObj=IclockZone.objects.filter(zone = z.id ).values("SN")
#             iclocks=iclock.objects.filter(SN__in=snObj)
#             #print iclocks
#             for dev in iclocks:
#                 dd={}
#                 dd["id"]=z.id
#                 dd["name"]=u'%s(%s)'%(dev.Alias or '',dev.SN)
#                 dd["pid"]=z.parent
#                 dd["value"]=dev.SN
#                 dd["open"]=True
#                 dd["isParent"]=False
#                 dd["type"]="device"
#                 dd['icon']="/media/img/clock.png"
#                 d["children"].append(dd.copy())
# #
#             rt.append(d.copy())
#         return getJSResponse(rt)
    elif funid=='combopen':
        d={}
        re=[]
        objs=combopen.objects.all().order_by('id')
        for t in objs:
            dd={}
            dd['id']=t.id
            dd['name']=t.name
            dd['count']=combopen_emp.objects.filter(combopen=t).count()
            re.append(dd.copy())

        d['combopen']=re
        rt=[]
        key=int(request.GET.get('key','-1'))
        objs=combopen_comb.objects.filter(combopen_door=key).order_by('sort')
        for t in objs:
            dd={}
            dd['combid']=t.combopen_id
            dd['opennumber']=t.opener_number
            rt.append(dd.copy())
        d['comb']=rt
        return getJSResponse(d)
    elif funid=='emp_door':
        UserID=request.GET.get('id')
        sord=request.POST.get('sord')
        objs=level_emp.objects.filter(UserID=UserID).values('level')
        if sord=='desc':
            doors=level_door.objects.filter(level__in=objs).distinct().order_by('-door_id')
        else:
            doors=level_door.objects.filter(level__in=objs).distinct().order_by('door_id')
        rt=[]
        for t in doors:
            dd={}
            dd['id']=t.door_id
            dd['door_name']=t.door.door_name
            dd['door_no']=t.door.door_no
            dd['device']=u'%s'%t.door.device
            rt.append(dd.copy())
        return getJSResponse(rt)
    elif funid=='door_emp':
        doorID=request.GET.get('id')
        sord=request.POST.get('sord')
        objs=level_door.objects.filter(door=doorID).values('level')
        if sord=='desc':
            emps=level_emp.objects.filter(level__in=objs).exclude(UserID__OffDuty=1).exclude(UserID__DelTag=1).distinct().order_by('-UserID__PIN')
        else:
            emps=level_emp.objects.filter(level__in=objs).exclude(UserID__OffDuty=1).exclude(UserID__DelTag=1).distinct().order_by('UserID__PIN')
        rt=[]
        for emp in emps:
            dd={}
            if emp.UserID:
                dd['id']=emp.UserID.id
                dd['PIN']=emp.UserID.PIN
                dd['EName']=emp.UserID.EName or ''
                dd['Gender']=u'%s'%(emp.UserID.get_Gender_display() or '')
                dd['DeptName']=emp.UserID.Dept().DeptName
                dd['Card']=u'%s'%(emp.UserID.Card or '')
                rt.append(dd.copy())
        return getJSResponse(rt)


def get_zone_list(key,ll,zones,usedzone,zoneids):
    d=[]
    dd={}
    for o in ll[key]:
        if zones['%s_id'%o] in usedzone:
            continue
        usedzone.append(zones['%s_id'%o])
        dd["id"]=zones['%s_id'%o]
        dd["name"]=zones['%s_name'%o]
        dd["pid"]=zones['%s_parent'%o]
        dd["value"]=zones['%s_code'%o]
        dd["open"]=True
        dd["isParent"]=False
        if o in zoneids:
            dd['checked'] = True
        else:
            dd['checked'] = False
        dd['icon'] = '/media/img/zone.png'
        try:
            dd["children"]=get_zone_list(o,ll,zones,usedzone,zoneids)
        except:
            dd["children"]=[]
        d.append(dd.copy())
    return d

def get_zone_device_list(key,ll,zones,usedzone):
    d=[]
    dd={}
    for o in ll[key]:
        if zones['%s_id'%o] in usedzone:
            continue
        usedzone.append(zones['%s_id'%o])
        dd["id"]=zones['%s_id'%o]
        dd["name"]=zones['%s_name'%o]
        dd["pid"]=zones['%s_parent'%o]
        dd["value"]=zones['%s_code'%o]
        dd["open"]=False
        dd["isParent"]=False
        dd["type"]="zone"
        dd['icon']="/media/img/dept.png"
        try:
            dd["children"]=get_zone_device_list(o,ll,zones,usedzone)
        except:
            dd["children"]=[]
        
        sn_list=IclockZone.objects.filter(zone = zones['%s_id'%o] ).values_list("SN",flat=True)
        iclocks=iclock.objects.filter(SN__in=sn_list)
        for dev in iclocks:
            dc={}
            dc["id"]=zones['%s_id'%o]
            dc["name"]=u'%s(%s)'%(dev.Alias or '',dev.SN)
            dc["pid"]=zones['%s_parent'%o]
            dc["value"]=dev.SN
            dc["open"]=True
            dc["isParent"]=False
            dc["type"]="device"
            dc['icon']="/media/img/clock.png"
            dd["children"].append(dc.copy())
        
        d.append(dd.copy())
    return d


def get_devices_of_tcp(request):
    """获取非PUSH设备，iclock中ProductType=15为非PUSH设备,TCP/IP和RS485的区分方法是iclock中的IPAddress为IP地址时是TCP/IP,为2:COM1的格式时为RS485"""
    #from mysite.iclock.iutils import getoptionsAttParam
    from mysite.core.zkmimi import check_sn_valid
    ret=[]
    d={}
    #ll=getoptionsAttParam()
    s = request.GET.get('s')
    e = request.GET.get('e')
    objs=iclock.objects.filter(ProductType=15).exclude(DelTag=1).exclude(State=0).order_by("pk").only('SN','IPAddress')
    if s and e:
        objs = objs[int(s)-1:int(e)]
    for t in objs:
        if not t.IPAddress:
            if settings.DEBUG:
                print ('--------- SN=%s, No IP address.' % t.SN)
            continue
        re = check_sn_valid(t.SN)
        dev=getDevice(t.SN)
        if re[1] == 1:
            if not re[0]:
                dev.LastActivity=None
                cache.set("iclock_" + t.SN, dev)
                if settings.DEBUG:
                    print ('----------- SN=%s, Unauthorized, Please confirm the authorization document.' % t.SN)
                continue
        d['SN']=t.SN
        d['Style']=dev.Style if hasattr(dev,'Style') else ''
        d['IPAddress']=t.IPAddress
        d['Password']=dev.COMKey if hasattr(dev,'COMKey') else ''
        d['CommType']='TCP'
        # if (hasattr(dev, 'ReaderEncryptFunOn') and str(dev.ReaderEncryptFunOn) == '1'):
        d['ReaderEncryptFunOn'] = 1  #因pull部分固件不兼容(门状态有的传16进制，有的传10进制)，pushserver获取实时记录，改为GetRTLogExt接口
        if ':' in t.IPAddress:
            d['CommType']='RS485'
            ls=t.IPAddress.split(':')
            d['DEVID']=ls[0]
            d['PORT']=ls[1]
        ret.append(d.copy())
    return getJSResponse(ret)


# def data_records(request):
#     from mysite.iclock.jqgrid import JqGrid
#     from django.template import loader
#
#     pgte=request.GET.get('TTime__gte','')
#     plt=request.GET.get('TTime__lt','')
#     evtno=request.GET.get('event_no','')
#     evtpoint=request.GET.get('event_point','')
#     pzones=request.GET.get('zones','')
#     pdevs=request.GET.get('devs','')
#     pdepts=request.GET.get('depts','')
#
#     startdate=datetime.datetime.strptime(pgte,"%Y-%m-%d")
#     enddate=datetime.datetime.strptime(plt,"%Y-%m-%d")+ datetime.timedelta(days=1)
#     print startdate, enddate
#     qs=records.objects.filter(TTime__gte=startdate,TTime__lt=enddate)
#     if evtno:
#         qs = qs.filter(event_no=evtno)
#     if pdepts:
#         deptids=pdepts.split(',')
#         emps=employee.objects.filter(OffDuty__lt=1).filter(DeptID__in=deptids).values_list("PIN",flat=True)
#         qs = qs.filter(pin__in = emps[0])
#     if evtpoint:
#         ep = evtpoint.split('-')
#         qs = qs.filter(SN=ep[0],event_point_name=ep[1])
#     else:
#         sn_list = []
#         if pdevs:
#             for sn in pdevs.split(','):
#                 sn_list.append(sn)
#         if pzones:
#             devs=IclockZone.objects.filter(zone__in = pzones.split(',')).values_list("SN",flat=True)
#             for dev in devs:
#                 sn_list.append(dev)
#         if sn_list:
#             qs = qs.filter(SN__in = sn_list)
#     jqGrid=JqGrid(request, queryset=qs)
#     cc=jqGrid.get_json(qs)
#     tmpFile='acc/records_list.js'
#     t=loader.get_template(tmpFile)
#     try:
#         cc['user']=request.user
#         rows=t.render(cc)
#     except Exception as e:
#         print "DataList=====",e
#         rows='[]'
#         pass
#     try:
#         t_r="{"+""""page":"""+str(cc['page'])+","+""""total":"""+str(cc['total'])+","+""""records":"""+str(cc['records'])+","+""""rows":"""+rows+"""}"""
#     except Exception as e:
#         print ("===========",e)
#
#     return getJSResponse(t_r)
#
