from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from importlib import import_module
from django.conf import settings
from mysite.base.models import GetParamValue
from django.contrib.sessions.models import Session
from django.shortcuts import redirect


class UserRestrictMiddleware(MiddlewareMixin):
    """
    实现单一登录（后者挤掉前者）
    """
    def process_request(self, request):
        """
        Checks if different session exists for user and deletes it.
        """
        if GetParamValue("opt_basic_user_restrict", '0')=='0':
            return 

        if request.user.is_authenticated and request.user.username != 'employee':
            cache_timeout = 86400
            cache_key = "%s_user_pk_%s_restrict" % (settings.UNIT, request.user.pk)
            cache_value = cache.get(cache_key)
            if cache_value is not None:
                #缓存中已记录user的session信息，检查此次登录是否同个session, 若不同，删除旧的session
                if request.session.session_key != cache_value:
                    engine = import_module(settings.SESSION_ENGINE)
                    session = engine.SessionStore(session_key=cache_value)
                    session.delete()
                    cache.set(cache_key, request.session.session_key, cache_timeout)
            else:
                cache.set(cache_key, request.session.session_key, cache_timeout)


class MySessionMiddleware(MiddlewareMixin):
    """
    处理浏览器有session_key，而后端数据库里没有，异常的处理
    The request's session was deleted before the request completed. The user may have logged out in a concurrent request, for example

    该中间件是早期用于控制在线管理员数功能（强制下线），但后期管理员的限制调整为控制新增，不再使用该业务
    当前版本生产环境不会出现该问题，测试环境出现的话，清除下缓存
    """
    def process_request(self, request):
        if request.path == settings.LOGIN_URL:
            return None
        else:
            session_key = request.COOKIES.get(settings.SESSION_COOKIE_NAME)
            if session_key:
                obj=Session.objects.filter(session_key=session_key).first()
                if not obj:
                    return redirect(settings.LOGIN_URL)


class ForceUserLogoutMiddleware(MiddlewareMixin):
    """
    实现强制用户注销
    """

    def process_request(self, request):
        """
        检查用户状态,用户被禁止登录或授权过期时强制注销
        """
        import datetime
        from django.contrib.auth import logout
        from mysite.base.models import Tenant
        
        if settings.MULTI_TENANT:
            obj = Tenant.objects.first()
            if obj.deltag or obj.paid_until < datetime.date.today():
                logout(request)
