# Generated by Django 4.2.11 on 2024-06-26 15:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('acc', '0026_auto_20240408_1051'),
    ]

    operations = [
        migrations.AlterField(
            model_name='acc_employee',
            name='isblacklist',
            field=models.BooleanField(blank=True, default=False, verbose_name='Open Blacklist'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='set_valid_time',
            field=models.BooleanField(blank=True, default=False, verbose_name='Set ValidTime'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='back_lock',
            field=models.BooleanField(blank=True, default=True, null=True, verbose_name='closed door lock'),
        ),
        migrations.AlterField(
            model_name='records',
            name='event_no',
            field=models.IntegerField(choices=[(-1, 'nothing'), (0, 'Normal opening'), (1, 'Punch during Passage Mode Time Zone'), (2, 'The first person opens the door (swipe the card)'), (3, 'Multiple people open the door normally'), (4, 'Emergency password opens'), (5, 'Open during Passage Mode Time Zone'), (6, 'trigger linkage event'), (7, 'Cancel the alarm'), (8, 'Open the door remotely'), (9, 'remote closing'), (10, 'Disable the usual opening time of the day'), (11, 'Enable the usual opening time of the day'), (12, 'Turn on auxiliary output'), (13, 'Turn off auxiliary output'), (14, 'Normally open the door by fingerprint'), (15, 'Multiple people open the door (by fingerprint)'), (16, 'Filling fingerprints during the normally open time period'), (17, 'Kajia Fingerprint Opens the Door'), (18, 'First card opens (by fingerprint)'), (19, 'First card opening (kajia fingerprint)'), (20, 'Swipe card interval is too short'), (21, 'Do not valid time period'), (22, 'Illegal time period'), (23, 'Unauthorized access'), (24, 'anti-submarine'), (25, 'Interlock failed'), (26, 'Multiple verification waits'), (27, 'People are not registered'), (28, 'door open timeout'), (29, 'The card has expired'), (30, 'wrong password'), (31, 'The fingerprint interval is too short'), (32, 'Verificando multi-usuario con huellas'), (33, 'Expired validity period'), (34, 'People are not registered'), (35, 'Do not valid time period'), (36, 'The door is not valid (press the exit button)'), (37, 'Unable to close the door during the normal opening period'), (38, 'The card has been lost'), (39, 'blacklist'), (41, 'Verification error'), (42, 'The Wiegand Card format is wrong'), (44, 'Anti-submarine verification failed'), (45, 'Anti-submarine verification timeout'), (47, 'Failed to send command'), (48, 'Multiple people fail to open the door'), (49, 'Do not valid time period'), (50, 'Swipe card interval is too short'), (51, 'Multi-person verification'), (52, 'Multiple authentication failed'), (53, 'People are not registered'), (54, 'Battery voltage is too low'), (55, 'Replace the battery immediately'), (56, 'Illegal operation'), (57, 'Backup power supply'), (58, 'Normally open alarm'), (59, 'Illegal management'), (60, 'The door is locked'), (61, 'Repeat verification'), (62, 'Disable Users'), (63, 'The door has been locked'), (64, 'The door button is not in the time period'), (65, 'Auxiliary input is not within the time period'), (74, 'The QR code has expired'), (100, 'tamper alarm'), (101, 'Duress password to open the door'), (102, 'The door was accidentally opened'), (104, 'Invalid card continuously swipe 5 times'), (110, 'Offline alarm for reading head'), (114, 'Circuit detection, fire alarm input disconnected'), (115, 'Circuit detection, fire alarm input short circuit'), (116, 'Circuit detection, auxiliary input disconnected'), (117, 'Circuit detection, auxiliary input short circuit'), (118, 'Circuit detection, exit switch disconnected'), (119, 'Circuit detection, short circuit of the exit switch'), (120, 'Circuit detection, door magnetic disconnection'), (121, 'Circuit detection, gate magnetic short circuit'), (200, 'The door is open'), (201, 'The door is closed'), (202, 'Going out the door to open the door'), (204, 'The end of the normally open time period'), (205, 'Remote opening is always open'), (206, 'Device startup'), (207, 'Password opens'), (208, 'Super user opens the door'), (209, 'Trigger exit button (locked)'), (215, 'First card opening (password)'), (216, 'In the normally open time period (by password)'), (220, 'Auxiliary input point disconnected'), (221, 'Auxiliary input point short circuit'), (225, 'The input point is back to normal'), (226, 'Input point alarm'), (222, 'Anti-submarine verification succeeded'), (223, 'anti-submarine verification'), (224, 'Press the doorbell'), (233, 'Activate Lockdown'), (232, 'Operation Success'), (234, 'Deactivate Lockdown'), (237, 'Reading Head Online'), (239, 'Call Request'), (240, 'Cancel Call'), (243, 'Fire alarm input disconnected'), (244, 'Fire alarm input short circuit'), (300, 'Trigger Global Linkage'), (500, 'Global Anti-Passback(logical)'), (4014, 'Fire input signal disconnected'), (4015, 'The door is online'), (5023, 'Restricted fire protection status'), (6011, 'The door is offline'), (6012, 'Door dismantling alarm'), (6013, 'Fire input signal triggered, door open normally open'), (6015, 'Reset the power supply of the expansion device'), (6016, 'Restore the factory settings of this machine')], null=True, verbose_name='Event Description'),
        ),
    ]
