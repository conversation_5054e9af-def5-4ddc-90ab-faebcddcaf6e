#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import string
from django.contrib import auth
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.template import loader, Context, RequestContext
from django.conf import settings
from django.contrib.auth.decorators import permission_required, login_required
from mysite.iclock.models import *
from mysite.acc.models import *
#from mysite.iclock.datasproc import *
from django.core.paginator import Paginator
#from mysite.iclock.datas import *
#from mysite.core.menu import *
from mysite.core.zktools import *
from mysite.iclock.models import *

import sys
from mysite.core.zkcmdproc import *


@login_required
def index(request):
	if request.method=='GET':
		tmpFile='acc/'+'door_permission.html'
		tmpFile=request.GET.get('t', tmpFile)
		cc={}


		settings.PAGE_LIMIT=GetParamValue('opt_users_page_limit','30',str(request.user.id))
		limit= int(request.GET.get('l', settings.PAGE_LIMIT))
		colmodels= [
				{'name':'id','hidden':True},
				{'name':'door_no','index':'','width':60,'label':u"%s"%(AccDoor._meta.get_field('door_no').verbose_name)},
				{'name':'door_name','index':'','width':150,'label':u"%s"%(AccDoor._meta.get_field('door_name').verbose_name)},
				{'name':'device','index':'','width':120,'label':u"%s"%(AccDoor._meta.get_field('device').verbose_name)},
                {'name':'device_sn','index':'','width':150,'label':u"%s"%(_(u'serial number'))}
			]

		colmodels_level_door = [
				{'name': 'id', 'hidden': True},
				{'name': 'name', 'width': 80, 'label': u"%s"%(level._meta.get_field('name').verbose_name)},
				{'name': 'TimeZone', 'sortable': False, 'width': 120, 'label': u"%s"%(level._meta.get_field('timeseg').verbose_name)},
				{'name': 'doors', 'sortable': False, 'align': 'center', 'width': 40,'label': u"%s"%(_(u'Control gate number'))},
				{'name': 'emps', 'sortable': False, 'align': 'center', 'width': 80,'label': u"%s"%(_(u'Number of related personnel'))},
			]

		cc['limit']=limit
		cc['colModel']=dumps1(colmodels)
		cc['colmodels_level_door'] = dumps1(colmodels_level_door)
		request.model=level


		return render(request,tmpFile,cc)#render(tmpFile,cc,RequestContext(request, {}))



def get_door_level(request):
	#print request.POST
	id=request.GET.get('id')
	limit= int(request.POST.get('rows', 0))
	try:
		offset = int(request.POST.get('page', 1))
	except:
		offset=1
	door_level  = level_door.objects.filter(door = id).values('level')#获取door 的 level
	objs = level.objects.filter(id__in=door_level).order_by('id')

	p=Paginator(objs, limit)
	iCount=p.count
	if iCount<(offset-1)*limit:
		offset=1
	page_count=p.num_pages
	pp=p.page(offset)
	objs=pp.object_list
	re=[]
	Result={}
	Result['datas']=re
	for t in objs:
		d={}
		d['id']=t.id
		d['name']=t.name
		d['TimeZone'] =t.timeseg.Name
		d['doors'] =t.doorCount()
		d['emps']=t.empCount()
		
		re.append(d.copy())
	if offset>page_count:offset=page_count
	item_count =iCount
	Result['item_count']=item_count
	Result['page']=offset
	Result['limit']=limit
	Result['from']=(offset-1)*limit+1
	Result['page_count']=page_count
	Result['datas']=re
	rs="{"+""""page":"""+str(Result['page'])+","+""""total":"""+str(Result['page_count'])+","+""""records":"""+str(Result['item_count'])+","+""""rows":"""+dumps(Result['datas'])+"""}"""
	return getJSResponse(rs)


def get_door(request):
	# print request.POST
	id = request.GET.get('id')
	limit = int(request.POST.get('rows', 0))
	try:
		offset = int(request.POST.get('page', 1))
	except:
		offset = 1
	door_ids = level_door.objects.filter(level=id).values('door')  # 获取door 的 id
	objs = AccDoor.objects.filter(id__in=door_ids).order_by('id')

	p = Paginator(objs, limit)
	iCount = p.count
	if iCount < (offset - 1) * limit:
		offset = 1
	page_count = p.num_pages
	pp = p.page(offset)
	objs = pp.object_list
	re = []
	Result = {}
	Result['datas'] = re
	for t in objs:
		d = {}
		d['id'] = t.id
		d['door_no'] = t.door_no
		d['door_name'] = t.door_name
		d['device'] = iclock.objects.get(SN = t.device_id).DeviceName
		# d['action']=''
		re.append(d.copy())
	if offset > page_count: offset = page_count
	item_count = iCount
	Result['item_count'] = item_count
	Result['page'] = offset
	Result['limit'] = limit
	Result['from'] = (offset - 1) * limit + 1
	Result['page_count'] = page_count
	Result['datas'] = re
	rs = "{" + """"page":""" + str(Result['page']) + "," + """"total":""" + str(Result['page_count']) + "," + """"records":""" + str(Result['item_count']) + "," + """"rows":""" + dumps(Result['datas']) + """}"""
	return getJSResponse(rs)
