## 会议模块
### 会议模块-补签申请功能点新增补签申请
#### 会议模块补签申请功能点RAG检索锚点
<!-- RAG检索锚点: 新增补签申请、补签申请、补记录申请、考勤补签、打卡补签 -->

#### 新增补签申请功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示补签表单]
    D --> E[选择补签日期]
    E --> F[选择补签类型]
    F --> G[设置补签时间]
    G --> H[填写补签原因]
    H --> I[验证申请数据]
    I --> J{数据验证}
    J -->|验证失败| K[显示错误信息]
    J -->|验证通过| L[检查补签限制]
    L --> M{是否超出限制}
    M -->|超出限制| N[提示超出次数限制]
    M -->|未超出| O[检查时间有效性]
    O --> P{时间是否有效}
    P -->|时间无效| Q[提示时间错误]
    P -->|时间有效| R[获取审批流程]
    R --> S[保存补签记录]
    S --> T[发送审批通知]
    T --> U[记录操作日志]
    U --> V[返回成功信息]
    K --> E
    N --> E
    Q --> G
    C --> W[结束]
    V --> W
```
#### 新增补签申请功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增补签申请业务流程。
2. 权限验证：验证当前用户是否具有补签申请权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行补签申请操作
3. 补签表单显示：
   - 显示补签申请表单界面
   - 提供必填和可选字段的输入控件
   - 显示补签规则和限制说明
4. 补签日期选择：
   - 选择需要补签的具体日期
   - 通常限制在一定时间范围内（如30天内）
   - 不能选择未来的日期进行补签
5. 补签类型选择：
   - 上班打卡（CHECKTYPE='I'）：补签上班时间
   - 下班打卡（CHECKTYPE='O'）：补签下班时间
   - 外出打卡（CHECKTYPE='i'）：补签外出时间
   - 返回打卡（CHECKTYPE='o'）：补签返回时间
   - 加班上班（CHECKTYPE='V'）：补签加班开始时间
   - 加班下班（CHECKTYPE='v'）：补签加班结束时间
6. 补签时间设置：
   - 设置具体的补签时间点（CHECKTIME字段）
   - 时间必须符合逻辑性（如上班时间早于下班时间）
   - 不能设置大于当前时间的补签时间
7. 补签原因填写：
   - 填写补签申请的具体原因（YUYIN字段）
   - 详细说明忘记打卡的原因和情况
   - 提供必要的证明和依据
8. 申请数据验证：
   - 验证必填字段是否完整
   - 检查时间格式和逻辑的正确性
   - 验证补签类型的合理性
9. 补签限制检查：
   - 检查用户的补签次数是否超出限制
   - 通过limit_checkforget函数验证次数限制
   - 防止恶意或频繁的补签申请
10. 时间有效性检查：
    - 验证补签时间不能大于当前时间
    - 检查补签时间是否在允许的时间范围内
    - 确保补签时间的逻辑合理性
11. 审批流程获取：
    - 根据申请人获取补签审批流程
    - 确定审批人员和审批级别
    - 设置审批流程的执行顺序
12. 补签记录保存：
    - 将补签申请保存到checkexact表
    - 设置申请状态为待审批（State=0）
    - 记录申请时间和审批流程信息
13. 审批通知发送：
    - 向审批人员发送待审批通知
    - 提供审批链接和申请详情
    - 通过系统消息或邮件方式通知
14. 操作日志记录：记录补签申请操作的详细日志。
15. 流程完成：返回申请成功信息，完成补签申请流程。

#### 新增补签申请功能点与其他模块及子模块业务关联说明
1. **与考勤管理模块的关联**：
   - 补签申请直接关联到考勤记录
   - 审批通过后会生成相应的考勤记录
   - 补签记录影响考勤统计和计算

2. **与人员管理模块的关联**：
   - 补签申请关联到具体的员工信息
   - 员工的部门信息影响审批流程
   - 员工的考勤规则影响补签限制

3. **与审批流程模块的关联**：
   - 补签申请需要按照配置的流程进行审批
   - 审批结果决定补签记录的有效性
   - 支持多级审批和流程自定义

4. **与通知系统的关联**：
   - 申请提交后需要通知审批人员
   - 审批结果需要通知申请人
   - 重要补签需要通知相关管理人员

5. **与系统配置模块的关联**：
   - 补签次数限制由系统配置决定
   - 补签时间范围受系统参数控制
   - 审批流程配置影响审批路径

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 会议模块
### 会议模块-补签申请功能点删除补签申请
#### 会议模块补签申请功能点RAG检索锚点
<!-- RAG检索锚点: 删除补签申请、撤销补签申请、补签申请删除、取消补签 -->

#### 删除补签申请功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的申请]
    D --> E[检查申请状态]
    E --> F{申请状态检查}
    F -->|已审批| G[提示无法删除]
    F -->|待审批| H[确认删除操作]
    H --> I{用户确认}
    I -->|取消删除| J[返回申请列表]
    I -->|确认删除| K[执行软删除]
    K --> L[更新申请状态]
    L --> M[清理关联数据]
    M --> N[发送取消通知]
    N --> O[记录操作日志]
    O --> P[返回成功信息]
    C --> Q[结束]
    G --> Q
    J --> Q
    P --> Q
```
#### 删除补签申请功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除补签申请业务流程。
2. 权限验证：验证当前用户是否具有删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 只有申请人本人或管理员可以删除申请
3. 申请选择：
   - 从补签申请列表中选择要删除的申请
   - 显示申请的基本信息供确认
   - 支持单个申请的删除操作
4. 申请状态检查：
   - 检查申请的当前审批状态
   - 验证申请是否可以被删除
   - 已审批通过的申请通常不允许删除
5. 状态验证：
   - 待审批状态（State=0）：允许删除
   - 已审批状态（State=2）：不允许删除
   - 已拒绝状态（State=3）：允许删除
6. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 要求用户明确确认删除意图
7. 软删除执行：
   - 设置申请状态为已删除（State=3）
   - 保留申请数据用于历史记录
   - 避免物理删除造成的数据丢失
8. 申请状态更新：
   - 更新checkexact表中的状态字段
   - 设置删除时间戳
   - 标记申请为无效状态
9. 关联数据清理：
   - 清理审批流程中的相关记录
   - 更新审批待办事项列表
   - 释放相关系统资源
10. 取消通知发送：
    - 通知相关审批人员申请已取消
    - 更新审批待办事项列表
    - 清理相关的系统通知
11. 操作日志记录：记录申请删除操作的详细日志。
12. 流程完成：返回删除成功信息，完成申请删除流程。

#### 删除补签申请功能点与其他模块及子模块业务关联说明
1. **与审批流程模块的关联**：
   - 删除申请需要清理审批流程中的记录
   - 通知审批人员申请已取消
   - 更新审批待办事项列表

2. **与考勤管理模块的关联**：
   - 删除申请不影响已生效的考勤记录
   - 未生效的补签计划需要清理
   - 考勤统计数据需要相应调整

3. **与通知系统的关联**：
   - 申请删除需要通知相关人员
   - 取消之前发送的审批通知
   - 清理系统待办事项

4. **与系统日志模块的关联**：
   - 所有删除操作都需要记录详细日志
   - 日志用于审计和问题排查
   - 支持数据恢复和回滚操作

5. **与权限管理模块的关联**：
   - 删除权限控制操作范围
   - 不同用户的删除权限不同
   - 管理员可以删除他人的申请

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
