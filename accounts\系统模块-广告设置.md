## 系统模块
### 系统模块-广告设置功能点新增广告
#### 系统模块广告设置功能点RAG检索锚点
<!-- RAG检索锚点: 新增广告、广告设置、广告管理、广告配置、信息屏广告 -->

#### 新增广告功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示新增表单]
    D --> E[填写广告信息]
    E --> F[上传广告文件]
    F --> G[设置播放参数]
    G --> H[验证表单数据]
    H --> I{数据验证}
    I -->|验证失败| J[显示错误信息]
    I -->|验证通过| K[检查文件格式]
    K --> L{文件格式是否支持}
    L -->|不支持| M[提示文件格式错误]
    L -->|支持| N[保存广告文件]
    N --> O[保存广告信息]
    O --> P[同步到设备]
    P --> Q[记录操作日志]
    Q --> R[返回成功信息]
    J --> E
    M --> F
    C --> S[结束]
    R --> S
```
#### 新增广告功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增广告业务流程。
2. 权限验证：验证当前用户是否具有广告管理权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行新增操作
3. 新增表单显示：
   - 显示广告信息录入表单
   - 提供必填和可选字段的输入界面
   - 显示文件上传和播放参数设置界面
4. 广告信息填写：
   - 广告名称（name）：必填，广告的标识名称
   - 广告描述：可选，广告的详细说明
   - 广告类型：设置广告的分类类型
   - 有效期设置：配置广告的生效时间范围
5. 广告文件上传：
   - 支持上传图片、视频等广告文件
   - 文件大小限制验证
   - 文件格式验证（支持jpg、png、mp4等格式）
   - 文件存储到指定目录
6. 播放参数设置：
   - 播放时长：设置广告的播放时间
   - 播放顺序：设置广告的播放优先级
   - 播放设备：选择要播放广告的设备
   - 播放时段：设置广告的播放时间段
7. 表单数据验证：
   - 验证必填字段是否已填写
   - 检查广告名称长度是否符合要求
   - 验证播放参数的合法性
8. 文件格式检查：
   - 验证上传文件的格式是否支持
   - 检查文件的完整性和有效性
   - 确保文件可以正常播放
9. 广告文件保存：
   - 将上传的文件保存到服务器指定目录
   - 生成文件的唯一标识和访问路径
   - 记录文件的基本信息和属性
10. 广告信息保存：
    - 将广告信息保存到advertisement表中
    - 设置广告的状态和配置参数
    - 确保数据的完整性和一致性
11. 设备同步：
    - 将广告信息同步到相关的信息屏设备
    - 调用设备通信接口推送广告内容
    - 确保设备能够正常播放广告
12. 操作日志记录：记录广告创建操作的详细日志。
13. 流程完成：返回操作成功信息，完成广告创建流程。

#### 新增广告功能点与其他模块及子模块业务关联说明
1. **与设备管理模块的关联**：
   - 广告需要同步到相关的信息屏设备
   - 设备状态影响广告的播放效果
   - 设备类型决定广告的格式要求

2. **与文件管理模块的关联**：
   - 广告文件需要统一的文件管理
   - 文件存储和访问权限控制
   - 文件备份和恢复策略

3. **与系统配置模块的关联**：
   - 广告功能的开启受系统配置影响
   - 广告文件大小限制由系统配置决定
   - 广告播放参数受系统设置影响

4. **与权限管理模块的关联**：
   - 广告管理需要相应的管理权限
   - 不同用户对广告的操作权限不同
   - 广告内容审核需要特殊权限

5. **与网络管理模块的关联**：
   - 广告同步需要网络连通性支持
   - 网络带宽影响广告文件的传输
   - 网络安全策略影响广告内容分发

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 系统模块
### 系统模块-广告设置功能点删除广告
#### 系统模块广告设置功能点RAG检索锚点
<!-- RAG检索锚点: 删除广告、移除广告、广告删除、广告管理删除 -->

#### 删除广告功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的广告]
    D --> E[检查广告使用状态]
    E --> F{广告是否正在播放}
    F -->|正在播放| G[提示广告使用中]
    F -->|未播放| H[确认删除操作]
    H --> I{用户确认}
    I -->|取消删除| J[返回广告列表]
    I -->|确认删除| K[从设备清除广告]
    K --> L[删除广告文件]
    L --> M[删除广告记录]
    M --> N[记录操作日志]
    N --> O[返回成功信息]
    C --> P[结束]
    G --> P
    J --> P
    O --> P
```
#### 删除广告功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除广告业务流程。
2. 权限验证：验证当前用户是否具有广告删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行删除操作
3. 广告选择：
   - 从广告列表中选择要删除的广告
   - 显示广告的基本信息供确认
   - 支持单个广告的删除操作
4. 广告使用状态检查：
   - 检查广告是否正在设备上播放
   - 验证广告是否有未完成的播放任务
   - 确保删除操作不会影响正在进行的播放
5. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 要求用户明确确认删除意图
6. 设备广告清除：
   - 从相关设备中清除广告内容
   - 调用设备通信接口删除广告文件
   - 确保设备不再播放已删除的广告
7. 广告文件删除：
   - 删除服务器上的广告文件
   - 清理相关的文件缓存
   - 释放文件存储空间
8. 广告记录删除：
   - 从advertisement表中删除广告记录
   - 可以采用物理删除或软删除方式
   - 保持数据的一致性
9. 操作日志记录：记录广告删除操作的详细日志。
10. 流程完成：返回删除成功信息，完成广告删除流程。

#### 删除广告功能点与其他模块及子模块业务关联说明
1. **与设备管理模块的关联**：
   - 删除广告需要从设备中清除相关内容
   - 设备状态影响广告删除的执行
   - 需要通知设备停止播放已删除广告

2. **与文件管理模块的关联**：
   - 删除广告需要清理相关文件
   - 文件删除需要权限验证
   - 文件备份策略影响删除操作

3. **与系统监控模块的关联**：
   - 广告删除操作需要监控记录
   - 异常删除操作需要告警通知
   - 广告使用统计需要更新

4. **与权限管理模块的关联**：
   - 广告删除需要相应的管理权限
   - 重要广告的删除需要特殊权限
   - 删除操作需要权限审计

5. **与系统配置模块的关联**：
   - 广告删除操作受系统权限配置限制
   - 删除操作需要记录到系统审计日志
   - 系统安全策略影响删除权限

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
