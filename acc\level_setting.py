from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required
from django.db.models import Count
from django.core.paginator import Paginator

from mysite.acc.models import *
from mysite.iclock.models import *
from mysite.core.zkcmdproc import *


@login_required
def index(request):
    """
    门禁权限-权限组设置页
    """
    if request.method == 'GET':
        tmp_file = 'acc/level_setting.html'
        col_models = [
            {'name': 'id', 'sortable': False, 'width': 70, 'align': 'center', 'label': str(_('Privilege Group Number'))},
            {'name': 'name', 'sortable': False, 'align': 'center', 'label': str(_('Privilege Group Name'))},
            {'name': 'period', 'sortable': False, 'align': 'center', 'label': str(_('period'))},
            {'name': 'door_count', 'sortable': False, 'width': 70, 'align': 'center', 'label': str(_('Control gate number'))},
            {'name': 'is_visitor', 'sortable': False, 'width': 100, 'align': 'center', 'label': str(_('Visitor Access Control Group'))},
            {'name': 'operation', 'sortable': False, 'width': 60, 'align': 'center', 'label': str(_('Operation'))},
            # 以下列隐藏，作为二次编辑回显使用
            {'name': 'data_name', 'hidden': True},
            {'name': 'data_period', 'hidden': True},
            {'name': 'data_is_visitor', 'hidden': True},
        ]
        col_models_east = [
            {'name': 'id', 'hidden': True},
            {'name': 'door_name', 'width': 208, 'label': str(_('door name'))},
            {'name': 'device__SN', 'width': 208, 'label': str(_('devise serial number'))},
            {'name': 'device__iclockzone__zone__name', 'label': str(_('area name')), 'hidden': True}
        ]
        cc = {
            'limit': GetParamValue('opt_users_page_limit', '30', str(request.user.id)),
            'colModel': dumps1(col_models),
            'colModel_east': dumps1(col_models_east)
        }
        request.model = level

        return render(request, tmp_file, cc)


@login_required
def level_group(request):
    """
    获取权限组数据
    """
    level_name = request.GET.get('level_name', '')
    period = request.GET.get('period', '')
    kwargs = {}
    if level_name:
        kwargs['name__contains'] = level_name
    if period:
        kwargs['timeseg__Name__contains'] = period

    if request.user.is_superuser or request.user.is_allzone:
        objs = level.objects.filter(**kwargs).annotate(door_count=Count('level_door')).order_by('id')
    else:
        # 根据管理员权限过滤
        zas = ZoneAdmin.objects.filter(user=request.user).values('code')
        iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zas).values('SN')
        ld = set(level_door.objects.filter(door__device__in=iz).values_list('level_id', flat=True))
        ex_level = set(level_door.objects.filter(level__in=ld).filter(~Q(door__device__in=iz)).values_list('level_id', flat=True))
        id_list = list(ld - ex_level)

        objs = level.objects.filter(**kwargs).filter(Q(id__in=id_list) | Q(level_door__isnull=True)).annotate(
            door_count=Count('level_door')).order_by('id')

    limit = int(request.POST.get('rows', settings.PAGE_LIMIT))
    offset = int(request.POST.get('page', 1))
    p = Paginator(objs, limit)
    if p.count < (offset - 1) * limit:
        offset = 1

    rows = []
    for obj in p.page(offset):
        rows.append({
            'id': obj.id,
            'name': f'<a onClick=edit_level_group("{obj.id}") href="#" style="color: red;cursor: pointer;">{obj.name}</a>',
            'period': obj.timeseg.Name,
            'door_count': obj.door_count,
            'is_visitor': str(_('Yes')) if obj.is_visitor else str(_('No')),
            'operation': f'<a onClick=add_door("{obj.id}") style="color: red;">{_("add doors")}</a>',
            # 以下数据作为二次编辑回显使用
            'data_name': obj.name,
            'data_period': obj.timeseg.id,
            'data_is_visitor': 1 if obj.is_visitor else 0
        })

    return getJSResponse({
        'page': offset,
        'records': p.count,
        'rows': rows,
        'total': p.num_pages
    })


@login_required
def level_group_doors(request):
    """
    获取权限组控制的门
    """
    kwargs = {'level_id': request.GET.get('id')}
    door_name = request.GET.get('door_name', '')
    if door_name:
        kwargs['door__door_name__contains'] = door_name
    try:
        objs = level_door.objects.filter(**kwargs)
    except:
        objs = []

    rows = []
    for obj in objs:
        rows.append({
            'id': obj.door_id,
            'door_name': obj.door.door_name,
            'device__SN': obj.door.device_id,
            'device__iclockzone__zone__name': obj.door.device.iclockzone_set.first().zone.name
        })
    return getJSResponse({
        'page': 1,
        'records': len(rows),
        'rows': rows,
        'total': 1
    })


@login_required
def doors(request):
    """
    获取权限组可添加的门
    """
    order_by = request.POST.get('sidx', 'id')
    sord = request.POST.get('sord', 'asc')
    if sord == 'desc':
        order_by = f'-{order_by}'
    filter_kwargs = {'device__DelTag': 0}
    if request.GET.get('SN', ''):
        filter_kwargs['device__SN__contains'] = request.GET.get('SN')
    if request.GET.get('door_name'):
        filter_kwargs['door_name__contains'] = request.GET.get('door_name')
    if request.POST.get('zone_id_list', []):
        filter_kwargs['device__iclockzone__zone__id__in'] = request.POST.getlist('zone_id_list')
    doors = AccDoor.objects.filter(**filter_kwargs).values(
        'id', 'door_name', 'device__SN', 'device__iclockzone__zone__name').order_by(order_by)

    # 根据权限过滤
    if not request.user.is_superuser:
        if not request.user.is_allzone:
            za = ZoneAdmin.objects.filter(user=request.user)
            iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=za.values('code'))
        else:
            zo = zone.objects.filter(DelTag=0)
            iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zo.values('id'))
        sns = iz.values('SN')
        doors = doors.filter(device__SN__in=sns)

    limit = int(request.POST.get('rows', settings.PAGE_LIMIT))
    offset = int(request.POST.get('page', 1))
    p = Paginator(doors, limit)
    if p.count < (offset - 1) * limit:
        offset = 1

    return getJSResponse({
        'page': offset,
        'records': p.count,
        'rows': list(p.page(offset)),
        'total': p.num_pages
    })
