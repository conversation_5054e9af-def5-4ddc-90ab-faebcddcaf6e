# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2024-04-08 10:51
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0067_auto_20240408_1051'),
        ('acc', '0025_auto_20221107_1103'),
    ]

    operations = [
        migrations.CreateModel(
            name='TriggerList',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('feature_type', models.CharField(choices=[(0, 'door'), (4, 'reader')], max_length=32, null=True)),
                ('trigger', models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='Trigger')),
                ('device', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock')),
            ],
            options={
                'verbose_name': 'Trigger List',
                'verbose_name_plural': 'Trigger List',
                'default_permissions': ('browse', 'add', 'change', 'delete'),
            },
        ),
        migrations.AddField(
            model_name='accdoor',
            name='new_verify_style',
            field=models.CharField(choices=[(0, 'or'), (1, 'and')], max_length=32, null=True),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='opendoor_logic',
            field=models.IntegerField(blank=True, choices=[(0, 'or'), (1, 'and')], default=0, null=True),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='rex_button_input_mode',
            field=models.IntegerField(blank=True, choices=[(0, 'Normal input mode'), (1, 'Line detection input mode')], default=0, null=True, verbose_name='Exit switch input mode'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='rex_button_supervised_resistor',
            field=models.IntegerField(blank=True, choices=[(1, '1.2K resistance mode'), (2, '2.2K resistance mode'), (3, '4.7K resistance mode'), (4, '10K resistance mode')], default=1, null=True, verbose_name='Exit switch circuit detection resistance value'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='rex_button_type',
            field=models.IntegerField(blank=True, choices=[(0, 'nothing'), (1, 'Normally open and effective'), (1, 'Normally closed and effective')], default=0, null=True, verbose_name='Exit switch input valid type'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='sensor_input_mode',
            field=models.IntegerField(blank=True, choices=[(0, 'Normal input mode'), (1, 'Line detection input mode')], default=0, null=True, verbose_name='Gate magnetic input mode'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='sensor_supervised_resistor',
            field=models.IntegerField(blank=True, choices=[(1, '1.2K resistance mode'), (2, '2.2K resistance mode'), (3, '4.7K resistance mode'), (4, '10K resistance mode')], default=1, null=True, verbose_name='Gate magnetic input line detection resistance value'),
        ),
        migrations.AddField(
            model_name='auxin',
            name='input_mode',
            field=models.IntegerField(blank=True, choices=[(0, 'Normal input mode'), (1, 'Line detection input mode')], default=0, null=True, verbose_name='模式'),
        ),
        migrations.AddField(
            model_name='auxin',
            name='input_type',
            field=models.IntegerField(blank=True, choices=[(0, 'nothing'), (1, 'Normally open and effective'), (1, 'Normally closed and effective')], default=0, null=True, verbose_name='有效类型'),
        ),
        migrations.AddField(
            model_name='auxin',
            name='supervised_resistor',
            field=models.IntegerField(blank=True, choices=[(1, '1.2K resistance mode'), (2, '2.2K resistance mode'), (3, '4.7K resistance mode'), (4, '10K resistance mode')], default=1, null=True, verbose_name='线路检测阻值'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='opendoor_type',
            field=models.IntegerField(choices=[(0, 'automatic matching'), (1, 'fingerprint only'), (2, 'Work only number'), (3, 'password only'), (4, 'only card'), (5, 'fingerprint or password'), (6, 'card or fingerprint'), (7, 'card or password'), (8, 'Work number plus fingerprint'), (9, 'Fingerprint plus password'), (10, 'Caga fingerprint'), (11, 'Caga Password'), (12, 'Fingerprint plus password plus card'), (13, 'Work number plus fingerprint plus password'), (14, 'Work number plus fingerprint or card plus fingerprint'), (15, 'human face'), (16, 'Face plus fingerprint'), (17, 'Face plus password'), (18, 'Face plus card'), (19, 'Face plus fingerprint plus card'), (20, 'Face plus fingerprint plus password'), (21, 'Finger vein'), (22, 'Finger vein plus password'), (23, 'Finger vein plus card'), (24, 'Finger vein plus card plus password'), (25, 'Palm'), (26, 'Palm&Card'), (27, 'Palm&FACE'), (28, 'Palm&FP'), (29, 'Palm&FACE&FP'), (200, 'other')], default=6, null=True, verbose_name='Ways of identifying'),
        ),
        migrations.AlterField(
            model_name='records',
            name='event_no',
            field=models.IntegerField(choices=[(-1, 'nothing'), (0, 'Normal opening'), (1, 'Punch during Passage Mode Time Zone'), (2, 'The first person opens the door (swipe the card)'), (3, 'Multiple people open the door normally'), (4, 'Emergency password opens'), (5, 'Open during Passage Mode Time Zone'), (6, 'trigger linkage event'), (7, 'Cancel the alarm'), (8, 'Open the door remotely'), (9, 'remote closing'), (10, 'Disable the usual opening time of the day'), (11, 'Enable the usual opening time of the day'), (12, 'Turn on auxiliary output'), (13, 'Turn off auxiliary output'), (14, 'Normally open the door by fingerprint'), (15, 'Multiple people open the door (by fingerprint)'), (16, 'Filling fingerprints during the normally open time period'), (17, 'Kajia Fingerprint Opens the Door'), (18, 'First card opens (by fingerprint)'), (19, 'First card opening (kajia fingerprint)'), (20, 'Swipe card interval is too short'), (21, 'Do not valid time period'), (22, 'Illegal time period'), (23, 'Unauthorized access'), (24, 'anti-submarine'), (25, 'Interlock failed'), (26, 'Multiple verification waits'), (27, 'People are not registered'), (28, 'door open timeout'), (29, 'The card has expired'), (30, 'wrong password'), (31, 'The fingerprint interval is too short'), (32, 'Verificando multi-usuario con huellas'), (33, 'Expired validity period'), (34, 'People are not registered'), (35, 'Do not valid time period'), (36, 'The door is not valid (press the exit button)'), (37, 'Unable to close the door during the normal opening period'), (38, 'The card has been lost'), (39, 'blacklist'), (41, 'Verification error'), (42, 'The Wiegand Card format is wrong'), (44, 'Anti-submarine verification failed'), (45, 'Anti-submarine verification timeout'), (47, 'Failed to send command'), (48, 'Multiple people fail to open the door'), (49, 'Do not valid time period'), (50, 'Swipe card interval is too short'), (51, 'Multi-person verification'), (52, 'Multiple authentication failed'), (53, 'People are not registered'), (54, 'Battery voltage is too low'), (55, 'Replace the battery immediately'), (56, 'Illegal operation'), (57, 'Backup power supply'), (58, 'Normally open alarm'), (59, 'Illegal management'), (60, 'The door is locked'), (61, 'Repeat verification'), (62, 'Disable Users'), (63, 'The door has been locked'), (64, 'The door button is not in the time period'), (65, 'Auxiliary input is not within the time period'), (74, 'The QR code has expired'), (100, 'tamper alarm'), (101, 'Duress password to open the door'), (102, 'The door was accidentally opened'), (104, 'Invalid card continuously swipe 5 times'), (110, 'Offline alarm for reading head'), (114, 'Circuit detection, fire alarm input disconnected'), (115, 'Circuit detection, fire alarm input short circuit'), (116, 'Circuit detection, auxiliary input disconnected'), (117, 'Circuit detection, auxiliary input short circuit'), (118, 'Circuit detection, exit switch disconnected'), (119, 'Circuit detection, short circuit of the exit switch'), (120, 'Circuit detection, door magnetic disconnection'), (121, 'Circuit detection, gate magnetic short circuit'), (200, 'The door is open'), (201, 'The door is closed'), (202, 'Going out the door to open the door'), (204, 'The end of the normally open time period'), (205, 'Remote opening is always open'), (206, 'Device startup'), (207, 'Password opens'), (208, 'Super user opens the door'), (209, 'Trigger exit button (locked)'), (215, 'First card opening (password)'), (216, 'In the normally open time period (by password)'), (220, 'Auxiliary input point disconnected'), (221, 'Auxiliary input point short circuit'), (225, 'The input point is back to normal'), (226, 'Input point alarm'), (222, 'Anti-submarine verification succeeded'), (223, 'anti-submarine verification'), (224, 'Press the doorbell'), (233, 'Activate Lockdown'), (232, 'Operation Success'), (234, 'Deactivate Lockdown'), (237, 'Reading Head Online'), (239, 'Call Request'), (240, 'Cancel Call'), (243, 'Fire alarm input disconnected'), (244, 'Fire alarm input short circuit'), (300, 'Trigger Global Linkage'), (500, 'Global Anti-Passback(logical)'), (4014, 'Fire input signal disconnected'), (4015, 'The door is online'), (6011, 'The door is offline'), (6012, 'Door dismantling alarm'), (6013, 'Fire input signal triggered, door open normally open'), (5023, 'Restricted fire protection status')], null=True, verbose_name='Event Description'),
        ),
        migrations.AlterField(
            model_name='records',
            name='verify',
            field=models.CharField(choices=[(0, 'automatic matching'), (1, 'fingerprint only'), (2, 'Work only number'), (3, 'password only'), (4, 'only card'), (5, 'fingerprint or password'), (6, 'card or fingerprint'), (7, 'card or password'), (8, 'Work number plus fingerprint'), (9, 'Fingerprint plus password'), (10, 'Caga fingerprint'), (11, 'Caga Password'), (12, 'Fingerprint plus password plus card'), (13, 'Work number plus fingerprint plus password'), (14, 'Work number plus fingerprint or card plus fingerprint'), (15, 'human face'), (16, 'Face plus fingerprint'), (17, 'Face plus password'), (18, 'Face plus card'), (19, 'Face plus fingerprint plus card'), (20, 'Face plus fingerprint plus password'), (21, 'Finger vein'), (22, 'Finger vein plus password'), (23, 'Finger vein plus card'), (24, 'Finger vein plus card plus password'), (25, 'Palm'), (26, 'Palm&Card'), (27, 'Palm&FACE'), (28, 'Palm&FP'), (29, 'Palm&FACE&FP'), (200, 'other')], default=200, max_length=32, null=True, verbose_name='verification'),
        ),
        migrations.AddField(
            model_name='antipassback',
            name='initial_triggerlist',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='apb_itrigger', to='acc.TriggerList'),
        ),
        migrations.AddField(
            model_name='antipassback',
            name='target_triggerlist',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='apb_ttrigger', to='acc.TriggerList'),
        ),
        migrations.AddField(
            model_name='interlock',
            name='initial_triggerlist',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='il_itrigger', to='acc.TriggerList'),
        ),
        migrations.AddField(
            model_name='interlock',
            name='target_triggerlist',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='il_ttrigger', to='acc.TriggerList'),
        ),
    ]
