# -*- coding: utf-8 -*-
# Generated by Django 1.11.5 on 2017-10-01 14:53
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='acc_employee',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('set_valid_time', models.BooleanField(default=False, verbose_name='\u8bbe\u7f6e\u6709\u6548\u65f6\u95f4')),
                ('acc_startdate', models.DateTimeField(blank=True, null=True, verbose_name='\u5f00\u59cb\u65f6\u95f4')),
                ('acc_enddate', models.DateTimeField(blank=True, null=True, verbose_name='\u7ed3\u675f\u65f6\u95f4')),
                ('acc_super_auth', models.SmallIntegerField(blank=True, choices=[(0, '\u5426'), (15, '\u662f')], default=0, null=True, verbose_name='\u8d85\u7ea7\u7528\u6237')),
                ('isblacklist', models.BooleanField(default=False, verbose_name='\u662f\u5426\u9ed1\u540d\u5355')),
            ],
            options={
                'db_table': 'acc_employee',
                'verbose_name': '\u95e8\u7981\u8bbe\u7f6e',
                'verbose_name_plural': 'acc_employee',
            },
        ),
        migrations.CreateModel(
            name='AccDoor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('door_no', models.IntegerField(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, verbose_name='\u95e8\u7f16\u53f7')),
                ('door_name', models.CharField(default='', max_length=30, null=True, verbose_name='\u95e8\u540d\u79f0')),
                ('lock_delay', models.IntegerField(default=5, help_text='\u79d2(\u8303\u56f40-254,0\u4ee3\u8868\u5e38\u95ed)', null=True, verbose_name='\u9501\u9a71\u52a8\u65f6\u957f')),
                ('back_lock', models.BooleanField(default=True, verbose_name='\u95ed\u95e8\u56de\u9501')),
                ('door_sensor_status', models.IntegerField(choices=[(0, '\u65e0'), (1, '\u5e38\u95ed'), (2, '\u5e38\u5f00')], default=0, help_text='\u95e8\u78c1\u7c7b\u578b\u4e3a\u65e0\u65f6\uff0c\u95e8\u78c1\u5ef6\u65f6\u4e0d\u53ef\u7f16\u8f91', null=True, verbose_name='\u95e8\u78c1\u7c7b\u578b')),
                ('sensor_delay', models.IntegerField(blank=True, default=15, help_text='\u79d2(\u8303\u56f41-254),\u95e8\u78c1\u5ef6\u65f6\u9700\u5927\u4e8e\u9501\u9a71\u52a8\u65f6\u957f', null=True, verbose_name='\u95e8\u78c1\u5ef6\u65f6')),
                ('opendoor_type', models.IntegerField(choices=[(3, '\u4ec5\u5bc6\u7801'), (1, '\u4ec5\u6307\u7eb9'), (4, '\u4ec5\u5361'), (11, '\u5361\u52a0\u5bc6\u7801'), (6, '\u5361\u6216\u6307\u7eb9'), (7, '\u5361\u6216\u5bc6\u7801'), (0, '\u5361\u6216\u5bc6\u7801\u6216\u6307\u7eb9'), (10, '\u5361\u52a0\u6307\u7eb9'), (21, '\u6307\u9759\u8109'), (22, '\u6307\u9759\u8109\u548c\u5bc6\u7801'), (23, '\u6307\u9759\u8109\u548c\u5361'), (24, '\u6307\u9759\u8109\u548c\u5361\u548c\u5bc6\u7801')], default=6, null=True, verbose_name='\u9a8c\u8bc1\u65b9\u5f0f')),
                ('card_intervaltime', models.IntegerField(default=1, help_text='\u79d2(\u8303\u56f4:0-254,0\u8868\u793a\u65e0\u95f4\u9694)', null=True, verbose_name='\u5237\u5361\u95f4\u9694')),
                ('reader_type', models.IntegerField(blank=True, choices=[(1, '\u5361\u8bfb\u5934'), (2, '\u6307\u7eb9\u8bfb\u5934')], default=0, null=True, verbose_name='\u8bfb\u5934\u7c7b\u578b')),
                ('is_att', models.IntegerField(blank=True, editable=False, null=True, verbose_name='\u8003\u52e4')),
                ('force_pwd', models.CharField(blank=True, default='', help_text='(\u6700\u59278\u4f4d\u6574\u6570)', max_length=18, null=True, verbose_name='\u80c1\u8feb\u5bc6\u7801')),
                ('supper_pwd', models.CharField(blank=True, default='', help_text='(\u6700\u59278\u4f4d\u6574\u6570)', max_length=18, null=True, verbose_name='\u7d27\u6025\u72b6\u6001\u5bc6\u7801')),
                ('duration_apb', models.IntegerField(blank=True, default=0, editable=False, help_text='\u5206(5-120)', null=True, verbose_name='\u5165\u53cd\u6f5c\u65f6\u957f')),
                ('global_apb', models.IntegerField(blank=True, choices=[(0, '\u5426'), (1, '\u662f')], editable=False, null=True, verbose_name='\u542f\u7528\u533a\u57df\u53cd\u6f5c')),
            ],
            options={
                'db_table': 'acc_door',
                'verbose_name': '\u95e8',
                'verbose_name_plural': '\u95e8',
            },
        ),
        migrations.CreateModel(
            name='AccDoorMap',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('left', models.CharField(blank=True, max_length=40, null=True, verbose_name='\u5de6\u8fb9\u8ddd')),
                ('top', models.CharField(blank=True, max_length=40, null=True, verbose_name='\u4e0a\u8fb9\u8ddd')),
            ],
        ),
        migrations.CreateModel(
            name='AccMap',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('map_name', models.CharField(default='', max_length=30, null=True, unique=True, verbose_name='\u5730\u56fe\u540d\u79f0')),
                ('map_path', models.CharField(blank=True, default='', max_length=30, null=True, verbose_name='\u5730\u56fe\u8def\u5f84')),
                ('width', models.FloatField(blank=True, default=0, editable=False, null=True, verbose_name='\u5bbd\u5ea6')),
                ('height', models.FloatField(blank=True, default=0, editable=False, null=True, verbose_name='\u9ad8\u5ea6')),
                ('mulriple', models.IntegerField(blank=True, default=5, editable=False, null=True, verbose_name='\u500d\u6570')),
            ],
        ),
        migrations.CreateModel(
            name='AccMap_zone',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.CreateModel(
            name='AccWiegandFmt',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('wiegand_name', models.CharField(default='', max_length=30, verbose_name='\u97e6\u6839\u5361\u683c\u5f0f\u540d\u79f0')),
                ('wiegand_count', models.IntegerField(blank=True, null=True, verbose_name='\u603b\u4f4d\u6570')),
                ('odd_start', models.IntegerField(blank=True, null=True, verbose_name='\u5947\u6821\u9a8c\u5f00\u59cb\u4f4d')),
                ('odd_count', models.IntegerField(blank=True, null=True, verbose_name='\u5947\u6821\u9a8c\u7ed3\u675f\u4f4d')),
                ('even_start', models.IntegerField(blank=True, null=True, verbose_name='\u5076\u6821\u9a8c\u5f00\u59cb\u4f4d')),
                ('even_count', models.IntegerField(blank=True, null=True, verbose_name='\u5076\u6821\u9a8c\u7ed3\u675f\u4f4d')),
                ('cid_start', models.IntegerField(blank=True, null=True, verbose_name='CID\u5f00\u59cb\u4f4d')),
                ('cid_count', models.IntegerField(blank=True, null=True, verbose_name='CID\u7ed3\u675f\u4f4d')),
                ('comp_start', models.IntegerField(blank=True, null=True, verbose_name='\u516c\u53f8\u7801\u5f00\u59cb\u4f4d')),
                ('comp_count', models.IntegerField(blank=True, null=True, verbose_name='\u516c\u53f8\u7801\u7ed3\u675f\u4f4d')),
            ],
            options={
                'db_table': 'acc_wiegandfmt',
                'verbose_name': '\u97e6\u6839\u5361\u683c\u5f0f',
                'verbose_name_plural': '\u97e6\u6839\u5361\u683c\u5f0f',
            },
        ),
        migrations.CreateModel(
            name='ACGroup',
            fields=[
                ('GroupID', models.IntegerField(choices=[(1, b'1'), (2, b'2'), (3, b'3'), (4, b'4'), (5, b'5'), (6, b'6'), (7, b'7'), (8, b'8'), (9, b'9'), (10, b'10'), (11, b'11'), (12, b'12'), (13, b'13'), (14, b'14'), (15, b'15'), (16, b'16'), (17, b'17'), (18, b'18'), (19, b'19'), (20, b'20'), (21, b'21'), (22, b'22'), (23, b'23'), (24, b'24'), (25, b'25'), (26, b'26'), (27, b'27'), (28, b'28'), (29, b'29'), (30, b'30'), (31, b'31'), (32, b'32'), (33, b'33'), (34, b'34'), (35, b'35'), (36, b'36'), (37, b'37'), (38, b'38'), (39, b'39'), (40, b'40'), (41, b'41'), (42, b'42'), (43, b'43'), (44, b'44'), (45, b'45'), (46, b'46'), (47, b'47'), (48, b'48'), (49, b'49'), (50, b'50'), (51, b'51'), (52, b'52'), (53, b'53'), (54, b'54'), (55, b'55'), (56, b'56'), (57, b'57'), (58, b'58'), (59, b'59'), (60, b'60'), (61, b'61'), (62, b'62'), (63, b'63'), (64, b'64'), (65, b'65'), (66, b'66'), (67, b'67'), (68, b'68'), (69, b'69'), (70, b'70'), (71, b'71'), (72, b'72'), (73, b'73'), (74, b'74'), (75, b'75'), (76, b'76'), (77, b'77'), (78, b'78'), (79, b'79'), (80, b'80'), (81, b'81'), (82, b'82'), (83, b'83'), (84, b'84'), (85, b'85'), (86, b'86'), (87, b'87'), (88, b'88'), (89, b'89'), (90, b'90'), (91, b'91'), (92, b'92'), (93, b'93'), (94, b'94'), (95, b'95'), (96, b'96'), (97, b'97'), (98, b'98'), (99, b'99')], primary_key=True, serialize=False, verbose_name='GroupID',db_column='groupid')),
                ('Name', models.CharField(blank=True,db_column='name', max_length=30, null=True, verbose_name='ACGroup name')),
                ('TimeZone1', models.IntegerField(blank=True,db_column='timezone1', default=0, null=True, verbose_name='TimeZone1')),
                ('TimeZone2', models.IntegerField(blank=True,db_column='timezone2', default=0, null=True, verbose_name='TimeZone2')),
                ('TimeZone3', models.IntegerField(blank=True,db_column='timezone3', default=0, null=True, verbose_name='TimeZone3')),
                ('VerifyType', models.IntegerField(db_column='verifytype',choices=[(0, 'FP/PW/RF/FACE'), (1, 'FP'), (2, 'PIN'), (3, 'PW'), (4, 'RF'), (5, 'FP/PW'), (6, 'FP/RF'), (7, 'PW/RF'), (8, 'PIN&FP'), (9, 'FP&PW'), (10, 'FP&RF'), (11, 'PW&RF'), (12, 'FP&PW&RF'), (13, 'PIN&FP&PW'), (14, 'FP&RF/PIN'), (15, 'FACE'), (16, 'FACE&FP'), (17, 'FACE&PW'), (18, 'FACE&RF'), (19, 'FACE&FP&RF'), (20, 'FACE&FP&PW')], default=0, null=True, verbose_name='VerifyType')),
                ('HolidayValid', models.SmallIntegerField(choices=[(0, 'No'), (1, 'Yes')],db_column='holidayvalid', default=0, null=True, verbose_name='HolidayValid')),
            ],
            options={
                'db_table': 'accgroup',
                'verbose_name': 'ACGroup-table',
                'verbose_name_plural': 'ACGroup-table',
            },
        ),
        migrations.CreateModel(
            name='ACUnlockComb',
            fields=[
                ('UnlockCombID', models.IntegerField(db_column='unlockcombid',choices=[(1, b'1'), (2, b'2'), (3, b'3'), (4, b'4'), (5, b'5'), (6, b'6'), (7, b'7'), (8, b'8'), (9, b'9'), (10, b'10')], primary_key=True, serialize=False, verbose_name='UnlockCombID')),
                ('Name', models.CharField(blank=True, max_length=30, null=True,db_column='name', verbose_name='ACUnlockComb name')),
                ('Group01', models.IntegerField(blank=True, null=True, verbose_name='Group1',db_column='group01')),
                ('Group02', models.IntegerField(blank=True, null=True, verbose_name='Group2',db_column='group02')),
                ('Group03', models.IntegerField(blank=True, null=True, verbose_name='Group3',db_column='group03')),
                ('Group04', models.IntegerField(blank=True, null=True, verbose_name='Group4',db_column='group04')),
                ('Group05', models.IntegerField(blank=True, null=True, verbose_name='Group5',db_column='group05')),
            ],
            options={
                'db_table': 'acunlockcomb',
                'verbose_name': 'ACUnlockComb-table',
                'verbose_name_plural': 'ACUnlockComb-table',
            },
        ),
        migrations.CreateModel(
            name='AntiPassBack',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('apb_rule', models.IntegerField(null=True, verbose_name='\u53cd\u6f5c\u6a21\u5f0f')),
            ],
            options={
                'verbose_name': '\u53cd\u6f5c\u8bbe\u7f6e',
                'verbose_name_plural': 'antiback',
            },
        ),
        migrations.CreateModel(
            name='AuxIn',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('aux_no', models.IntegerField(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, verbose_name='\u7f16\u53f7')),
                ('aux_name', models.CharField(default='', max_length=30, null=True, verbose_name='\u540d\u79f0')),
                ('printer_name', models.CharField(default='', max_length=30, null=True, verbose_name='\u4e1d\u5370\u540d\u79f0')),
                ('remark', models.CharField(blank=True, max_length=20, null=True, verbose_name='\u5907\u6ce8')),
            ],
            options={
                'verbose_name': '\u8f93\u5165',
                'verbose_name_plural': '\u8f93\u5165',
            },
        ),
        migrations.CreateModel(
            name='AuxOut',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('aux_no', models.IntegerField(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, verbose_name='\u7f16\u53f7')),
                ('aux_name', models.CharField(default='', max_length=30, null=True, verbose_name='\u540d\u79f0')),
                ('printer_name', models.CharField(default='', max_length=30, null=True, verbose_name='\u4e1d\u5370\u540d\u79f0')),
                ('remark', models.CharField(blank=True, max_length=20, null=True, verbose_name='\u5907\u6ce8')),
            ],
            options={
                'verbose_name': '\u8f93\u5165',
                'verbose_name_plural': '\u8f93\u5165',
            },
        ),
        migrations.CreateModel(
            name='combopen',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='', max_length=30, null=True, verbose_name='\u5f00\u95e8\u4eba\u5458\u7ec4\u540d\u79f0')),
                ('remark', models.CharField(blank=True, max_length=20, null=True, verbose_name='\u5907\u6ce8')),
            ],
            options={
                'verbose_name': '\u7ec4\u540d\u79f0',
                'verbose_name_plural': 'combopen',
            },
        ),
        migrations.CreateModel(
            name='combopen_comb',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('opener_number', models.IntegerField(null=True)),
                ('sort', models.IntegerField(null=True)),
            ],
            options={
                'verbose_name': '\u5f00\u95e8\u7ec4\u5408',
                'verbose_name_plural': 'combopen_comb',
            },
        ),
        migrations.CreateModel(
            name='combopen_door',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='', max_length=30, null=True, verbose_name='\u5f00\u95e8\u7ec4\u5408\u540d\u79f0')),
            ],
            options={
                'verbose_name': '\u5f00\u95e8\u7ec4\u5408\u540d\u79f0',
                'verbose_name_plural': 'combopen_door',
            },
        ),
        migrations.CreateModel(
            name='combopen_emp',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': '\u5f00\u95e8\u7ec4\u5408\u4eba\u5458',
                'verbose_name_plural': 'combopen_emp',
            },
        ),
        migrations.CreateModel(
            name='FirstOpen',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': '\u9996\u4eba\u5e38\u5f00',
                'verbose_name_plural': '\u9996\u4eba\u5e38\u5f00',
            },
        ),
        migrations.CreateModel(
            name='FirstOpen_emp',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': '\u9996\u4eba\u5e38\u5f00\u4eba\u5458',
                'verbose_name_plural': 'FirstOpen_emp',
            },
        ),
        migrations.CreateModel(
            name='IaccDevItemDefine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ItemName', models.CharField(max_length=100,db_column='itemname')),
                ('ItemType', models.CharField(max_length=20, null=True,db_column='itemtype')),
                ('ItemValue', models.TextField(max_length=102400, null=True,db_column='itemvalue')),
                ('Published', models.IntegerField(db_column='published',choices=[(0, b'NOT SHARE'), (1, b'SHARE READ'), (2, b'SHARE READ/WRITE')], default=0, null=True)),
            ],
            options={
                'verbose_name_plural': 'iaccdevitemdefine',
                'db_table': 'iaccdevitemdefine',
                'verbose_name': 'iaccdevitemdefine',
                'permissions': (('iaccMonitor_iaccdevitemdefine', 'iaccMonitor iaccdevitemdefine'), ('iaccAlarm_iaccdevitemdefine', 'iaccAlarm iaccdevitemdefine'), ('iaccUserRights_iaccdevitemdefine', 'iaccUserRights iaccdevitemdefine')),
            },
        ),
        migrations.CreateModel(
            name='IaccEmpItemDefine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ItemName', models.CharField(max_length=100,db_column='itemname')),
                ('ItemType', models.CharField(max_length=20, null=True,db_column='itemtype')),
                ('ItemValue', models.TextField(max_length=102400, null=True,db_column='itemvalue')),
                ('Published', models.IntegerField(db_column='published',choices=[(0, b'NOT SHARE'), (1, b'SHARE READ'), (2, b'SHARE READ/WRITE')], default=0, null=True)),
            ],
            options={
                'verbose_name_plural': 'iaccempitemdefine',
                'db_table': 'iaccempitemdefine',
                'verbose_name': 'iaccempitemdefine',
                'permissions': (('iaccRecordDetails_iaccempitemdefine', 'iaccRecordDetails iaccempitemdefine'), ('iaccSummaryRecord_iaccempitemdefine', 'iaccSummaryRecord iaccempitemdefine'), ('iaccEmpUserRights_iaccempitemdefine', 'iaccEmpUserRights iaccempitemdefine'), ('iaccEmpDevice_iaccempitemdefine', 'iaccEmpDevice iaccempitemdefine')),
            },
        ),
        migrations.CreateModel(
            name='IclockZone',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('iscascadecheck', models.IntegerField(blank=True, default=0, editable=False, null=True)),
            ],
            options={
                'verbose_name': '\u533a\u57df',
                'verbose_name_plural': '\u533a\u57df',
            },
        ),
        migrations.CreateModel(
            name='InterLock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interlock_rule', models.IntegerField(null=True, verbose_name='\u4e92\u9501\u89c4\u5219')),
                ('remark', models.CharField(blank=True, max_length=20, null=True, verbose_name='\u5907\u6ce8')),
            ],
            options={
                'verbose_name': '\u4e92\u9501\u89c4\u5219',
                'verbose_name_plural': 'interlock',
                'permissions': (),
            },
        ),
        migrations.CreateModel(
            name='level',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30, verbose_name='\u6743\u9650\u7ec4\u540d\u79f0')),
                ('itype', models.IntegerField(blank=True, choices=[(0, '\u95e8\u7981\u6743\u9650\u7ec4')], default=0, editable=False, null=True, verbose_name='\u6743\u9650\u7ec4\u7c7b\u578b')),
                ('is_visitor', models.IntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0, editable=False, verbose_name='\u8bbf\u5ba2\u95e8\u7981\u6743\u9650\u7ec4')),
                ('irange', models.IntegerField(blank=True, default=0, editable=False, null=True)),
            ],
            options={
                'verbose_name': '\u95e8\u7981\u6743\u9650\u7ec4',
                'verbose_name_plural': 'AccLevel',
                'permissions': (('addemps_level', 'Add emps'), ('delallemps_level', 'Delete allemps')),
            },
        ),
        migrations.CreateModel(
            name='level_door',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': '\u95e8\u7981\u4eba\u5458',
                'verbose_name_plural': 'level_emp',
            },
        ),
        migrations.CreateModel(
            name='level_emp',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': '\u95e8\u7981\u4eba\u5458',
                'verbose_name_plural': 'level_emp',
            },
        ),
        migrations.CreateModel(
            name='linkage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='', max_length=30, null=True, verbose_name='\u8054\u52a8\u540d\u79f0')),
                ('remark', models.CharField(blank=True, max_length=20, null=True, verbose_name='\u5907\u6ce8')),
            ],
            options={
                'verbose_name': '\u8054\u52a8',
                'verbose_name_plural': 'linkage',
            },
        ),
        migrations.CreateModel(
            name='linkage_inout',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('input_type', models.IntegerField(null=True)),
                ('input_id', models.IntegerField(null=True)),
                ('output_type', models.IntegerField(null=True)),
                ('output_id', models.IntegerField(null=True)),
                ('action_type', models.IntegerField(null=True)),
                ('action_time', models.IntegerField(null=True)),
            ],
            options={
                'verbose_name': '\u8054\u52a8',
                'verbose_name_plural': 'linkage_inout',
            },
        ),
        migrations.CreateModel(
            name='linkage_trigger',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trigger_cond', models.IntegerField(null=True)),
                ('linkage_index', models.IntegerField(null=True)),
            ],
            options={
                'verbose_name': '\u8054\u52a8',
                'verbose_name_plural': 'linkage_trigger',
            },
        ),
        migrations.CreateModel(
            name='records',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pin', models.CharField(blank=True, max_length=24, null=True, verbose_name='\u5de5\u53f7')),
                ('name', models.CharField(blank=True, max_length=24, null=True, verbose_name='\u59d3\u540d')),
                ('card_no', models.CharField(blank=True, max_length=10, null=True, verbose_name='\u5361\u53f7')),
                ('TTime', models.DateTimeField(verbose_name='\u65f6\u95f4',db_column='ttime')),
                ('inorout', models.IntegerField(choices=[(0, '\u5165'), (1, '\u51fa'), (2, '\u65e0')], null=True, verbose_name='\u8fdb\u51fa')),
                ('verify', models.IntegerField(choices=[(3, 'Password'), (1, 'Fingerprint'), (4, 'Card'), (5, 'Add'), (6, '\u5361\u6216\u6307\u7eb9'), (10, '\u5361+\u6307\u7eb9'), (16, 'Other'), (11, '\u5361+\u5bc6\u7801'), (200, '\u5176\u4ed6'), (15, 'FACE')], default=200, null=True, verbose_name='verification')),
                ('event_no', models.IntegerField(choices=[(-1, '\u65e0'), (0, '\u6b63\u5e38\u5237\u5361\u5f00\u95e8'), (1, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5185\u5f00\u95e8'), (2, '\u9996\u4eba\u5f00\u95e8(\u5237\u5361)'), (3, '\u591a\u5361\u5f00\u95e8(\u5237\u5361)'), (4, '\u7d27\u6025\u72b6\u6001\u5bc6\u7801\u5f00\u95e8'), (5, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5f00\u95e8'), (6, '\u89e6\u53d1\u8054\u52a8\u4e8b\u4ef6'), (7, '\u53d6\u6d88\u62a5\u8b66'), (8, '\u8fdc\u7a0b\u5f00\u95e8'), (9, '\u8fdc\u7a0b\u5173\u95e8'), (10, '\u7981\u7528\u5f53\u5929\u5e38\u5f00\u65f6\u95f4\u6bb5'), (11, '\u542f\u7528\u5f53\u5929\u5e38\u5f00\u65f6\u95f4\u6bb5'), (12, '\u5f00\u542f\u8f85\u52a9\u8f93\u51fa'), (13, '\u5173\u95ed\u8f85\u52a9\u8f93\u51fa'), (14, '\u6b63\u5e38\u6309\u6307\u7eb9\u5f00\u95e8'), (15, '\u591a\u5361\u5f00\u95e8(\u6309\u6307\u7eb9)'), (16, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u5185\u6309\u6307\u7eb9'), (17, '\u5361\u52a0\u6307\u7eb9\u5f00\u95e8'), (18, '\u9996\u5361\u5f00\u95e8(\u6309\u6307\u7eb9)'), (19, '\u9996\u5361\u5f00\u95e8(\u5361\u52a0\u6307\u7eb9)'), (20, '\u5237\u5361\u95f4\u9694\u592a\u77ed'), (21, '\u95e8\u975e\u6709\u6548\u65f6\u95f4\u6bb5(\u5237\u5361)'), (22, '\u975e\u6cd5\u65f6\u95f4\u6bb5'), (23, '\u975e\u6cd5\u8bbf\u95ee'), (24, '\u53cd\u6f5c'), (25, '\u4e92\u9501\u672a\u901a\u8fc7'), (26, '\u591a\u5361\u9a8c\u8bc1(\u5237\u5361)'), (27, '\u4eba\u672a\u767b\u8bb0'), (28, '\u95e8\u5f00\u8d85\u65f6'), (29, '\u5361\u5df2\u8fc7\u6709\u6548\u671f'), (30, '\u5bc6\u7801\u9519\u8bef'), (31, '\u6309\u6307\u7eb9\u95f4\u9694\u592a\u77ed'), (32, '\u591a\u5361\u9a8c\u8bc1(\u6309\u6307\u7eb9)'), (33, '\u6307\u7eb9\u5df2\u8fc7\u6709\u6548\u671f'), (34, '\u4eba\u672a\u767b\u8bb0'), (35, '\u95e8\u975e\u6709\u6548\u65f6\u95f4\u6bb5(\u6309\u6307\u7eb9)'), (36, '\u95e8\u975e\u6709\u6548\u65f6\u95f4\u6bb5(\u6309\u51fa\u95e8\u6309\u94ae)'), (37, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u65e0\u6cd5\u5173\u95e8'), (38, '\u5361\u5df2\u6302\u5931'), (39, '\u9ed1\u540d\u5355'), (41, '\u9a8c\u8bc1\u65b9\u5f0f\u9519\u8bef'), (42, '\u97e6\u6839\u5361\u683c\u5f0f\u4e0d\u5bf9'), (44, '\u53cd\u6f5c\u9a8c\u8bc1\u5931\u8d25'), (48, '\u591a\u5361\u5f00\u95e8\u5931\u8d25'), (100, '\u9632\u62c6\u62a5\u8b66'), (101, '\u80c1\u8feb\u5bc6\u7801\u5f00\u95e8'), (102, '\u95e8\u88ab\u610f\u5916\u6253\u5f00'), (200, '\u95e8\u5df2\u6253\u5f00'), (201, '\u95e8\u5df2\u5173\u95ed'), (202, '\u51fa\u95e8\u6309\u94ae\u5f00\u95e8'), (204, '\u5e38\u5f00\u65f6\u95f4\u6bb5\u7ed3\u675f'), (205, '\u8fdc\u7a0b\u5f00\u95e8\u5e38\u5f00'), (206, '\u8bbe\u5907\u542f\u52a8'), (207, '\u5bc6\u7801\u5f00\u95e8'), (208, '\u8d85\u7ea7\u7528\u6237\u5f00\u95e8'), (209, '\u89e6\u53d1\u51fa\u95e8\u6309\u94ae(\u88ab\u9501\u5b9a)'), (220, '\u8f85\u52a9\u8f93\u5165\u70b9\u65ad\u5f00'), (221, '\u8f85\u52a9\u8f93\u5165\u70b9\u77ed\u8def'), (225, '\u8f93\u5165\u70b9\u6062\u590d\u6b63\u5e38'), (226, '\u8f93\u5165\u70b9\u62a5\u8b66'), (222, '\u53cd\u6f5c\u9a8c\u8bc1\u6210\u529f'), (223, '\u53cd\u6f5c\u9a8c\u8bc1')], null=True, verbose_name='\u4e8b\u4ef6\u63cf\u8ff0')),

                ('eventaddr', models.CharField(blank=True, max_length=30, null=True, verbose_name='work code')),
                ('event_point_name', models.CharField(blank=True, max_length=30, null=True, verbose_name='\u4e8b\u4ef6\u70b9')),
                ('reader_name', models.CharField(blank=True, max_length=30, null=True, verbose_name='\u8bfb\u5934\u540d\u79f0')),
                ('Reserved', models.CharField(blank=True,db_column='reserved', max_length=20, null=True, verbose_name='Reserved')),
                ('dev_serial_num', models.IntegerField(blank=True, null=True, verbose_name='\u8bbe\u5907\u6d41\u6c34\u53f7')),
            ],
            options={
                'verbose_name': '\u95e8\u7981\u8bb0\u5f55',
                'verbose_name_plural': '\u95e8\u7981\u8bb0\u5f55',
                'permissions': (('monitor_oplog', 'Real Monitor'), ('acc_records', 'Acc Records'), ('acc_reports', 'Acc Reports')),
            },
        ),
        migrations.CreateModel(
            name='searchs',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('SN', models.CharField(max_length=20, verbose_name='serial number',db_column='sn')),
                ('Alias', models.CharField(blank=True, max_length=20, null=True,db_column='alias', verbose_name='Device Alias Name')),
                ('State', models.IntegerField(default=1, editable=False, verbose_name='\u72b6\u6001',db_column='state')),
                ('Protype', models.CharField(blank=True,db_column='protype', editable=False, help_text='\u901a\u8baf\u7c7b\u578b', max_length=10, null=True)),
                ('MAC', models.CharField(blank=True,db_column='mac', editable=False, max_length=20, null=True, verbose_name='MAC')),
                ('IPAddress', models.CharField(blank=True,db_column='ipaddress', max_length=20, null=True, verbose_name='IPAddress')),
                ('NetMask', models.CharField(blank=True,db_column='netmask', max_length=20, null=True, verbose_name='NetMask')),
                ('GATEIPAddress', models.CharField(blank=True,db_column='gateipaddress', max_length=20, null=True, verbose_name='GATEIPAddress')),
                ('DeviceName', models.CharField(blank=True,db_column='devicename', editable=False, max_length=30, null=True, verbose_name='Device Name')),
                ('FWVersion', models.CharField(blank=True,db_column='fwversion', editable=False, max_length=50, null=True, verbose_name='FW Version')),
                ('WebServerIP', models.CharField(blank=True,db_column='webserverip', max_length=20, null=True, verbose_name='WebServerIP')),
                ('WebServerURL', models.CharField(blank=True,db_column='webserverurl', max_length=100, null=True, verbose_name='WebServerURL')),
                ('WebServerPort', models.CharField(blank=True,db_column='webserverport', max_length=20, null=True, verbose_name='WebServerPort')),
                ('IsSupportSSL', models.IntegerField(blank=True,db_column='issupportssl', default=0, editable=False, null=True, verbose_name='IsSupportSSL')),
                ('DNSFunOn', models.IntegerField(blank=True,db_column='dnsfunon', default=0, editable=False, null=True, verbose_name='DNSFunOn')),
                ('DNS', models.CharField(blank=True,db_column='dns', max_length=100, null=True, verbose_name='WebServerURL')),
                ('OpStamp', models.DateTimeField(blank=True,db_column='opstamp', editable=False, null=True)),
                ('Style', models.CharField(blank=True,db_column='style', default='', editable=False, max_length=20, null=True, verbose_name='style')),
                ('isAdd', models.IntegerField(blank=True,db_column='isadd', default=0, editable=False, null=True)),
                ('Reserved', models.CharField(blank=True,db_column='reserved', max_length=210, null=True)),
            ],
            options={
                'verbose_name': '\u8bbe\u5907',
                'verbose_name_plural': 'device',
            },
        ),
        migrations.CreateModel(
            name='timezones',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Name', models.CharField(blank=True, max_length=30,db_column='name', verbose_name='\u65f6\u95f4\u6bb5\u540d\u79f0')),
                ('remark', models.CharField(blank=True, max_length=70, null=True, verbose_name='\u5907\u6ce8')),
                ('tz', models.TextField(null=True, verbose_name='\u65f6\u95f4')),
                ('DelTag', models.IntegerField(blank=True,db_column='deltag', default=0, editable=False, null=True, verbose_name='\u5220\u9664\u6807\u8bb0')),
            ],
            options={
                'verbose_name': 'ACTimeZones-table',
                'verbose_name_plural': 'ACTimeZones-table',
            },
        ),
        migrations.CreateModel(
            name='UserACCDevice',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('setTime', models.DateTimeField(blank=True,db_column='settime', null=True, verbose_name='SETTime')),
                ('Reserved', models.IntegerField(blank=True,db_column='reserved', default=0, editable=False, null=True)),
                ('Reserved1', models.FloatField(blank=True,db_column='reserved1', default=0, editable=False, null=True)),
                ('Reserved2', models.CharField(editable=False,db_column='reserved2', max_length=30)),
            ],
            options={
                'db_table': 'useraccdevice',
                'verbose_name': 'UserACCDevice-table',
                'verbose_name_plural': 'UserACCDevice-table',
            },
        ),
        migrations.CreateModel(
            name='UserACDevice',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('Card', models.CharField(editable=False,db_column='card', max_length=20)),
                ('door_no', models.IntegerField(blank=True, default=0, editable=False, null=True)),
                ('TimezoneId', models.IntegerField(blank=True,db_column='timezoneid', default=0, editable=False, null=True)),
                ('setTime', models.DateTimeField(blank=True,db_column='settime', null=True)),
                ('StartTime', models.DateTimeField(blank=True,db_column='starttime', null=True)),
                ('EndTime', models.DateTimeField(blank=True,db_column='endtime', null=True)),
                ('Reserved', models.CharField(editable=False,db_column='reserved', max_length=30)),
            ],
            options={
                'verbose_name': 'UserACDevice-table',
                'verbose_name_plural': 'UserACDevice-table',
            },
        ),
        migrations.CreateModel(
            name='UserACMachines',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
            ],
            options={
                'db_table': 'useracmachines',
                'verbose_name': 'UserACMachines-table',
                'verbose_name_plural': 'UserACMachines-table',
            },
        ),
        migrations.CreateModel(
            name='UserACPrivilege',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('IsUseGroup', models.IntegerField(db_column='isusegroup',choices=[(0, '\u81ea\u5b9a\u4e49\u65f6\u95f4\u6bb5'), (1, '\u4f7f\u7528\u7ec4\u8bbe\u7f6e')], default=0, null=True, verbose_name='IsUseGroup')),
                ('TimeZone1', models.IntegerField(db_column='timezone1',blank=True, default=0, null=True, verbose_name='TimeZone1')),
                ('TimeZone2', models.IntegerField(db_column='timezone2',blank=True, default=0, null=True, verbose_name='TimeZone2')),
                ('TimeZone3', models.IntegerField(db_column='timezone3',blank=True, default=0, null=True, verbose_name='TimeZone3')),
            ],
            options={
                'db_table': 'useracprivilege',
                'verbose_name': 'UserACPrivilege-table',
                'verbose_name_plural': 'UserACPrivilege-table',
            },
        ),
        migrations.CreateModel(
            name='zone',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='\u6700\u5927\u957f\u5ea6\u4e0d\u8d85\u8fc740\u4e2a\u5b57\u7b26,\u4fee\u6539\u533a\u57df\u7f16\u53f7\u540e\u4e0d\u4f1a\u6539\u53d8\u8bbe\u5907\u6240\u5728\u7684\u533a\u57df\u3002', max_length=40, verbose_name='\u533a\u57df\u7f16\u53f7')),
                ('name', models.CharField(max_length=40, verbose_name='\u533a\u57df\u540d\u79f0')),
                ('remark', models.CharField(blank=True, max_length=50, null=True, verbose_name='\u5907\u6ce8')),
                ('parent', models.IntegerField(blank=True, db_column='parent_id', default=0, verbose_name='\u4e0a\u7ea7\u533a\u57df')),
                ('DelTag', models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='\u5220\u9664\u6807\u8bb0')),
            ],
            options={
                'verbose_name': '\u533a\u57df',
            },
        ),
        migrations.CreateModel(
            name='ZoneAdmin',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('iscascadecheck', models.IntegerField(blank=True, default=0, editable=False, null=True)),
                ('code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.zone')),
            ],
            options={
                'verbose_name': 'admin granted zone',
                'verbose_name_plural': 'admin granted zone',
            },
        ),
    ]
