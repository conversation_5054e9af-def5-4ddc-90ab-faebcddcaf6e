## 访客模块
### 访客模块-被访人管理功能点新增被访人
#### 访客模块被访人管理功能点RAG检索锚点
<!-- RAG检索锚点: 新增被访人、被访人管理、被访人员、接待人员、访客接待 -->

#### 新增被访人功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示新增表单]
    D --> E[选择员工信息]
    E --> F[设置权限组]
    F --> G[填写备注信息]
    G --> H[验证表单数据]
    H --> I{数据验证}
    I -->|验证失败| J[显示错误信息]
    I -->|验证通过| K[检查员工重复]
    K --> L{员工是否已存在}
    L -->|已存在| M[提示员工已是被访人]
    L -->|不存在| N[保存被访人信息]
    N --> O[更新权限配置]
    O --> P[记录操作日志]
    P --> Q[返回成功信息]
    J --> E
    M --> E
    C --> R[结束]
    Q --> R
```
#### 新增被访人功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增被访人业务流程。
2. 权限验证：验证当前用户是否具有被访人管理权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行新增操作
3. 新增表单显示：
   - 显示被访人信息录入表单
   - 提供员工选择和权限配置界面
   - 显示字段验证规则和提示信息
4. 员工信息选择：
   - 从employee表中选择要设置为被访人的员工
   - 显示员工的基本信息（姓名、部门、职位等）
   - 支持按姓名、工号、部门等条件搜索员工
   - 验证员工的有效性和在职状态
5. 权限组设置：
   - 为被访人配置访客门禁权限组
   - 从level表中选择is_visitor=1的访客权限组
   - 支持多个权限组的选择和配置
   - 权限组决定访客可以访问的区域和门禁
6. 备注信息填写：
   - 填写被访人的备注信息
   - 说明被访人的职责和接待范围
   - 记录特殊的接待要求和注意事项
7. 表单数据验证：
   - 验证必填字段是否已填写
   - 检查员工信息的有效性
   - 验证权限组配置的正确性
8. 员工重复检查：
   - 检查选择的员工是否已经是被访人
   - 通过Interviewee表验证员工的唯一性
   - 防止同一员工重复设置为被访人
9. 被访人信息保存：
   - 将被访人信息保存到Interviewee表中
   - 关联员工ID（UserID字段）
   - 保存权限组配置（levels字段）
   - 设置创建时间和状态信息
10. 权限配置更新：
    - 更新被访人的门禁权限配置
    - 同步权限信息到相关系统
    - 确保权限配置的生效
11. 操作日志记录：记录被访人添加操作的详细日志。
12. 流程完成：返回操作成功信息，完成被访人添加流程。

#### 新增被访人功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 新增的被访人可以接受访客预约
   - 被访人信息用于预约时的接待人选择
   - 被访人的权限配置影响访客的门禁权限

2. **与访客登记模块的关联**：
   - 访客登记时需要选择被访人
   - 被访人信息用于登记记录的关联
   - 被访人的联系方式用于通知和确认

3. **与权限管理模块的关联**：
   - 被访人管理涉及门禁权限的配置
   - 被访人的权限组决定访客的访问范围
   - 权限变更需要同步到门禁系统

4. **与人员管理模块的关联**：
   - 被访人必须是系统中的有效员工
   - 员工信息变更影响被访人的状态
   - 员工离职需要清理被访人配置

5. **与通知系统的关联**：
   - 被访人设置后需要通知相关人员
   - 访客预约和登记需要通知被访人
   - 被访人的联系方式用于消息推送

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-被访人管理功能点删除被访人
#### 访客模块被访人管理功能点RAG检索锚点
<!-- RAG检索锚点: 删除被访人、移除被访人、被访人删除、取消被访人、被访人管理删除 -->

#### 删除被访人功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的被访人]
    D --> E[检查关联数据]
    E --> F{是否有关联访客记录}
    F -->|有关联记录| G[提示存在关联数据]
    F -->|无关联记录| H[确认删除操作]
    H --> I{用户确认}
    I -->|取消删除| J[返回被访人列表]
    I -->|确认删除| K[执行删除操作]
    K --> L[清理权限配置]
    L --> M[记录操作日志]
    M --> N[返回成功信息]
    C --> O[结束]
    G --> O
    J --> O
    N --> O
```
#### 删除被访人功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除被访人业务流程。
2. 权限验证：验证当前用户是否具有被访人删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行删除操作
3. 被访人选择：
   - 从被访人列表中选择要删除的记录
   - 显示被访人的基本信息供确认
   - 支持单个被访人记录的删除操作
4. 关联数据检查：
   - 检查被访人是否有关联的访客预约记录
   - 检查被访人是否有关联的访客登记记录
   - 确保删除操作不会影响现有的访客业务
5. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 说明删除后该员工将无法接待访客
   - 要求用户明确确认删除意图
6. 删除操作执行：
   - 从Interviewee表中删除被访人记录
   - 可以采用物理删除或软删除方式
   - 保持数据的一致性和完整性
7. 权限配置清理：
   - 清理被访人相关的权限配置
   - 移除访客权限组的关联
   - 确保权限系统的数据一致性
8. 操作日志记录：记录被访人删除操作的详细日志。
9. 流程完成：返回删除成功信息，完成被访人删除流程。

#### 删除被访人功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 删除被访人前需要检查预约记录的关联
   - 有关联预约的被访人不能删除
   - 删除后该员工不再出现在预约选项中

2. **与访客登记模块的关联**：
   - 删除被访人前需要检查登记记录的关联
   - 有关联登记的被访人不能删除
   - 删除后该员工不再出现在登记选项中

3. **与权限管理模块的关联**：
   - 删除被访人需要清理相关权限配置
   - 权限组关联需要相应清理
   - 确保权限系统的数据一致性

4. **与人员管理模块的关联**：
   - 删除被访人不影响员工的基本信息
   - 员工仍然存在，只是不再是被访人
   - 员工离职时会自动清理被访人配置

5. **与系统配置模块的关联**：
   - 被访人删除操作受系统权限配置限制
   - 删除操作需要记录到系统审计日志
   - 系统安全策略影响删除权限

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
