# -*- coding: utf-8 -*-
# Generated by Django 1.11.5 on 2017-10-01 14:53
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('iclock', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('acc', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='zoneadmin',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='zone',
            unique_together=set([('code', 'name')]),
        ),
        migrations.AddField(
            model_name='useracprivilege',
            name='ACGroupID',
            field=models.ForeignKey(db_column='groupid', default=1, on_delete=django.db.models.deletion.CASCADE, to='acc.ACGroup', verbose_name='\u95e8\u7981\u7ec4'),
        ),
        migrations.AddField(
            model_name='useracprivilege',
            name='UserID',
            field=models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u5458\u5de5'),
        ),
        migrations.AddField(
            model_name='useracmachines',
            name='SN',
            field=models.ForeignKey(db_column='sn', on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='device'),
        ),
        migrations.AddField(
            model_name='useracmachines',
            name='UserID',
            field=models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u5458\u5de5'),
        ),
        migrations.AddField(
            model_name='useracdevice',
            name='SN',
            field=models.ForeignKey(db_column='sn', on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907'),
        ),
        migrations.AddField(
            model_name='useracdevice',
            name='UserID',
            field=models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u4eba\u5458'),
        ),
        migrations.AddField(
            model_name='useraccdevice',
            name='SN',
            field=models.ForeignKey(db_column='sn', on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907'),
        ),
        migrations.AddField(
            model_name='useraccdevice',
            name='UserID',
            field=models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u4eba\u5458'),
        ),
        migrations.AlterUniqueTogether(
            name='timezones',
            unique_together=set([('Name', 'DelTag')]),
        ),
        migrations.AlterUniqueTogether(
            name='searchs',
            unique_together=set([('SN',)]),
        ),
        migrations.AddField(
            model_name='records',
            name='SN',
            field=models.ForeignKey(db_column='sn_id',blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='device'),
        ),
        migrations.AddField(
            model_name='linkage_trigger',
            name='linkage',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.linkage'),
        ),
        migrations.AddField(
            model_name='linkage_trigger',
            name='linkage_inout',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.linkage_inout'),
        ),
        migrations.AddField(
            model_name='linkage_inout',
            name='linkage',
            field=models.ForeignKey(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.linkage'),
        ),
        migrations.AddField(
            model_name='linkage',
            name='device',
            field=models.ForeignKey(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907\u540d\u79f0'),
        ),
        migrations.AddField(
            model_name='level_emp',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id',editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u95e8\u7981\u7ec4\u4eba\u5458'),
        ),
        migrations.AddField(
            model_name='level_emp',
            name='level',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.level', verbose_name='\u95e8\u7981\u7ec4'),
        ),
        migrations.AddField(
            model_name='level_door',
            name='door',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='\u95e8'),
        ),
        migrations.AddField(
            model_name='level_door',
            name='level',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.level', verbose_name='\u95e8\u7981\u7ec4'),
        ),
        migrations.AddField(
            model_name='level',
            name='timeseg',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.timezones', verbose_name='\u65f6\u95f4\u6bb5'),
        ),
        migrations.AddField(
            model_name='interlock',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907'),
        ),
        migrations.AddField(
            model_name='iclockzone',
            name='SN',
            field=models.ForeignKey(db_column='sn_id',on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock'),
        ),
        migrations.AddField(
            model_name='iclockzone',
            name='zone',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.zone', verbose_name='\u533a\u57df'),
        ),
        migrations.AddField(
            model_name='iaccempitemdefine',
            name='Author',
            field=models.ForeignKey(db_column='author_id',on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='iaccdevitemdefine',
            name='Author',
            field=models.ForeignKey(db_column='author_id',on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='firstopen_emp',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id',editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u4eba\u5458'),
        ),
        migrations.AddField(
            model_name='firstopen_emp',
            name='firstopen',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.FirstOpen', verbose_name='\u95e8'),
        ),
        migrations.AddField(
            model_name='firstopen',
            name='door',
            field=models.ForeignKey( null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='\u95e8\u540d\u79f0'),
        ),
        migrations.AddField(
            model_name='firstopen',
            name='timeseg',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.timezones', verbose_name='\u95e8\u7981\u65f6\u95f4\u6bb5'),
        ),
        migrations.AddField(
            model_name='combopen_emp',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id',editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u4eba\u5458'),
        ),
        migrations.AddField(
            model_name='combopen_emp',
            name='combopen',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.combopen', verbose_name='\u5f00\u95e8\u7ec4\u5408'),
        ),
        migrations.AddField(
            model_name='combopen_door',
            name='door',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='\u95e8'),
        ),
        migrations.AddField(
            model_name='combopen_comb',
            name='combopen',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.combopen'),
        ),
        migrations.AddField(
            model_name='combopen_comb',
            name='combopen_door',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.combopen_door'),
        ),
        migrations.AlterUniqueTogether(
            name='combopen',
            unique_together=set([('name',)]),
        ),
        migrations.AddField(
            model_name='auxout',
            name='device',
            field=models.ForeignKey(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907\u540d\u79f0'),
        ),
        migrations.AddField(
            model_name='auxin',
            name='device',
            field=models.ForeignKey(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907\u540d\u79f0'),
        ),
        migrations.AddField(
            model_name='antipassback',
            name='device',
            field=models.ForeignKey(null=True,on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907'),
        ),
        migrations.AddField(
            model_name='accmap_zone',
            name='code',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.zone'),
        ),
        migrations.AddField(
            model_name='accmap_zone',
            name='imap',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccMap', verbose_name='\u6240\u5c5e\u5730\u56fe'),
        ),
        migrations.AddField(
            model_name='accdoormap',
            name='door',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='\u95e8'),
        ),
        migrations.AddField(
            model_name='accdoormap',
            name='imap',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccMap', verbose_name='\u6240\u5c5e\u5730\u56fe'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='device',
            field=models.ForeignKey(help_text='\u4e0d\u5141\u8bb8\u4fee\u6539', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='\u8bbe\u5907\u540d\u79f0'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='imap',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccMap', verbose_name='\u6240\u5c5e\u5730\u56fe'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='lock_active',
            field=models.ForeignKey(default=1, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lockactive_set', to='acc.timezones', verbose_name='\u95e8\u6709\u6548\u65f6\u95f4\u6bb5'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='long_open',
            field=models.ForeignKey(blank=True, help_text='\u5f53\u95e8\u4e00\u76f4\u5904\u4e8e\u5e38\u5f00\u72b6\u6001\u65f6\uff0c\u5728\u8be5\u65f6\u95f4\u6bb5\u5185\u8fde\u7eed\u5237\u5361\u4e94\u6b21\u53ef\u4ee5\u89e3\u9664\u95e8\u5e38\u5f00\u72b6\u6001\u3002', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='longopen_set', to='acc.timezones', verbose_name='\u95e8\u5e38\u5f00\u65f6\u95f4\u6bb5'),
        ),
        migrations.AddField(
            model_name='accdoor',
            name='wiegand_fmt',
            field=models.ForeignKey(default=1, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccWiegandFmt', verbose_name='\u97e6\u6839\u5361\u683c\u5f0f'),
        ),
        migrations.AddField(
            model_name='acc_employee',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id',editable=False, on_delete=django.db.models.deletion.CASCADE,
                                    to='iclock.employee', verbose_name='\u4eba\u5458'),
        ),
        migrations.AddField(
            model_name='acc_employee',
            name='morecard_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.combopen', verbose_name='\u591a\u4eba\u5f00\u95e8\u4eba\u5458\u7ec4'),
        ),
        migrations.AlterUniqueTogether(
            name='zoneadmin',
            unique_together=set([('user', 'code')]),
        ),
        migrations.AlterUniqueTogether(
            name='useracmachines',
            unique_together=set([('UserID', 'SN')]),
        ),
        migrations.AlterUniqueTogether(
            name='useracdevice',
            unique_together=set([('UserID', 'SN', 'door_no', 'TimezoneId')]),
        ),
        migrations.AlterUniqueTogether(
            name='useraccdevice',
            unique_together=set([('UserID', 'SN')]),
        ),
        migrations.AlterUniqueTogether(
            name='records',
            unique_together=set([('TTime', 'SN', 'dev_serial_num')]),
        ),
        migrations.AlterUniqueTogether(
            name='linkage',
            unique_together=set([('name',)]),
        ),
        migrations.AlterUniqueTogether(
            name='level_emp',
            unique_together=set([('level', 'UserID')]),
        ),
        migrations.AlterUniqueTogether(
            name='level_door',
            unique_together=set([('level', 'door')]),
        ),
        migrations.AlterUniqueTogether(
            name='level',
            unique_together=set([('name',)]),
        ),
        migrations.AlterUniqueTogether(
            name='interlock',
            unique_together=set([('device', 'interlock_rule')]),
        ),
        migrations.AlterUniqueTogether(
            name='iclockzone',
            unique_together=set([('SN', 'zone')]),
        ),
        migrations.AlterUniqueTogether(
            name='iaccempitemdefine',
            unique_together=set([('ItemName', 'Author', 'ItemType')]),
        ),
        migrations.AlterUniqueTogether(
            name='iaccdevitemdefine',
            unique_together=set([('ItemName', 'Author', 'ItemType')]),
        ),
        migrations.AlterUniqueTogether(
            name='firstopen_emp',
            unique_together=set([('firstopen', 'UserID')]),
        ),
        migrations.AlterUniqueTogether(
            name='firstopen',
            unique_together=set([('door', 'timeseg')]),
        ),
        migrations.AlterUniqueTogether(
            name='combopen_emp',
            unique_together=set([('combopen', 'UserID')]),
        ),
        migrations.AlterUniqueTogether(
            name='auxout',
            unique_together=set([('device', 'aux_no')]),
        ),
        migrations.AlterUniqueTogether(
            name='auxin',
            unique_together=set([('device', 'aux_no')]),
        ),
        migrations.AlterUniqueTogether(
            name='accdoor',
            unique_together=set([('device', 'door_no')]),
        ),
    ]
