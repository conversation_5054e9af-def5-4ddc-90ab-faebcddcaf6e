# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2018-08-24 16:15
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_auto_20180615_1519'),
    ]

    operations = [
        migrations.AlterField(
            model_name='myuser',
            name='AutheTimeDept',
            field=models.IntegerField(blank=True, db_column='authetimedept', default=0, help_text='The time period and shift. If all units use the same set of time slots and shifts, ignore this item', null=True, verbose_name='authorized use'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='logout mark'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='Tele',
            field=models.CharField(blank=True, db_column='ophone', max_length=30, null=True, verbose_name='contact number'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='emp_pin',
            field=models.CharField(blank=True, editable=False, max_length=30, null=True, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='first_name',
            field=models.CharField(blank=True, max_length=30, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='is_alldept',
            field=models.BooleanField(default=False, help_text='Authorize whether to manage all departments.', verbose_name='Authorize all departments'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='is_public',
            field=models.BooleanField(default=False, editable=False, help_text='Open super administrator name, phone number, EMAIL, convenient to contact.', verbose_name='Is it public?'),
        ),
        migrations.AlterField(
            model_name='myuser',
            name='logincount',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Number of logins'),
        ),
    ]
