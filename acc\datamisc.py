#coding=utf-8
from mysite.iclock.models import *
from django.utils.encoding import smart_str
from mysite.iclock.datautils import *
from django.shortcuts import render
from django.template import loader, Context, RequestContext, Library, Template, Context, TemplateDoesNotExist
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils.translation import gettext as _trans
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.views.decorators.csrf import csrf_protect
from mysite.utils import trunc
from mysite.acc.models import *
from mysite.visitors.models import visitionlogs
from mysite.utils import *
#import json
MAX_REALTIME_COUNT=50
g_rtstate={}
g_doorstate={}
def get_recordColModel():
    Result=[{'name':'id','hidden':True}]
    for i in range(10):
        Result.append({'name':'door%s'%(i),'sortable':False,'width':90,'title':False,'resizable':False,'align':'center','label':''})
    return Result

def get_door_state(val, doorno):
    if doorno == 1:
        return (val & 0x00000000000000000000FF)
    elif doorno == 2:
        return (val & 0x000000000000000000FF00) >> 8
    elif doorno == 3:
        return (val & 0x0000000000000000FF0000) >> 16
    elif doorno == 4:
        return (val & 0x00000000000000FF000000) >> 24
    elif doorno == 5:
        return (val & 0x000000000000FF00000000) >> 32
    elif doorno == 6:
        return (val & 0x0000000000FF0000000000) >> 40
    elif doorno == 7:
        return (val & 0x00000000FF000000000000) >> 48
    elif doorno == 8:
        return (val & 0x000000FF00000000000000) >> 56
    elif doorno == 9:
        return (val & 0x0000FF0000000000000000) >> 64
    elif doorno == 10:
        return (val & 0x00FF000000000000000000) >> 72
    elif doorno == 11:
        return (val & 0xFF00000000000000000000) >> 80

#获取某个门的继电器状态，只能到8个门
def get_door_relay(val, doorno):
    if doorno == 1:
        return (val & 0x1)
    elif doorno == 2:
        return (val & 0x2) >> 1
    elif doorno == 3:
        return (val & 0x4) >> 2
    elif doorno == 4:
        return (val & 0x8) >> 3
    elif doorno == 5:
        return (val & 0x16) >> 4
    elif doorno == 6:
        return (val & 0x32) >> 5
    elif doorno == 7:
        return (val & 0x64) >> 6
    else:
        return (val & 0x128) >> 7

def get_rtLog(request):
    SN=request.GET.get('SN', "")
    nt=trunc(datetime.datetime.now()-datetime.timedelta(days=1))
    lasttid=int(request.GET.get("lasttid","-1"))
    items=records.objects.all()#filter(SN__ProductType__in=[4,5])
    if SN:
        devices = SN.split(',')
        items=items.filter(SN__in=devices)
    elif not (request.user.is_superuser or request.user.is_allzone):
        zas = ZoneAdmin.objects.filter(user=request.user).values('code')  # 区域
        iz = IclockZone.objects.filter(zone__DelTag=0, zone__in=zas).values('SN')  # 控制器
        items = items.filter(SN__in=iz)
    #items=items

#	if (cache.get("%s_haslogs_%s"%(settings.UNIT,request.user.pk))!=cache.get("%s_logstamp_"%(settings.UNIT))) or lasttid==0:
#		cache.set("%s_haslogs_%s"%(settings.UNIT,request.user.pk),cache.get("%s_logstamp_"%(settings.UNIT)))



    logs=items.filter(id__gt=lasttid).order_by("-id")

    if lasttid==-1:
        #logs=logs[:10]
        lasttid = logs.aggregate(Max('id'))['id__max']
        if not lasttid: lasttid = 0
        logs = []
    else:
        logs=logs[:MAX_REALTIME_COUNT]
    lines=[]
    photo_lines=[]
    result={}
    for l in logs:
        lasttid=max(l.id,lasttid)
        line={}
        line['id']=l.id
#		line['PIN']="%s"%l.employee()
        line['TTime']=l.TTime.strftime("%m-%d %H:%M:%S")

        if l.SN:
            dev=l.Device()
            if dev.Alias and dev.Alias!=dev.IPAddress:
                line['Device']=u"%s"%(dev.Alias)
            else:
                line['Device']=u"%s"%(dev.SN)
        else:
            line['Device']=""

        line['event_point']=get_event_point_name(l)

        line['event_no']=l.get_event_no_display()
        line['eventno']=l.event_no
        if l.event_no==6:
            line['card_no']=''
        else:
            line['card_no']=l.card_no or ''
        line['pin']=''
        line['name']=''
        line['deptName']=''
        emp=None
        if l.pin:
            try:
                emp=employee.objByPIN(l.pin)
            except:
                if str(l.pin).startswith('vis'):
                    id = l.pin[3:]
                    visitor = visitionlogs.objects.filter(pk=id)
                    if visitor:
                        line['pin'] = l.pin
                        line['name'] = visitor[0].VisempName or ''
        if emp:
            line['pin']=u"%s"%(emp.PIN or '')
            line['name']=u"%s"%(emp.EName or '')
            line['deptName']=emp.Dept().DeptName or ''
            line['photo_url'] = getStoredFileURL('photo/thumbnail', None, '%s.jpg' % l.pin)
            if l.getAccImgUrl():#tdb08 等设备显示抓怕照片
                line['photo_url'] = l.getAccImgUrl()

        try:
            line['inorout']=u"%s"%(dict(STATE_CHOICES)[l.inorout])
        except:
            line['inorout']=u'%s'%l.inorout
        if l.event_no==6:  # 触发联动 |触发联动 |联动事件记录中的验证方式的值为具体的事件类型
            if l.SN:
                dev=l.Device()
                if hasattr(dev, 'NewVFStyles'): # 新验证方式解析规则不同
                    # 据说只会返回 00000000000000000000000000000000
                    line['verify'] = '%s'%_(u'Other')
                else:
                    line['verify']= get_EVENT_CHOICES_name(l.verify)
            else:
                line['verify']= get_EVENT_CHOICES_name(l.verify)
        elif l.event_no==7:  # 取消报警, 兼容P3000，直接返回"其他"，不再解析了
            line['verify'] = '%s'%_(u'Other')
        else:
            line['verify']= l.get_verify_display_ex() or '%s'%_(u'Other')
        line['dev_serial_num']=l.dev_serial_num or ''
        lines.insert(0,line.copy())
    result['data']=lines
    result['lasttId']=lasttid
    result['ret']=len(lines)
    result['tm']=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    return result

def getDoorState(SN,door_no,stamp):
    device = getDevice(SN)
    state = device.getDynState()
    dev_state=('default', 0, 0, 0)
    if state == DEV_STATUS_PAUSE:
        dev_state= ('disabled', 0, 0, 0)
    elif state == DEV_STATUS_OFFLINE:
        dev_state= ('offline', 0, 0, 0)
    elif state == DEV_STATUS_OK:
        dev_state= ('online', 0, 0, 0)
        rtstate=cache.get('_device_%s'%SN)
        if rtstate:
            d=lineToDict(rtstate)
            tm=d['time']
            sensor=d['sensor']
            relay=int(d.get('relay',0),16)
            alarm=d['alarm']
            if not alarm:
                # 部分设备alram传值为空
                alarm = '0' * 16  # 0000000000000000
            rela=relay>>(door_no-1)&0x01
            if device.Style in ['30']: # P3000
                # P3000门磁类型解析与标准协议不同，示例
                # rtstate = "time=2024-06-17 10:08:30\tsensor=0000000012\trelay=000000\talarm=00000000\tdoor=01000100\tsn=SRF1241000028"
                # sensor 门磁类型，格式 000000AABB, AA 5~8门， BB 1~4门
                # sensor=0000000012  16进制 12
                # 对应二进制 00 0l 00 l0
                #          4门 3门 2门 1门
                # 00 无门磁
                # 01 关闭
                # 10 打开
                # 0000000012 对应就是4门无门磁，3门关闭，2门无门磁，1门打开
                sens=int(sensor,16)
                sens=sens>>((door_no-1)*2)&0x03
            else:
                # 示例
                # time=2024-09-24 15:44:01	sensor=00	relay=00	alarm=0300000000000000	door=01
                if door_no<5:
                    sens=sensor[0:2]
                    sens=int(sens,16)
                    sens=sens>>((door_no-1)*2)&0x03
                else:
                    sens=sensor[2:4]
                    sens=int(sens,16)
                    sens=sens>>(((door_no - 4)-1)*2)&0x03
            alm = alarm[door_no*2-2:door_no*2]
            alm=int(alm,16)
            alms = bin(alm)[2:].zfill(5)
            alm0 = alms[0]
            alm1 = alms[1]
            alm2 = alms[2]
            alm3 = alms[3]
            alm4 = alms[4]
            #print "ssss-----",rtstate,door_no,sens,rela,alm
            if sens==0: # 二进制00
                if rela==1:
                    return ('nosensor_unlocked',sens,rela,alm)
                else:
                    return ('nosensor',sens,rela,alm)
            if sens==1: # 二进制01
                if alm == 0:

                    if rela==0:
                        return ('closed',sens,rela,alm)
                    elif rela==1:
                        return ('closed_unlocked',sens,rela,alm)
                else:

                    if rela==0:
                        return ('alarm_closed',sens,rela,alm)
                    elif rela==1:
                        return ('alarm_closed_unlocked',sens,rela,alm)
                return ('closed',sens,rela,alm)
            if sens==2: # 二进制10
                if alm == 0:
                    if rela==0:
                        return ('opened',sens,rela,alm)
                    elif rela==1:
                        return ('opened_unlocked',sens,rela,alm)
                else:
                    if alm4 == 1:
                        if rela==0:
                            return ('alarm_timeout',sens,rela,alm)
                        elif rela==1:
                            return ('alarm_timeout_unlocked',sens,rela,alm)
                    else:
                        if rela==0:
                            return ('alarm_opened',sens,rela,alm)
                        elif rela==1:
                            return ('alarm_opened_unlocked',sens,rela,alm)

                return ('opened',sens,rela,alm)

            return ('closed',sens,rela,alm)
    return dev_state


def getDoorStateForTCP(SN,door_no,stamp):
    device = getDevice(SN)
    state = device.getDynState()
    dev_state=('default', 0, 0, 0)
    if state == DEV_STATUS_PAUSE:
        dev_state= ('disabled', 0, 0, 0)
    elif state == DEV_STATUS_OFFLINE:
        dev_state= ('offline', 0, 0, 0)
    elif state == DEV_STATUS_OK:
        dev_state= ('online', 0, 0, 0)
        rtstate = cache.get('_device_tcp_%s' % SN)
        if rtstate:
            l=rtstate.split(',')
            tm=l[0]
            rela=0
            sens=0
            if l[1]:
                # l数据['2020-12-18 09:44:54','04','00000000','C8']
                # 数据格式如下“0-时间 + 1-门磁状态 + 2-报警事件 + 3-继电器状态 ”
                sens = int(l[1], 16) >> ((door_no - 1) * 2) & 0x03
                rela = int(l[3], 16) >> (door_no - 1) & 0x01
                alarm = l[2]
                alm = alarm[door_no*2-2:door_no*2]
                alm = int(alm, 16)
                alms = bin(alm)[2:].zfill(5)
                if alms.find('1') >= 0:
                    alm = 1
                else:
                    alm = 0
                # if (hasattr(device, 'ReaderEncryptFunOn') and str(device.ReaderEncryptFunOn) == '1'):
                #     # l数据['2020-12-18 09:44:54','04','00000000','C8']
                #     # 数据格式如下“0-时间 + 1-门磁状态 + 2-报警事件 + 3-继电器状态 ”
                #     sens = int(l[1], 16) >> ((door_no - 1) * 2) & 0x03
                #     rela = int(l[3], 16) >> (door_no - 1) & 0x01
                #     alarm = l[2]
                #     alm = alarm[door_no*2-2:door_no*2]
                #     alm = int(alm, 16)
                #     alms = bin(alm)[2:].zfill(5)
                #     if alms.find('1') >= 0:
                #         alm = 1
                #     else:
                #         alm = 0
                # else:
                #     # 数据格式['2020-12-18 09:44:54','33686017','269484288','0','255','0','0']
                #     # 数据格式如下“0-时间 + 1-门磁状态 + 2-报警事件”
                #     sens = get_door_state(int(l[1]),door_no)
                #     alm = get_door_state(int(l[2]), door_no)  # （1报警，2门开超时,门磁超时和意外开门同时触发情况二进制10001,十进制17)
                #     if alm <= 2:
                #         alm = alm % 2
                #     else:
                #        if alm / 2 == 8:
                #            alm = 0
                #        else :
                #            alm = 2
            #门状态：GetRTlog协议文档写的是十六进制，SDK传给软件是十进制字符串,GetRTlogExt协议写的是二进制, SDK传的是十六进制
            if sens == 0:
                return ('nosensor',sens,rela,alm)
            if sens == 1:
                if alm == 0:
                    return ('closed_nolock',sens,rela,alm)
                elif alm == 1:
                    return ('alarm_closed',sens,rela,alm)
                elif alm == 2:
                    return ('alarm_closed',sens,rela,alm)
                return ('closed_nolock',sens,rela,alm)
            if sens == 2:
                if alm == 0:
                    return ('opened_nolock',sens,rela,alm)
                elif alm == 1:
                    return ('alarm_opened',sens,rela,alm)
                elif alm == 2:
                    return ('alarm_timeout',sens,rela,alm)

                return ('opened_nolock',sens,rela,alm)

            return ('closed',sens,rela,alm)
    return dev_state

def get_rtstate(request):
    """
    获取门状态，实时监控、电子地图共用
    """
    stamp = request.GET.get('stamp', '0')
    sns = request.GET.get('SN', '')
    devices = [x for x in sns.split(',') if x]  #实时监控，左侧设备树选择的设备
    result = {}
    doorstate_dict = {}  #需要缓存在Cache的门状态信息, 缓存存的是所有门的信息
    authorized_devices = []  #授权设备

    if request.user.is_anonymous:
        return result

    cached_doorstate = cache.get('%s_ACC_DOORSTATE' % (settings.UNIT))  #已缓存的门状态信息
    if not cached_doorstate or stamp == '0':  #新打开实时监控页、切换电子地图选项卡（刷新缓存）
        acc_doors = AccDoor.objects.filter(device__DelTag=0, device__ProductType__in=[4,5,15,25]).order_by('id')
        for l in acc_doors:
            line = {}
            line['id'] = l.id
            line['SN'] = l.device_id
            line['door_name'] = l.door_name
            line['door_no'] = l.door_no
            line['ptype'] = l.device.ProductType
            if line['ptype'] == 15:  #PULL设备，如C3
                state = getDoorStateForTCP(l.device_id, l.door_no, stamp)
            else:
                state = getDoorState(l.device_id, l.door_no, stamp)
            line['stamp'] = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            line['state'] = state
            doorstate_dict[l.id] = line
        cache.set('%s_ACC_DOORSTATE' % (settings.UNIT), doorstate_dict)
        #查询用户权限
        if not (request.user.is_superuser or request.user.is_allzone):
            zonelist = userZoneList(request.user)
            authorized_devices = IclockZone.objects.filter(zone__in=zonelist).values_list("SN", flat=True)
            cache.set('%s_%s_ACC_DOORSTATE_AUTHORIZED_SN' % (settings.UNIT, request.user.id), authorized_devices)
    else:
        doorstate_dict = cached_doorstate
        cached_authorized_devices = cache.get('%s_%s_ACC_DOORSTATE_AUTHORIZED_SN' % (settings.UNIT, request.user.id))  #已缓存的授权设备信息
        if cached_authorized_devices:
            authorized_devices = cached_authorized_devices
        for k, v in doorstate_dict.items():
            if v['ptype'] == 15:
                state = getDoorStateForTCP(v['SN'], v['door_no'], stamp)
            else:
                state = getDoorState(v['SN'], v['door_no'], stamp)
            if state and v['state'] != state: #门状态有变化的更新
                v['state'] = state
                v['stamp'] = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        cache.set('%s_ACC_DOORSTATE' % (settings.UNIT), doorstate_dict)  #这边并发情况，可能有问题

    _state={}
    c = 0
    laststamp = stamp
    _stamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    for k, v in doorstate_dict.items():
        if devices:
            if v['SN'] not in devices:
                continue  #如果按设备查询，不在查询范围的过滤掉
            else:
                v['stamp'] = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                _stamp = v['stamp']
        if authorized_devices and v['SN'] not in set(authorized_devices):
            continue  #未授权设备，过滤掉
        if laststamp >= _stamp:
            continue
        _state[k] = v
        c += 1
    laststamp = max(laststamp, _stamp)
    result['data'] = _state
    result['ret'] = c
    result['stamp'] = laststamp
    result['tm'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return result

"""实时 """
#@login_required
@csrf_protect
def newTransLog(request):

    global g_doorstate
    result={}
    lasttid=int(request.GET.get("lasttid","-1"))
    lastdid=int(request.GET.get("lastdid","-1"))
    mapid=int(request.GET.get("mapid","-1"))
    if request.method=='GET':
        attColModel=records.colModels()[:11]
        HeaderModels=[]
        cc={}
        cc['recordColModel']=dumps(get_recordColModel())
        cc['accModel']=dumps(attColModel)
        cc['HeaderModels']=dumps(HeaderModels)
        g_doorstate={}
        data=loads(GetParamValue('acc_param','{}','acc'))
        if not data:
                data={
                    'is_':0,#是否启用
                }
        cc['acc_params']=dumps1(data)



        return render(request,"acc/dlogcheck.html",cc)#render("acc/dlogcheck.html",cc,RequestContext(request, {}))
    logs=[]
    items=[]
    photo_logs=[]
    datas=[]
    if mapid==-1:
        datas=get_rtLog(request)
    try:
        rtstate=get_rtstate(request)
    except:
        rtstate = {'stamp': 0}  # stamp给个默认值，以免前端显示异常undefine
        if settings.DEBUG:
            import traceback; traceback.print_exc()
    cc={}
    cc['data']=datas
    cc['state']=rtstate
    return getJSResponse(cc)

#l 取消报警
def alarmLog(request):
    first_time = datetime.datetime.now()  # 第一次取消报警
    even = records.objects.filter(event_no=7).order_by("-TTime").first()  # l 获取最后一次取消报警时间对象
    cc = {}
    if even:
        cc['time'] = datetime.datetime.strftime(even.TTime, '%Y-%m-%d %H:%M:%S')
    else:
        cc['time'] = datetime.datetime.strftime(first_time, '%Y-%m-%d %H:%M:%S')
    return getJSResponse(cc)

def getAccInoutLog(request):
        result={}
        lasttid=int(request.GET.get("lasttid","-1"))
        result['lastId']=lasttid
        now=datetime.date.today()
        #logs=records.objects.filter(id__gt=lasttid,TTime__gte=now).exclude(pin=0).order_by("-id")
        ntime = datetime.datetime(now.year, now.month, now.day, 0, 0, 0).strftime("%Y-%m-%d %H:%M:%S")
        logs = records.objects.exclude(pin=0).filter(TTime__gte=ntime).order_by("-id")[:10]
        result['emp_date']={}
        result['depts_date']={}
        mhtml = ""
        i = 0
        '''
        if not logs:
            for  i in range(10):
                photo = u"/media/img/ued/img_photo3.png"
                html = u"<div class='accrendance_msg'><div class='id_bottom_pic'>"
                html += u"<img class = 'acc_pic' id='index_" + str(i) + "' src='" + photo + "'></img>"
                html += u"<p class='acc_record acc_record_name'>姓名: XXX</p>"
                html += u"<p class='acc_record acc_record_checktime'>2019-12-12 10:06:59</p>"
                html += u"</div></div>"
                mhtml += html
                i = i + 1
        '''
        for log in reversed(logs):
            ll={}
            ll['pin']=log.pin
            ll['name']=log.name
            if log.employee()!='':
                ll['deptname']=log.employee().Dept().DeptName
            else:
                ll['deptname']=''
            ll['ttime']=log.TTime.strftime("%Y-%m-%d %H:%M:%S")
            if log.Device():
                ll['event_point_name']=log.event_point_name
                ll['sn']=log.Device().Alias or log.Device().SN
            cmapfile =''
            if log.Device():
                cmapfile = getStoredFileName("photo", None, '{}.jpg'.format(log.pin))
                if os.path.exists(cmapfile):
                    cmapfile = getStoredFileURL('photo/thumbnail', None, '%s.jpg'%log.pin)
                else:
                    ll['urls']=""
                    cmapfile =u"/media/img/ued/img_photo3.png"

            photo = u"/media/img/transaction/noimg.jpg"
            html = u"<div class='accrendance_msg'><div class='id_bottom_pic'>"
            html += u"<img class = 'acc_pic' id='index_" + str(i) + "' src='" + cmapfile + "'></img>"
            html += u"<p class='acc_record acc_record_name'>"+log.name+"</p>"
            html += u"<p class='acc_record acc_record_checktime'>"+log.TTime.strftime("%Y-%m-%d %H:%M:%S")+"</p>"
            #html += u"<p class='att_record'>"+"事件类型:"+get_event_point_name(log)+"</p>"
            html += u"</div></div>"
            mhtml += html
            i = i + 1


        return {"mhtml":mhtml}


def accInoutLog(request):
    cc = {}
    device_status_number = get_device_status_number()
    today_24_hour_total = get_today_24_hour_inout_total()
    top5_event_total = get_top5_event_total()
    event_count = get_acc_event_total()
    dept = get_deptnums()
    data_total = get_datanums()
    # event_type  =  get_event_type_total()
    accinout = getAccInoutLog(request)
    cc = {
        'device_status_number': device_status_number,
        'today_24_hour_total': today_24_hour_total,
        'top5_event_total': top5_event_total,
        'event_count': event_count,
        'dept': dept,
        'data_total': data_total,
        'mhtml': accinout['mhtml']
    }
    if request.method == 'GET':
       return render(request, 'acc/accInOutlog.html', cc)
    else:
        #print cc['mhtml']
        return getJSResponse(cc)

def get_datanums():
    from mysite.iclock.reportsview import get_leave_del_emp_tag
    uo,ud = get_leave_del_emp_tag()
    ndate =datetime.datetime.now().strftime("%Y-%m-%d")
    TDate = datetime.datetime.strptime(ndate, "%Y-%m-%d")
    finger_count =  BioData.objects.filter(bio_type = 1).exclude(UserID__DelTag = ud).filter(UserID__OffDuty__lt = uo).values_list('UserID').distinct().count()
    emp_count = employee.objects.exclude(DelTag = ud).filter(OffDuty__lt = uo).count()
    card_count = employee.objects.exclude(DelTag = ud).filter(OffDuty__lt = uo).exclude(Card__isnull=True).exclude(Card='').count()
    ad_count = BioData.objects.filter(bio_type=9).exclude(UserID__DelTag = ud).filter(UserID__OffDuty__lt=uo).values_list('UserID').distinct().count()  # 已登记可见光面部人数
    in_count = records.objects.filter(inorout=0, TTime__gte=TDate,TTime__lt=TDate + datetime.timedelta(days=1)).exclude(pin=0).all().count()
    out_count = records.objects.filter(inorout=1, TTime__gte=TDate,TTime__lt=TDate + datetime.timedelta(days=1)).exclude(pin=0).all().count()
    return ({'finger_count': finger_count, 'emp_count': emp_count,'card_count':card_count,'ad_count':ad_count,'in_count':in_count,'out_count':out_count})


def get_deptnums():
    from mysite.iclock.reportsview import get_leave_del_emp_tag
    now = datetime.datetime.now()
    st = datetime.datetime(now.year, now.month, now.day, 0, 0, 0).strftime("%Y-%m-%d %H:%M:%S")
    et = (datetime.datetime(now.year, now.month, now.day, 0, 0, 0) + datetime.timedelta(days=1)).strftime(
        "%Y-%m-%d %H:%M:%S")
    uo,ud = get_leave_del_emp_tag()
    emp_sql = """
                select count(*),defaultdeptid,d.deptname 
                from userinfo us
                left join departments d on d.deptid = us.defaultdeptid
                where us.deltag <%s and us.offduty<%s
                GROUP BY defaultdeptid,d.deptname
                order by count(*) DESC
    """%(ud,uo)
    '''	
    emp_sql = """
		select count(*),defaultdeptid,d.deptname  from acc_records ar
		left join userinfo us on us.badgenumber = ar.pin
		left join departments d on d.deptid = us.defaultdeptid 
		where  pin <> 0 and ttime >= '%s' and ttime <'%s'
		GROUP BY defaultdeptid order by count(*) DESC
    """%(st,et)	
    '''	
    #print ('emp_sql---',emp_sql)
    cs = customSql(emp_sql,[],False)
    obj = cs.fetchall()
    dept_count  = ['-','-','-','-','-']
    dept_name = []

    k = 0
    for t in obj:
        dept_count[k]=t[0]
        dept_name.append(t[2])
        k = k + 1
        if k ==5:
           break

    return ({'dept_name': dept_name, 'dept_count': dept_count})

def get_acc_event_total():
    #0 — 19  定义为正常事件 (已经用完，由200 — 254中增加)
    #20 — 99  定义为异常事件
    #100 — 199  定义为报警事件
    #200 — 254  定义为正常事件
    now = datetime.datetime.now()
    hour_range = datetime.datetime.now().hour + 1
    event_count = []
    event_name =[_(u"Normal events"), _(u"Abnormal events"), _(u"Alarm event")]
    st = datetime.datetime(now.year, now.month, now.day, 0, 0, 0)
    et = st + datetime.timedelta(days=1)
    event_obj = records.objects.filter(TTime__gte=st, TTime__lt=et)
    nomal_event_0_19 = event_obj.filter(event_no__gte=0,event_no__lt=20).count()
    nomal_event_200_254 = event_obj.filter(event_no__gte=200,event_no__lt=254).count()
    abnormal_event_20_99  = event_obj.filter(event_no__gte=20,event_no__lt=99).count()
    alarm_event_100_199 = event_obj.filter(event_no__gte=100,event_no__lt=199).count()

    value1 ={}
    value1['value'] = nomal_event_0_19 + nomal_event_200_254
    value1['name'] = u'%s' % _(u"Normal events")
    value2 ={}
    value2['value'] = u'%s'% (abnormal_event_20_99)
    value2['name'] = u'%s' % _(u"Abnormal events")
    value3 ={}
    value3['value'] =u'%s'% alarm_event_100_199
    value3['name'] = u'%s' % _(u"Alarm event")

    event_count.append(value1)
    event_count.append(value2)
    event_count.append(value3)
    return event_count
     


def get_top5_event_total():
    now = datetime.datetime.now()
    st = datetime.datetime(now.year, now.month, now.day, 0, 0, 0).strftime("%Y-%m-%d %H:%M:%S")
    et = (datetime.datetime(now.year, now.month, now.day, 0, 0, 0) + datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
    event_sql = """
       select count(event_no),event_no from acc_records
       where ttime >='%s' and ttime <'%s'
       group by event_no order by count(event_no) DESC 
	   """%(st,et)
              
    #print ('event_sql',event_sql)
    cs = customSql(event_sql,[],False)
    obj = cs.fetchall()
    dev_event_name = []
    dev_event_count = []
    event_dict = {}
    for t in EVENT_CHOICES:
        event_dict[t[0]]=t[1]
		
    k = 0
    for i in obj:
        dev_event_count.append(int(i[0]))
        dev_event_name.append('%s'%event_dict[i[1]])
        k = k + 1
        if k ==5:
           break	

    return ({'dev_event_count': dev_event_count, 'dev_event_name': dev_event_name})



def get_today_24_hour_inout_total():
    now = datetime.datetime.now()
    hour_range = datetime.datetime.now().hour + 1
    hour_times = []
    hour_times = ['-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-']	
    for i in range(hour_range):
        st = datetime.datetime(now.year, now.month, now.day, i, 0, 0)
        et = st + datetime.timedelta(hours=1)
        hour_count = records.objects.filter(TTime__gte=st, TTime__lt=et,inorout__isnull=False).count()
        hour_times[i]=hour_count

    return  {'hour_times':hour_times}


def get_device_status_number():
    acc_device=iclock.objects.filter(ProductType__in=[4,5,25,15]).exclude(DelTag=1)
    offline=0
    pause=0
    for obj in acc_device:
        device_status=obj.getDynState()
        if device_status==DEV_STATUS_OFFLINE:
            offline+=1
        elif device_status==DEV_STATUS_PAUSE:
            pause+=1
    online=acc_device.count()-offline-pause
    return ({'online':online,'offline':offline})


@csrf_protect
@login_required
def newInoutLog(request): 
    if request.method=='GET':
        cc={}
        Title=GetParamValue('opt_basic_sitetitle')
        cc['title'] = Title
        return render(request,"acc/dlogcheck_blue.html",cc)
    else:
        result={}
        lasttid=int(request.GET.get("lasttid","-1"))
        result['lastId']=lasttid
        now=datetime.date.today()
        logs=records.objects.filter(id__gt=lasttid,TTime__gte=now).exclude(pin=0).order_by("-id")
        result['emp_date']={}
        result['depts_date']={}
        if logs:
            ll={}
            ll['pin']=logs[0].pin
            ll['name']=logs[0].name
            if logs[0].employee()!='':
                ll['deptname']=logs[0].employee().Dept().DeptName
            else:
                ll['deptname']=''
            ll['ttime']=logs[0].TTime.strftime("%Y-%m-%d %H:%M:%S")
            if logs[0].Device():
                ll['event_point_name']=logs[0].event_point_name			    
                ll['sn']=logs[0].Device().Alias or logs[0].Device().SN
            if logs[0].Device( ):
                cmapfile = getStoredFileName("photo", None, '{}.jpg'.format(logs[0].pin))
                if os.path.exists(cmapfile):
                    ll['urls']=logs[0].pin
                else:
                    ll['urls']=""

            if logs[0].employee() !='' :
                SSN = logs[0].employee().SSN
                ll['ssn'] = f'{SSN[:6]}********{SSN[-4:]}' if SSN else ''
            else:
                ll['ssn']=""
            ll['event_no']=logs[0].event_no
            inorout=logs[0].inorout
            ll['feifa']=""
            if inorout==0:
                ll['inorout']=u"%s"%(_(u'enter'))
            elif inorout==1:
                ll['inorout']=u"%s"%(_(u'going out'))
            else:
                ll['inorout']=''
            res=records.objects.filter(pin=ll['pin'],TTime__lt=logs[0].TTime).order_by("-TTime")
            if res:
                if inorout==res[0].inorout:
                    ll['feifa']="F"
            result['emp_date']=ll
            res=records.objects.filter(TTime__gte=now).order_by("-TTime")
            emps=[]
            depts_date=[]
            dlist={}
            for r in res:
                if r.inorout==0 and r.pin not in emps:
                    try:
                        deptid=str(r.employee().Dept().DeptID)
                    except:
                        continue
                    try:
                        dlist[deptid]+=1
                    except:
                        dlist[deptid]=1
                emps.append(r.pin)
            for k in dlist.keys():
                deptname=department.objByID(k).DeptName
                plan = u"<div>{}</div>".format(
                    _(u"%(dept)s present: %(num)s people") % {                        "dept": deptname,
                        "num": dlist[k],
                    }
                )
                depts_date.append(plan)
            result['depts_date']=depts_date
            result['lastId']=logs[0].id
        return getJSResponse(result)
#@login_required	
#def DeviceState(request): 
#	logs=AccDoor.objects.all().order_by('id')
#
#	result={}
#	lines=[]
#	door_state={'disabled':"door_disabled.png",
#        'default':"door_default.jpg",
#        'nosensor':"door_nosensor.png",
#        'offline': "door_offline.png",
#        'opened': "door_opened.png",
#        'closed': "door_closed.png",
#        'alarm': "door_alarm.png",
#        'open_timeout': "door_open_timeout.png"
#	}
#
#	lines.append(line.copy())
#	result['msg']='OK'
#	result['data']=lines
#	result['ret']=len(lines)
#	return getJSResponse(result)
#
#	
#
#MAX_PHOTO_WIDTH=400
#
