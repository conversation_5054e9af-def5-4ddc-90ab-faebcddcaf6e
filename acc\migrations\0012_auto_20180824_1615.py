# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2018-08-24 16:15
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0019_auto_20180824_1615'),
        ('acc', '0011_auto_20180402_1234'),
    ]

    operations = [
        migrations.CreateModel(
            name='empZone',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('UserID', models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee')),
            ],
            options={
                'verbose_name_plural': 'EmpZone',
                'db_table': 'empzone',
                'default_permissions': ('browse', 'add', 'change', 'delete'),
                'verbose_name': 'EmpZone',
            },
        ),
        migrations.AlterModelOptions(
            name='acc_employee',
            options={'verbose_name': 'Access Control Settings', 'verbose_name_plural': 'acc_employee'},
        ),
        migrations.AlterModelOptions(
            name='accdoor',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'door', 'verbose_name_plural': 'door'},
        ),
        migrations.AlterModelOptions(
            name='accwiegandfmt',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Wegenka format', 'verbose_name_plural': 'Wegenka format'},
        ),
        migrations.AlterModelOptions(
            name='antipassback',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'anti-submarine setting', 'verbose_name_plural': 'antiback'},
        ),
        migrations.AlterModelOptions(
            name='auxin',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'enter', 'verbose_name_plural': 'enter'},
        ),
        migrations.AlterModelOptions(
            name='auxout',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'enter', 'verbose_name_plural': 'enter'},
        ),
        migrations.AlterModelOptions(
            name='combopen',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('addemps_combopen', 'Add emps'), ('delallemps_combopen', 'Delete allemps')), 'verbose_name': 'group name', 'verbose_name_plural': 'combopen'},
        ),
        migrations.AlterModelOptions(
            name='combopen_comb',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Open door combination', 'verbose_name_plural': 'combopen_comb'},
        ),
        migrations.AlterModelOptions(
            name='combopen_door',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Open door combination name', 'verbose_name_plural': 'combopen_door'},
        ),
        migrations.AlterModelOptions(
            name='combopen_emp',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Opening the door combination', 'verbose_name_plural': 'combopen_emp'},
        ),
        migrations.AlterModelOptions(
            name='firstopen',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('addemps_firstopen', 'Add emps'), ('delallemps_firstopen', 'Delete allemps')), 'verbose_name': 'The first person is always open', 'verbose_name_plural': 'The first person is always open'},
        ),
        migrations.AlterModelOptions(
            name='firstopen_emp',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'The first person is always open', 'verbose_name_plural': 'FirstOpen_emp'},
        ),
        migrations.AlterModelOptions(
            name='iclockzone',
            options={'verbose_name': 'area', 'verbose_name_plural': 'area'},
        ),
        migrations.AlterModelOptions(
            name='interlock',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (), 'verbose_name': 'Interlocking rules', 'verbose_name_plural': 'interlock'},
        ),
        migrations.AlterModelOptions(
            name='level',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('addemps_level', 'Add emps'), ('delallemps_level', 'Delete allemps')), 'verbose_name': 'Access Control Group', 'verbose_name_plural': 'AccLevel'},
        ),
        migrations.AlterModelOptions(
            name='level_door',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Access Control Staff', 'verbose_name_plural': 'level_emp'},
        ),
        migrations.AlterModelOptions(
            name='level_emp',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Access Control Staff', 'verbose_name_plural': 'level_emp'},
        ),
        migrations.AlterModelOptions(
            name='linkage',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Linkage', 'verbose_name_plural': 'linkage'},
        ),
        migrations.AlterModelOptions(
            name='linkage_inout',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'Linkage', 'verbose_name_plural': 'linkage_inout'},
        ),
        migrations.AlterModelOptions(
            name='linkage_trigger',
            options={'verbose_name': 'Linkage', 'verbose_name_plural': 'linkage_trigger'},
        ),
        migrations.AlterModelOptions(
            name='records',
            options={'default_permissions': (), 'permissions': (('monitor_oplog', 'Real Monitor'), ('acc_records', 'Acc Records'), ('acc_reports', 'Acc Reports')), 'verbose_name': 'Access Control Record', 'verbose_name_plural': 'Access Control Record'},
        ),
        migrations.AlterModelOptions(
            name='searchs',
            options={'verbose_name': 'equipment', 'verbose_name_plural': 'device'},
        ),
        migrations.AlterModelOptions(
            name='zone',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'area'},
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='acc_enddate',
            field=models.DateTimeField(blank=True, null=True, verbose_name='End Time'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='acc_startdate',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Start time'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='acc_super_auth',
            field=models.SmallIntegerField(blank=True, choices=[(0, 'No'), (15, 'Yes')], default=0, null=True, verbose_name='Superuser'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='isblacklist',
            field=models.BooleanField(default=False, verbose_name='Open Blacklist'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='morecard_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.combopen', verbose_name='Multiple Openers'),
        ),
        migrations.AlterField(
            model_name='acc_employee',
            name='set_valid_time',
            field=models.BooleanField(default=False, verbose_name='Set ValidTime'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='back_lock',
            field=models.BooleanField(default=True, verbose_name='closed door lock'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='card_intervaltime',
            field=models.IntegerField(default=1, help_text='Seconds (range: 0-254, 0 means no interval)', null=True, verbose_name='Swipe interval'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='device',
            field=models.ForeignKey(help_text='Not allowed to modify', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='device name'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='door_name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='door name'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='door_no',
            field=models.IntegerField(help_text='Not allowed to modify', null=True, verbose_name='door number'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='door_sensor_status',
            field=models.IntegerField(choices=[(0, 'nothing'), (1, 'Normally open'), (2, 'Normally closed')], default=0, help_text='The door magnetic type is not available, the door magnetic delay is not editable', null=True, verbose_name='door magnetic type'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='duration_apb',
            field=models.IntegerField(blank=True, default=0, editable=False, help_text='minute(5-120)', null=True, verbose_name='Into the anti-submarine time'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='force_pwd',
            field=models.CharField(blank=True, default='', help_text='(maximum 8-digit integer)', max_length=18, null=True, verbose_name='duress password'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='global_apb',
            field=models.IntegerField(blank=True, choices=[(0, 'No'), (1, 'Yes')], editable=False, null=True, verbose_name='Enable regional anti-submarine'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='imap',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccMap', verbose_name='affiliation map'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='is_att',
            field=models.IntegerField(blank=True, editable=False, null=True, verbose_name='Attendance'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='lock_active',
            field=models.ForeignKey(default=1, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lockactive_set', to='acc.timezones', verbose_name='door valid time period'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='lock_delay',
            field=models.IntegerField(default=5, help_text='Seconds (range 0-254, 0 stands for normally closed)', null=True, verbose_name='Lock drive duration'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='long_open',
            field=models.ForeignKey(blank=True, help_text='When the door is always in the normally open state, it is possible to release the normally open state by continuously swiping the card five times during this time period.', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='longopen_set', to='acc.timezones', verbose_name='Door open time period'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='opendoor_type',
            field=models.IntegerField(choices=[(0, 'automatic matching'), (1, 'fingerprint only'), (2, 'Work only number'), (3, 'password only'), (4, 'only card'), (5, 'fingerprint or password'), (6, 'card or fingerprint'), (7, 'card or password'), (8, 'Work number plus fingerprint'), (9, 'Fingerprint plus password'), (10, 'Caga fingerprint'), (11, 'Caga Password'), (12, 'Fingerprint plus password plus card'), (13, 'Work number plus fingerprint plus password'), (14, 'Work number plus fingerprint or card plus fingerprint'), (15, 'human face'), (16, 'Face plus fingerprint'), (17, 'Face plus password'), (18, 'Face plus card'), (19, 'Face plus fingerprint plus card'), (20, 'Face plus fingerprint plus password'), (21, 'Finger vein'), (22, 'Finger vein plus password'), (23, 'Finger vein plus card'), (24, 'Finger vein plus card plus password'), (200, 'other')], default=6, null=True, verbose_name='Ways of identifying'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='reader_type',
            field=models.IntegerField(blank=True, choices=[(1, 'card reading head'), (2, 'fingerprint reader')], default=0, null=True, verbose_name='reader type'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='sensor_delay',
            field=models.IntegerField(blank=True, default=15, help_text='Second (range 1-254), the door magnetic delay must be greater than the lock drive duration', null=True, verbose_name='door magnetic delay'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='supper_pwd',
            field=models.CharField(blank=True, default='', help_text='(maximum 8-digit integer)', max_length=18, null=True, verbose_name='emergency password'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='wiegand_fmt',
            field=models.ForeignKey(default=1, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccWiegandFmt', verbose_name='Wegenka format'),
        ),
        migrations.AlterField(
            model_name='accdoormap',
            name='door',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='door'),
        ),
        migrations.AlterField(
            model_name='accdoormap',
            name='imap',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccMap', verbose_name='affiliation map'),
        ),
        migrations.AlterField(
            model_name='accdoormap',
            name='left',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='left margin'),
        ),
        migrations.AlterField(
            model_name='accdoormap',
            name='top',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='top margin'),
        ),
        migrations.AlterField(
            model_name='accmap',
            name='height',
            field=models.FloatField(blank=True, default=0, editable=False, null=True, verbose_name='height'),
        ),
        migrations.AlterField(
            model_name='accmap',
            name='map_name',
            field=models.CharField(default='', max_length=30, null=True, unique=True, verbose_name='map name'),
        ),
        migrations.AlterField(
            model_name='accmap',
            name='map_path',
            field=models.CharField(blank=True, default='', max_length=30, null=True, verbose_name='map path'),
        ),
        migrations.AlterField(
            model_name='accmap',
            name='mulriple',
            field=models.IntegerField(blank=True, default=5, editable=False, null=True, verbose_name='multiple'),
        ),
        migrations.AlterField(
            model_name='accmap',
            name='width',
            field=models.FloatField(blank=True, default=0, editable=False, null=True, verbose_name='width'),
        ),
        migrations.AlterField(
            model_name='accmap_zone',
            name='code',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='acc.zone'),
        ),
        migrations.AlterField(
            model_name='accmap_zone',
            name='imap',
            field=models.ForeignKey(blank=True, editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccMap', verbose_name='affiliation map'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='cid_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='CID end bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='cid_start',
            field=models.IntegerField(blank=True, null=True, verbose_name='CID start bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='comp_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='Company code end bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='comp_start',
            field=models.IntegerField(blank=True, null=True, verbose_name='Company code start bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='even_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='even check end bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='even_start',
            field=models.IntegerField(blank=True, null=True, verbose_name='even check start bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='odd_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='odd check end bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='odd_start',
            field=models.IntegerField(blank=True, null=True, verbose_name='odd check start bit'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='wiegand_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='Total number of digits'),
        ),
        migrations.AlterField(
            model_name='accwiegandfmt',
            name='wiegand_name',
            field=models.CharField(default='', max_length=30, verbose_name='Weiganka format name'),
        ),
        migrations.AlterField(
            model_name='antipassback',
            name='apb_rule',
            field=models.IntegerField(null=True, verbose_name='anti-submarine mode'),
        ),
        migrations.AlterField(
            model_name='antipassback',
            name='device',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='equipment'),
        ),
        migrations.AlterField(
            model_name='auxin',
            name='aux_name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='auxin',
            name='aux_no',
            field=models.IntegerField(help_text='Not allowed to modify', null=True, verbose_name='Numbering'),
        ),
        migrations.AlterField(
            model_name='auxin',
            name='device',
            field=models.ForeignKey(default=None, help_text='Not allowed to modify', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='device name'),
        ),
        migrations.AlterField(
            model_name='auxin',
            name='printer_name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='screen name'),
        ),
        migrations.AlterField(
            model_name='auxin',
            name='remark',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='auxout',
            name='aux_name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='auxout',
            name='aux_no',
            field=models.IntegerField(help_text='Not allowed to modify', null=True, verbose_name='Numbering'),
        ),
        migrations.AlterField(
            model_name='auxout',
            name='device',
            field=models.ForeignKey(default=None, help_text='Not allowed to modify', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='device name'),
        ),
        migrations.AlterField(
            model_name='auxout',
            name='printer_name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='screen name'),
        ),
        migrations.AlterField(
            model_name='auxout',
            name='remark',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='combopen',
            name='name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='Opening staff group name'),
        ),
        migrations.AlterField(
            model_name='combopen',
            name='remark',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='combopen_door',
            name='door',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='door'),
        ),
        migrations.AlterField(
            model_name='combopen_door',
            name='name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='Open door combination name'),
        ),
        migrations.AlterField(
            model_name='combopen_emp',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='combopen_emp',
            name='combopen',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.combopen', verbose_name='Open door combination'),
        ),
        migrations.AlterField(
            model_name='firstopen',
            name='door',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='door name'),
        ),
        migrations.AlterField(
            model_name='firstopen',
            name='timeseg',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.timezones', verbose_name='Access Control Period'),
        ),
        migrations.AlterField(
            model_name='firstopen_emp',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='firstopen_emp',
            name='firstopen',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.FirstOpen', verbose_name='door'),
        ),
        migrations.AlterField(
            model_name='iclockzone',
            name='zone',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.zone', verbose_name='area'),
        ),
        migrations.AlterField(
            model_name='interlock',
            name='device',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='equipment'),
        ),
        migrations.AlterField(
            model_name='interlock',
            name='interlock_rule',
            field=models.IntegerField(null=True, verbose_name='Interlocking rules'),
        ),
        migrations.AlterField(
            model_name='interlock',
            name='remark',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='level',
            name='is_visitor',
            field=models.IntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0, editable=False, verbose_name='Visitor Access Control Group'),
        ),
        migrations.AlterField(
            model_name='level',
            name='itype',
            field=models.IntegerField(blank=True, choices=[(0, 'Access Control Group')], default=0, editable=False, null=True, verbose_name='Permission Group Type'),
        ),
        migrations.AlterField(
            model_name='level',
            name='name',
            field=models.CharField(max_length=30, verbose_name='Privilege Group Name'),
        ),
        migrations.AlterField(
            model_name='level',
            name='timeseg',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='acc.timezones', verbose_name='period'),
        ),
        migrations.AlterField(
            model_name='level_door',
            name='door',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.AccDoor', verbose_name='door'),
        ),
        migrations.AlterField(
            model_name='level_door',
            name='level',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.level', verbose_name='Access Control Group'),
        ),
        migrations.AlterField(
            model_name='level_emp',
            name='UserID',
            field=models.ForeignKey(db_column='userid_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='Access Control Team'),
        ),
        migrations.AlterField(
            model_name='level_emp',
            name='level',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.level', verbose_name='Access Control Group'),
        ),
        migrations.AlterField(
            model_name='linkage',
            name='device',
            field=models.ForeignKey(help_text='Not allowed to modify', null=True, on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='device name'),
        ),
        migrations.AlterField(
            model_name='linkage',
            name='name',
            field=models.CharField(default='', max_length=30, null=True, verbose_name='Linked name'),
        ),
        migrations.AlterField(
            model_name='linkage',
            name='remark',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='linkage_inout',
            name='linkage',
            field=models.ForeignKey(help_text='Not allowed to modify', null=True, on_delete=django.db.models.deletion.CASCADE, to='acc.linkage'),
        ),
        migrations.AlterField(
            model_name='records',
            name='TTime',
            field=models.DateTimeField(db_column='ttime', verbose_name='time'),
        ),
        migrations.AlterField(
            model_name='records',
            name='card_no',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='card number'),
        ),
        migrations.AlterField(
            model_name='records',
            name='dev_serial_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='Device serial number'),
        ),
        migrations.AlterField(
            model_name='records',
            name='event_no',
            field=models.IntegerField(choices=[(-1, 'nothing'), (0, 'Normal opening'), (1, 'Punch during Passage Mode Time Zone'), (2, 'The first person opens the door (swipe the card)'), (3, 'Multiple people open the door normally'), (4, 'Emergency password opens'), (5, 'Open during Passage Mode Time Zone'), (6, 'trigger linkage event'), (7, 'Cancel the alarm'), (8, 'Open the door remotely'), (9, 'remote closing'), (10, 'Disable the usual opening time of the day'), (11, 'Enable the usual opening time of the day'), (12, 'Turn on auxiliary output'), (13, 'Turn off auxiliary output'), (14, 'Normally open the door by fingerprint'), (15, 'Multiple people open the door (by fingerprint)'), (16, 'Filling fingerprints during the normally open time period'), (17, 'Kajia Fingerprint Opens the Door'), (18, 'First card opens (by fingerprint)'), (19, 'First card opening (kajia fingerprint)'), (20, 'Swipe card interval is too short'), (21, 'Do not valid time period'), (22, 'Illegal time period'), (23, 'Unauthorized access'), (24, 'anti-submarine'), (25, 'Interlock failed'), (26, 'Multiple verification waits'), (27, 'People are not registered'), (28, 'door open timeout'), (29, 'The card has expired'), (30, 'wrong password'), (31, 'The fingerprint interval is too short'), (32, 'Multiple verification waits'), (33, 'Expired validity period'), (34, 'People are not registered'), (35, 'Do not valid time period'), (36, 'The door is not valid (press the exit button)'), (37, 'Unable to close the door during the normal opening period'), (38, 'The card has been lost'), (39, 'blacklist'), (41, 'Verification error'), (42, 'The Wiegand Card format is wrong'), (44, 'Anti-submarine verification failed'), (48, 'Multiple people fail to open the door'), (51, 'Multi-person verification'), (52, 'Multiple authentication failed'), (63, 'The door has been locked'), (64, 'The door button is not in the time period'), (65, 'Auxiliary input is not within the time period'), (100, 'tamper alarm'), (101, 'Duress password to open the door'), (102, 'The door was accidentally opened'), (104, 'Invalid card continuously swipe 5 times'), (200, 'The door is open'), (201, 'The door is closed'), (202, 'Going out the door to open the door'), (204, 'The end of the normally open time period'), (205, 'Remote opening is always open'), (206, 'Device startup'), (207, 'Password opens'), (208, 'Super user opens the door'), (209, 'Trigger exit button (locked)'), (216, 'In the normally open time period (by password)'), (220, 'Auxiliary input point disconnected'), (221, 'Auxiliary input point short circuit'), (225, 'The input point is back to normal'), (226, 'Input point alarm'), (222, 'Anti-submarine verification succeeded'), (223, 'anti-submarine verification'), (224, 'Press the doorbell'), (233, 'Activate Lockdown'), (232, 'Operation Success'), (234, 'Deactivate Lockdown'), (300, 'Trigger Global Linkage'), (500, 'Global Anti-Passback(logical)')], null=True, verbose_name='Event Description'),
        ),
        migrations.AlterField(
            model_name='records',
            name='event_point_name',
            field=models.CharField(blank=True, max_length=30, null=True, verbose_name='Event point'),
        ),
        migrations.AlterField(
            model_name='records',
            name='inorout',
            field=models.IntegerField(choices=[(0, 'in'), (1, 'out'), (2, 'nothing')], null=True, verbose_name='in and out'),
        ),
        migrations.AlterField(
            model_name='records',
            name='name',
            field=models.CharField(blank=True, max_length=24, null=True, verbose_name='name'),
        ),
        migrations.AlterField(
            model_name='records',
            name='pin',
            field=models.CharField(blank=True, max_length=24, null=True, verbose_name='personnel number'),
        ),
        migrations.AlterField(
            model_name='records',
            name='reader_name',
            field=models.CharField(blank=True, max_length=30, null=True, verbose_name='reader name'),
        ),
        migrations.AlterField(
            model_name='records',
            name='verify',
            field=models.IntegerField(choices=[(0, 'automatic matching'), (1, 'fingerprint only'), (2, 'Work only number'), (3, 'password only'), (4, 'only card'), (5, 'fingerprint or password'), (6, 'card or fingerprint'), (7, 'card or password'), (8, 'Work number plus fingerprint'), (9, 'Fingerprint plus password'), (10, 'Caga fingerprint'), (11, 'Caga Password'), (12, 'Fingerprint plus password plus card'), (13, 'Work number plus fingerprint plus password'), (14, 'Work number plus fingerprint or card plus fingerprint'), (15, 'human face'), (16, 'Face plus fingerprint'), (17, 'Face plus password'), (18, 'Face plus card'), (19, 'Face plus fingerprint plus card'), (20, 'Face plus fingerprint plus password'), (21, 'Finger vein'), (22, 'Finger vein plus password'), (23, 'Finger vein plus card'), (24, 'Finger vein plus card plus password'), (200, 'other')], default=200, null=True, verbose_name='verification'),
        ),
        migrations.AlterField(
            model_name='searchs',
            name='Protype',
            field=models.CharField(blank=True, db_column='protype', editable=False, help_text='Communication Type', max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='searchs',
            name='State',
            field=models.IntegerField(db_column='state', default=1, editable=False, verbose_name='status'),
        ),
        migrations.AlterField(
            model_name='timezones',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='timezones',
            name='Name',
            field=models.CharField(blank=True, db_column='name', max_length=30, verbose_name='time period name'),
        ),
        migrations.AlterField(
            model_name='timezones',
            name='remark',
            field=models.CharField(blank=True, max_length=70, null=True, verbose_name='Remarks'),
        ),
        migrations.AlterField(
            model_name='timezones',
            name='tz',
            field=models.TextField(null=True, verbose_name='time'),
        ),
        migrations.AlterField(
            model_name='useraccdevice',
            name='SN',
            field=models.ForeignKey(db_column='sn', on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='equipment'),
        ),
        migrations.AlterField(
            model_name='useraccdevice',
            name='UserID',
            field=models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='useracdevice',
            name='SN',
            field=models.ForeignKey(db_column='sn', on_delete=django.db.models.deletion.CASCADE, to='iclock.iclock', verbose_name='equipment'),
        ),
        migrations.AlterField(
            model_name='useracdevice',
            name='UserID',
            field=models.ForeignKey(db_column='userid', on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='personnel'),
        ),
        migrations.AlterField(
            model_name='useracprivilege',
            name='IsUseGroup',
            field=models.IntegerField(choices=[(0, 'Custom time period'), (1, 'Using Group Settings')], db_column='isusegroup', default=0, null=True, verbose_name='IsUseGroup'),
        ),
        migrations.AlterField(
            model_name='useracprivilege',
            name='UserID',
            field=models.ForeignKey(db_column='userid', default=None, on_delete=django.db.models.deletion.CASCADE, to='iclock.employee', verbose_name='\u5458\u5de5'),
        ),
        migrations.AlterField(
            model_name='zone',
            name='DelTag',
            field=models.IntegerField(blank=True, db_column='deltag', default=0, editable=False, null=True, verbose_name='delete tag'),
        ),
        migrations.AlterField(
            model_name='zone',
            name='code',
            field=models.CharField(help_text='The maximum length is no more than 40 characters. After modifying the area number, it will not change the area where the device is located.', max_length=40, verbose_name='area number'),
        ),
        migrations.AlterField(
            model_name='zone',
            name='name',
            field=models.CharField(max_length=40, verbose_name='area name'),
        ),
        migrations.AlterField(
            model_name='zone',
            name='parent',
            field=models.IntegerField(blank=True, db_column='parent_id', default=0, verbose_name='superior area'),
        ),
        migrations.AlterField(
            model_name='zone',
            name='remark',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Remarks'),
        ),
        migrations.AddField(
            model_name='empzone',
            name='zone',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.zone'),
        ),
        migrations.AlterUniqueTogether(
            name='empzone',
            unique_together=set([('UserID', 'zone')]),
        ),
    ]
