## 访客模块
### 访客模块-访客黑名单功能点新增黑名单
#### 访客模块访客黑名单功能点RAG检索锚点
<!-- RAG检索锚点: 新增黑名单、访客黑名单、黑名单管理、禁止访问、安全管理 -->

#### 新增黑名单功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示新增表单]
    D --> E[填写黑名单信息]
    E --> F[验证表单数据]
    F --> G{数据验证}
    G -->|验证失败| H[显示错误信息]
    G -->|验证通过| I[检查身份证号重复]
    I --> J{身份证号是否重复}
    J -->|重复| K[提示身份证号已存在]
    J -->|不重复| L[保存黑名单信息]
    L --> M[同步到访客设备]
    M --> N[记录操作日志]
    N --> O[返回成功信息]
    H --> E
    K --> E
    C --> P[结束]
    O --> P
```
#### 新增黑名单功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增黑名单业务流程。
2. 权限验证：验证当前用户是否具有黑名单管理权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行新增操作
3. 新增表单显示：
   - 显示黑名单信息录入表单
   - 提供必填和可选字段的输入界面
   - 显示字段验证规则和提示信息
4. 黑名单信息填写：
   - 访客姓名（name）：必填，黑名单人员的姓名
   - 身份证号（ssn）：必填，黑名单人员的身份证号码
   - 性别（gender）：可选，黑名单人员的性别
   - 手机号码（mobile）：可选，黑名单人员的联系方式
   - 备注信息：可选，加入黑名单的原因说明
5. 表单数据验证：
   - 验证必填字段是否已填写
   - 检查身份证号格式的正确性
   - 验证手机号码格式的有效性
   - 确保姓名长度符合要求
6. 身份证号重复检查：
   - 检查输入的身份证号是否已存在于黑名单中
   - 通过Blacklist表的唯一约束验证
   - 防止同一人员重复加入黑名单
7. 黑名单信息保存：
   - 将黑名单信息保存到Blacklist表中
   - 设置创建时间（create_time）为当前时间
   - 设置修改时间戳（OpStamp）为当前时间
   - 确保数据的完整性和一致性
8. 同步到访客设备：
   - 调用zk_set_vistor_data函数同步黑名单数据
   - 将黑名单信息推送到所有在线的访客设备
   - 确保设备能够实时识别和拦截黑名单人员
   - 设置缓存标记避免重复推送
9. 操作日志记录：记录黑名单添加操作的详细日志。
10. 流程完成：返回操作成功信息，完成黑名单添加流程。

#### 新增黑名单功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 黑名单人员无法进行访客预约
   - 预约时会检查申请人是否在黑名单中
   - 黑名单验证在预约保存时执行

2. **与访客登记模块的关联**：
   - 黑名单人员无法进行访客登记
   - 登记时会检查访客身份证号是否在黑名单中
   - 黑名单验证在登记保存时执行

3. **与设备管理模块的关联**：
   - 黑名单信息需要同步到所有访客设备
   - 设备用于实时识别和拦截黑名单人员
   - 设备状态影响黑名单信息的同步

4. **与安全管理模块的关联**：
   - 黑名单是访客安全管理的重要组成部分
   - 黑名单变更需要记录详细的安全日志
   - 黑名单信息属于敏感数据需要权限保护

5. **与系统配置模块的关联**：
   - 黑名单功能的开启受系统配置影响
   - 黑名单数量可能受系统限制
   - 黑名单同步策略由系统配置决定

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-访客黑名单功能点删除黑名单
#### 访客模块访客黑名单功能点RAG检索锚点
<!-- RAG检索锚点: 删除黑名单、移除黑名单、黑名单删除、解除黑名单、黑名单管理删除 -->

#### 删除黑名单功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的黑名单]
    D --> E[确认删除操作]
    E --> F{用户确认}
    F -->|取消删除| G[返回黑名单列表]
    F -->|确认删除| H[执行物理删除]
    H --> I[从设备清除黑名单]
    I --> J[记录操作日志]
    J --> K[返回成功信息]
    C --> L[结束]
    G --> L
    K --> L
```
#### 删除黑名单功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除黑名单业务流程。
2. 权限验证：验证当前用户是否具有黑名单删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行删除操作
3. 黑名单选择：
   - 从黑名单列表中选择要删除的记录
   - 显示黑名单人员的基本信息供确认
   - 支持单个黑名单记录的删除操作
4. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 说明删除后该人员将可以正常访问
   - 要求用户明确确认删除意图
5. 物理删除执行：
   - 从Blacklist表中物理删除黑名单记录
   - 不同于其他模块的软删除，黑名单采用物理删除
   - 确保删除后该人员不再受到访问限制
6. 从设备清除黑名单：
   - 调用zk_delete_vistor_data函数清除设备上的黑名单
   - 向所有在线的访客设备发送删除命令
   - 确保设备不再拦截该人员的访问
   - 清理相关的缓存标记
7. 操作日志记录：记录黑名单删除操作的详细日志。
8. 流程完成：返回删除成功信息，完成黑名单删除流程。

#### 删除黑名单功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 删除黑名单后该人员可以正常进行访客预约
   - 预约验证不再拦截该人员
   - 需要清理预约系统中的黑名单缓存

2. **与访客登记模块的关联**：
   - 删除黑名单后该人员可以正常进行访客登记
   - 登记验证不再拦截该人员
   - 需要清理登记系统中的黑名单缓存

3. **与设备管理模块的关联**：
   - 删除黑名单需要同步到所有访客设备
   - 设备需要清除该人员的黑名单信息
   - 设备状态影响黑名单删除的同步

4. **与安全管理模块的关联**：
   - 黑名单删除是重要的安全操作
   - 需要记录详细的安全审计日志
   - 删除操作需要更高级别的权限

5. **与系统配置模块的关联**：
   - 黑名单删除操作受系统权限配置限制
   - 删除操作需要记录到系统审计日志
   - 系统安全策略影响删除权限

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
