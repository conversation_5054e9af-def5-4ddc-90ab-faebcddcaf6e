#!/usr/bin/python
# coding:utf-8
import urllib2, json

def main():
    params = {
        "starttime": "2018-04-25 00:00:01",
        "endtime": "2018-05-12 23:00:01"
    }
    params = json.dumps(params)
    sendreq(params)

def sendreq(params):
    sendurl = 'http://127.0.0.1:81/api/v2/transaction/get/?key=5d3dqdrxvt967ktk5ohh9cu3gdq0_9x9ptfj3dyyvh84'

    wp = urllib2.urlopen(sendurl,params)
    result = wp.read()  # 获取接口返回内容

    if result:
        print (result)
    else:
        # 请求失败
        print ("get transaction error")

if __name__ == '__main__':
    main()