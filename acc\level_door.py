#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import string
from django.contrib import auth

from django.utils.translation import gettext_lazy as _
from django.template import loader, Context, RequestContext
from django.conf import settings
from django.contrib.auth.decorators import permission_required, login_required
#from mysite.iclock.models import *
from mysite.acc.models import *
#from mysite.iclock.datasproc import *
from django.core.paginator import Paginator
#from mysite.iclock.datas import *
#from mysite.core.menu import *
from mysite.utils import *
import sys
from mysite.core.zkcmdproc import *

@login_required
def savelevel(request,key):
 #   if key=='_new_':
       # print request.POST
        timseg=request.POST.get('timeseg','1')
        name=request.POST.get('name')
        authedDoor=request.POST.get('AuthedDoor','')
        is_visitor=request.POST.get('is_visitor', 0)
        only_delete = request.POST.get('only_delete', 0)
        authedList=[]
        is_timeSeg_Flag=False
        is_send=True
        last_level_doors=[]
        new_level_doors=[]
        del_level_doors=[]
        new_devs=[]
        del_devs=[]
        if authedDoor:
                authedList=authedDoor.split(',')
        try:
                rg=0
                #if '-1' in authedList:
                #        authedList.remove('-1')
                #    
                #        rg=0
                        #authedList=[]
                authedList=list(set(AccDoor.objects.filter(id__in=authedList).values_list('id',flat=True)))
                if key=='_new_':
                        lvl=level(name=name,timeseg_id=timseg,irange=rg,is_visitor=is_visitor)
                        lvl.save(force_insert=True)
                        new_level_doors=authedList
                        adminLog(time=datetime.datetime.now(),User_id=request.user.id, model=level._meta.verbose_name, object = name,action=_(u"Add")).save(force_insert=True)#
                        is_send=False
                else:
                        #l_door=level_door.objects.filter(level=int(key))
                        #for t in l_door:
                        #        last_level_doors.append(t.door_id)
                        last_level_doors=list(level_door.objects.filter(level=int(key)).values_list('door',flat=True))
                        obj=level.objects.get(id=key)
                        if obj.timeseg_id!=int(timseg):
                                is_timeSeg_Flag=True
                                #devs=list(AccDoor.objects.filter(id__in=last_level_doors).distinct().values_list('device',flat=True))
                                #for sn in devs:
                                #    delete_data(sn, 'userauthorize')#2018.7.12不需要清空，下发授权时就已经失效

                        if obj.irange==-1:
                                is_send=False
            
                        #if rg==-1:
                        #        if obj.irange==-1:
                        #               is_send=False #此次全选，上次也全选不发送
                        #if obj.irange!=-1:#上次没有选择所有
                        #else:
                         #       l_door=AccDoor.objects.filter(device__DelTag=0).order_by('id')
                        #        for t in l_door:
                         #               last_level_doors.append(t.id)

                        # only_delete为权限组设置页-删除门操作  此时上传的门id是要删除的id  而不是要保留的id
                        if only_delete:
                            del_level_doors = authedList
                            authedList = list(set(last_level_doors) - set(authedList))
                        else:
                            for t in authedList:
                                if int(t) not in last_level_doors:
                                    new_level_doors.append(int(t))
                            for t in last_level_doors:
                                if t not in authedList:
                                    del_level_doors.append(t)

                        lvl=level(id=key,name=name,timeseg_id=timseg,irange=rg,is_visitor=is_visitor)
                        lvl.save(force_update=True)
                        adminLog(time=datetime.datetime.now(),User_id=request.user.id,object=name, model=level._meta.verbose_name, action=_(u"Modify")).save(force_insert=True)#
                        level_door.objects.filter(door__in=del_level_doors,level=int(key)).delete()
                        adminLog(time=datetime.datetime.now(),User_id=request.user.id,object=name, model=level._meta.verbose_name, action=_(u"Modify the access control group")).save(
                            force_insert=True)#

                if new_level_doors:    
                        new_devs=list(AccDoor.objects.filter(id__in=new_level_doors).distinct().values_list('device',flat=True))
                if del_level_doors:        
                        del_devs=list(AccDoor.objects.filter(id__in=del_level_doors).distinct().values_list('device',flat=True))



                
                                   
                    
 #               if rg!=-1:
                for t in new_level_doors:
                    level_door(level=lvl,door_id=t).save()
                if new_devs:
                        tzobj=timezones.objects.filter(id=int(timseg))
                        if tzobj:
                                sendTimeZonesToAcc(tzobj,new_devs)
                new_devs=new_devs+del_devs
                if is_timeSeg_Flag:
                    is_send=True
                    new_devs = list(AccDoor.objects.filter(id__in=authedList).distinct().values_list('device', flat=True))
                if key!='_new_' and is_send:
                        if new_devs:
                                #sendLevelToAccEx([],[key],new_devs,del_devs,1)
                                # zk_send_acc_data([],new_devs,del_devs)#del_devs的作用是不用下发人员信息，仅下发授权
                                try:
                                    # 权限组多时，延时很多，改用线程
                                    import threading
                                    thread = threading.Thread(target=zk_send_acc_data, args=([],new_devs,del_devs))
                                    thread.start()
                                except:
                                    if settings.DEBUG:
                                        import traceback;traceback.print_exc()

        except Exception as e:
            print ("savelevel========",e)
            return getJSResponse({"ret":1,"message":u'%s'%_("Save failed")})

        return getJSResponse({"ret":0,"message":u'%s'%_("Save Success")})
    

