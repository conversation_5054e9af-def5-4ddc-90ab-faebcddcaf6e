#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import string
from django.contrib import auth
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.template import loader, Context, RequestContext
from django.conf import settings
from django.contrib.auth.decorators import permission_required, login_required
from mysite.iclock.models import *
from mysite.acc.models import *
#from mysite.iclock.datasproc import *
from django.core.paginator import Paginator
from mysite.acc.getBaseData import userZoneList
#from mysite.iclock.datas import *
#from mysite.core.menu import *
from mysite.core.zktools import *
from mysite.iclock.models import *
import sys
from mysite.core.zkcmdproc import *


@login_required
def index(request):
    if request.method == 'GET':
        tmpFile = 'acc/' + 'door_access.html'
        tmpFile = request.GET.get('t', tmpFile)
        cc = {}
        settings.PAGE_LIMIT = GetParamValue('opt_users_page_limit', '30', str(request.user.id))
        limit = int(request.GET.get('l', settings.PAGE_LIMIT))

        colmodels = [
            # {'name': 'id', 'hidden': True},
            {'name': 'doorname', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'door name'))},
            {'name': 'permname', 'sortable': False, 'width': 120, 'label': u"%s" % (_(u'Privilege Group Name'))},
            {'name': 'zonename', 'sortable': False, 'width': 120, 'label': u"%s" % (_(u'AreaName'))},
            {'name': 'doortime', 'sortable': False, 'width': 100, 'label': u"%s" % (_(u'time period name'))},
            {'name': 'SN', 'sortable': False, 'width': 120,'label': u"%s" % (_(u'devise serial number'))},
            {'name': 'emp_detail', 'sortable': False, 'width': 120, 'label': u"%s" % (_(u'personnel practice'))},
        ]
        colmodels_level = [
            # {'name': 'id', 'hidden': True},
            {'name': 'pin', 'index': '', 'width': 120, 'label': u"%s" % (_(u'Personnel number'))},
            {'name': 'name', 'width': 100, 'label': u"%s" % (_(u'Emp name'))},
            {'name': 'dept', 'width': 160, 'label': u"%s" % (_(u'DeptName'))},
        ]

        cc['limit'] = limit
        cc['colModel'] = dumps1(colmodels)
        cc['colModel_level'] = dumps1(colmodels_level)
        request.model = level

        return render(request, tmpFile, cc)

# 门设置数据(init,search)
def door_data(request):
    limit = int(request.POST.get('rows', settings.PAGE_LIMIT))
    search_param = request.GET.get('search','')
    search_sn = request.GET.get('q','')
    iclock_sn = iclock.objects.filter(Q(SN__icontains=search_sn) & Q(ProductType__in=[4, 5, 15, 25])).exclude(DelTag=1)
    if request.user.is_superuser or request.user.is_allzone:
        iclocks = iclock_sn
    else:
        zonelist = userZoneList(request.user)
        iclocks = IclockZone.objects.filter(Q(zone__in=zonelist) & Q(SN_id__in=iclock_sn)).values_list("SN",flat=True)
    objs = AccDoor.objects.filter(Q(device_id__in=iclocks) & Q(door_name__icontains=search_param)).filter(
        door_name__icontains=search_param)

    objs = objs.values('id', 'door_name', 'device_id')

    levs = level_door.objects.filter(door__in=[obj['id'] for obj in objs]).values(
            'id', 'door_id', 'level_id', 'level__name', 'level__timeseg__Name'
            )
    level_door_dict = {}
    for le in levs:
        if le['door_id'] in level_door_dict:
            level_door_dict[le['door_id']].append(le)
        else:
            level_door_dict[le['door_id']] = [le]

    dev_area = IclockZone.objects.filter(SN__in=[obj['device_id'] for obj in objs]).values('SN','zone__name')
    dev_area_dict = {}
    for de in dev_area:
        dev_area_dict[de['SN']] = de['zone__name']

    r_t = {}
    last = []
    Result = {}
    for obj in objs:
        try:
            r_t['doorname'] = obj['door_name']
            r_t['permname'] = ''
            zonename = dev_area_dict[obj['device_id']]
            r_t['zonename'] = zonename
            r_t['doortime'] = ''
            r_t['SN'] = obj['device_id']
            levs = level_door_dict.get(obj['id'], [])
            if not levs:
                r_t['emp_detail'] = '<a onclick="pin_add()" name="' + r_t['permname'] +\
                                        '" style="color:red;">' + str(_(u"Add emps")) + '</a>'
                last.append(r_t.copy())
                continue

            for lev in levs:
                r_t['permname'] = lev['level__name']
                r_t['doortime'] = lev['level__timeseg__Name']
                r_t['emp_detail'] = '<a onclick="pin_add(' + str(lev['level_id']) + ',' + str(
                    lev['id']) + ')" name="' + r_t['permname'] + '" style="color:red;">' + str(_(u"Add emps")) + '</a>'
                last.append(r_t.copy())
        except:
            if settings.DEBUG:
                import traceback;traceback.print_exc()
            print('door get fail ！！！')
            pass

    try:
        offset = int(request.POST.get('page', 1))
    except:
        offset=1
    p = Paginator(last, limit)
    iCount = p.count
    if iCount<(offset-1)*limit:
        offset=1
    page_count=p.num_pages
    pp=p.page(offset)
    objs=pp.object_list
    if offset>page_count:offset=page_count
    item_count =iCount
    Result['item_count'] = item_count
    Result['page'] = offset
    Result['limit'] = limit
    Result['from'] = (offset - 1) * limit + 1
    Result['page_count'] = page_count
    Result['datas'] = objs
    rs = "{" + """"page":""" + str(Result['page']) + "," + """"total":""" + str(Result['page_count']) + "," + """"records":""" + str(Result['item_count']) + "," + """"rows":""" + dumps(
        Result['datas']) + """}"""
    return getJSResponse(rs)

# 门权限添加人员
def employeefordoor(request):
    doorid = request.GET.get('doorid', '')  # 门权限组id
    deptIDs = request.POST.get('deptIDs', "")
    userIDs = request.POST.get('UserIDs', "")
    isContainedChild = request.POST.get('isContainChild', "")

    if doorid == '':
        return getJSResponse({"ret": 1, "message": u"%s" % _('Save failed')})
    levelID = doorid.split(',')

    if userIDs == "":
        isAllDept = False
        deptidlist = deptIDs.split(',')
        deptids = deptidlist
        if isContainedChild == "1":
            deptids, isAllDept = getContainedDept(request, deptidlist)

        if isAllDept:
            emps = employee.objects.all().exclude(OffDuty=1).exclude(DelTag=1)
        else:
            emps = employee.objects.filter(DeptID__in=deptids).exclude(OffDuty=1).exclude(DelTag=1)
    else:
        userids = userIDs.split(',')
        emps = employee.objects.filter(id__in=userids)

    for emp in emps:
        level_id = level_door.objects.filter(id=doorid).first().level_id
        leve_emp = level_emp.objects.filter(Q(UserID_id=emp) & Q(level_id=level_id))
        if leve_emp:
            continue
        level_emp(level_id=level_id,UserID=emp).save()

    # SN
    level_door_obj = level_door.objects.filter(id=doorid).first()
    devs = AccDoor.objects.filter(id=level_door_obj.door_id).distinct().values_list('device', flat=True)
    if devs:
        zk_send_acc_data(emps, devs)
    for sn in devs:
        dev=getDevice(sn)
        appendDevCmd(dev, 'INFO')
    log_object = level_door_obj.door.door_name or ''
    adminLog(time=datetime.datetime.now(), User_id=request.user.id, object=log_object,
             model=str(_('Add people by door')) + ":" + str(_('Add')),
             action=_(u"Modify")).save(force_insert=True)
    return getJSResponse({"ret":0,"message":u"%s"%_('Operation Success')})


# 门 -> 人员
def get_door_emp(request):
    id = request.GET.get('id',0)
    limit = int(request.POST.get('rows', 0))
    sidx = request.POST.get('sidx')
    if sidx == 'tz':
        sidx = 'level__timeseg__tz'
    else:
        sidx = 'level'
    sord = request.POST.get('sord')
    try:
        offset = int(request.POST.get('page', 1))
    except:
        offset = 1
    leve_id = level_door.objects.filter(id=id).first().level_id  # 门权限组id
    if sord == 'desc':
        # objs = level_emp.objects.filter(UserID=id).order_by('-' + sidx)
        objs = level_emp.objects.filter(level_id=leve_id).order_by('-' + sidx)  # 人id
    else:
        # objs = level_emp.objects.filter(UserID=id).order_by(sidx)
        objs = level_emp.objects.filter(level_id=leve_id).order_by('-' + sidx)  # 人id
    rss = {}
    if not leve_id:
        return getJSResponse(rss)

    p = Paginator(objs, limit)
    iCount = p.count
    if iCount < (offset - 1) * limit:
        offset = 1
    page_count = p.num_pages
    pp = p.page(offset)
    objs = pp.object_list
    re = []
    Result = {}
    Result['datas'] = re
    for emp in objs:
        use = employee.objects.filter(id=emp.UserID_id).first()
        d = {}
        d['pin'] = use.PIN
        d['name'] = use.EName
        name = department.objects.filter(DeptID=use.DeptID_id).first().DeptName
        d['dept'] = name
        re.append(d.copy())

    if offset > page_count:
        offset = page_count
    item_count = iCount
    Result['item_count'] = item_count
    Result['page'] = offset
    Result['limit'] = limit
    Result['from'] = (offset - 1) * limit + 1
    Result['page_count'] = page_count
    Result['datas'] = re
    rs = "{" + """"page":""" + str(Result['page']) + "," + """"total":""" + str(
        Result['page_count']) + "," + """"records":""" + str(Result['item_count']) + "," + """"rows":""" + dumps(
        Result['datas']) + """}"""
    return getJSResponse(rs)


# 删除门权限组人员
def del_door_emp(request):
    click_id = request.POST.get('click_id')
    emp_pin = request.POST.getlist('pin_list')

    # lev = level_door.objects.filter(id=click_id).first().level
    emp = employee.objects.filter(PIN__in=emp_pin)
    emp_font = employee.objects.filter(PIN__in=emp_pin)
    level_id = level_door.objects.filter(pk=click_id).first().level_id
    level_door_obj = level_door.objects.filter(id=click_id).first()
    devs = AccDoor.objects.filter(id=level_door_obj.door_id).distinct().values_list('device', flat=True)
    for font in emp_font:
        level_emps = level_emp.objects.filter(UserID_id=font).filter(level=level_id)
        # print(devs)
        level_emps.delete()
        try:
            zk_send_acc_data(emp, devs)
            # deleteEmpfromAcc(Levels,emps,keys,1)

            for sn in devs:
                dev = getDevice(sn)
                appendDevCmd(dev, 'INFO')
        except Exception as e:
            print("level_emp4444444444", e)
    log_object = level_door_obj.door.door_name or ''
    adminLog(time=datetime.datetime.now(), User_id=request.user.id, object=log_object,
             model=str(_('Add people by door')) + ":" + str(_('Delete')),
             action=_(u"Modify")).save(force_insert=True)
    return getJSResponse({"ret":0,"message":u"%s"%_('Operation Success')})
