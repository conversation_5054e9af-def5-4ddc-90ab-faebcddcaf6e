#!/usr/bin/python
# coding:utf-8
import urllib2, json

def main():
    # params = [{
    #     "deptnumber": "10001",
    #     "deptname": "开发部",
    #     "parentnumber": "1",
    #     "deptaddr": "熵基科技",
    #     "deptperson": "李四",
    #     "deptphone": "18111111111"
    # }]
    params=[{"deptnumber": "1","deptaddr": "杭州华为","deptperson": "华为","deptphone": "18122222222"}]
    # for i in range(200):
    #     params.append({})
    params = json.dumps(params)
    sendreq(params)# 请求更新部门

def sendreq(params):
    sendurl = 'http://127.0.0.1:8080/api/v2/department/update/?key=c18ew-ybhemm5v9d4mc8516sjfkkfqmx_exlctknq-fh'  # 更新部门发送的URL

    wp = urllib2.urlopen(sendurl,params)
    result = wp.read()  # 获取接口返回内容

    if result:
        print (result)

if __name__ == '__main__':
    main()