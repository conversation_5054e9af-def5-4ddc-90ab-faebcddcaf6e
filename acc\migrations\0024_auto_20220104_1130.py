# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-01-04 11:30
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0058_auto_20220104_1130'),
        ('acc', '0023_auto_20211130_1110'),
    ]

    operations = [
        migrations.CreateModel(
            name='level_dept',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('DeptID', models.ForeignKey(db_column='dept_id', editable=False, on_delete=django.db.models.deletion.CASCADE, to='iclock.department', verbose_name='Access Control Team')),
                ('level', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.CASCADE, to='acc.level', verbose_name='Access Control Group')),
            ],
            options={
                'verbose_name': 'Access Control Dept',
                'verbose_name_plural': 'level_dept',
                'default_permissions': ('browse', 'add', 'change', 'delete'),
            },
        ),
        migrations.AlterUniqueTogether(
            name='level_dept',
            unique_together=set([('level', 'DeptID')]),
        ),
    ]
