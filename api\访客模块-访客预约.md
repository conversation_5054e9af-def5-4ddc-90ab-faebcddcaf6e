## 访客模块
### 访客模块-访客预约功能点新增预约
#### 访客模块访客预约功能点RAG检索锚点
<!-- RAG检索锚点: 新增访客预约、访客预约、预约申请、访客申请、预约管理 -->

#### 新增预约功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示预约表单]
    D --> E[填写访客信息]
    E --> F[选择被访人]
    F --> G[设置访问时间]
    G --> H[填写访问事由]
    H --> I[上传访客照片]
    I --> J[验证预约数据]
    J --> K{数据验证}
    K -->|验证失败| L[显示错误信息]
    K -->|验证通过| M[检查黑名单]
    M --> N{是否在黑名单}
    N -->|在黑名单| O[提示访客被禁止]
    N -->|不在黑名单| P[保存预约记录]
    P --> Q[生成访客卡号]
    Q --> R[发送审核通知]
    R --> S[记录操作日志]
    S --> T[返回成功信息]
    L --> E
    O --> E
    C --> U[结束]
    T --> U
```
#### 新增预约功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增访客预约业务流程。
2. 权限验证：验证当前用户是否具有访客预约权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行预约操作
3. 预约表单显示：
   - 显示访客预约表单界面
   - 提供必填和可选字段的输入控件
   - 显示预约规则和注意事项
4. 访客信息填写：
   - 访客姓名（visempName）：必填，访客的真实姓名
   - 身份证号（SSN）：必填，访客的身份证号码
   - 手机号码（Mobile）：可选，访客的联系方式
   - 访客性别（VisGender）：可选，访客的性别信息
   - 访客公司（VisCompany）：可选，访客所属公司
   - 访客人数（VisitingNum）：可选，访客人数（1-1000）
   - 车牌号码（platenumber）：可选，访客的车牌号码
5. 被访人选择：
   - 从被访人列表中选择接待人员
   - 显示被访人的姓名、部门、联系方式等信息
   - 被访人信息用于后续的通知和确认
6. 访问时间设置：
   - 来访日期（viscomeDate）：必填，访客计划来访的日期
   - 离开日期（vislevelDate）：必填，访客计划离开的日期
   - 验证时间的合理性和逻辑性
7. 访问事由填写：
   - 从来访事由列表中选择访问原因
   - 填写详细的访问目的和内容
   - 访问事由用于访客分类和统计
8. 访客照片上传：
   - 支持上传访客的身份证照片
   - 照片用于身份验证和安全识别
   - 照片格式和大小限制验证
9. 预约数据验证：
   - 验证必填字段是否完整
   - 检查身份证号格式的正确性
   - 验证手机号码格式的有效性
   - 确保访客人数在合理范围内（1-1000）
   - 检查访问事由和车牌号不包含特殊符号
10. 黑名单检查：
    - 检查访客身份证号是否在黑名单中
    - 通过Blacklist表验证访客的安全状态
    - 黑名单中的访客无法进行预约
11. 预约记录保存：
    - 将预约信息保存到reservation表中
    - 设置预约状态为待审核（audit=0）
    - 设置预约类型（recType）和相关标识
    - 记录预约时间和申请人信息
12. 访客卡号生成：
    - 根据系统配置自动生成访客卡号
    - 支持自动递增和自定义卡号模式
    - 卡号用于访客的身份识别和门禁控制
13. 审核通知发送：
    - 向审核人员发送预约审核通知
    - 通知被访人有新的访客预约
    - 通过系统消息、邮件或微信等方式通知
14. 操作日志记录：记录访客预约操作的详细日志。
15. 流程完成：返回预约成功信息，完成访客预约流程。

#### 新增预约功能点与其他模块及子模块业务关联说明
1. **与被访人管理模块的关联**：
   - 预约需要选择具体的被访人
   - 被访人信息用于预约的接待安排
   - 被访人的权限配置影响访客的门禁权限

2. **与访客黑名单模块的关联**：
   - 预约时需要检查访客是否在黑名单中
   - 黑名单中的访客无法进行预约
   - 黑名单验证在预约保存时执行

3. **与来访事由模块的关联**：
   - 预约时需要选择来访事由
   - 事由信息用于预约的分类和统计
   - 事由配置影响预约选项的可用性

4. **与审批流程模块的关联**：
   - 预约提交后需要进入审批流程
   - 审批结果决定预约的最终状态
   - 支持多级审批和流程自定义

5. **与设备管理模块的关联**：
   - 预约审核通过后会同步到访客设备
   - 设备用于访客的身份验证和签到
   - 设备状态影响预约信息的同步

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-访客预约功能点删除预约
#### 访客模块访客预约功能点RAG检索锚点
<!-- RAG检索锚点: 删除访客预约、取消预约、预约删除、撤销预约、预约管理删除 -->

#### 删除预约功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的预约]
    D --> E[检查预约状态]
    E --> F{预约状态检查}
    F -->|已审核通过| G[提示无法删除已审核预约]
    F -->|待审核或已拒绝| H[确认删除操作]
    H --> I{用户确认}
    I -->|取消删除| J[返回预约列表]
    I -->|确认删除| K[执行软删除]
    K --> L[从设备清除预约信息]
    L --> M[发送取消通知]
    M --> N[记录操作日志]
    N --> O[返回成功信息]
    C --> P[结束]
    G --> P
    J --> P
    O --> P
```
#### 删除预约功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除访客预约业务流程。
2. 权限验证：验证当前用户是否具有预约删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 只有预约申请人或管理员可以删除预约
3. 预约选择：
   - 从访客预约列表中选择要删除的预约
   - 显示预约的基本信息供确认
   - 支持单个预约记录的删除操作
4. 预约状态检查：
   - 检查预约的当前审核状态
   - 验证预约是否可以被删除
   - 已审核通过的预约通常不允许删除
5. 状态验证：
   - 待审核状态（audit=0）：允许删除
   - 已审核通过状态（audit=1）：不允许删除
   - 已拒绝状态（audit=2）：允许删除
6. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 要求用户明确确认删除意图
7. 软删除执行：
   - 设置DelTag字段为1，标记预约为已删除
   - 保留预约数据用于历史记录
   - 避免物理删除造成的数据丢失
8. 设备信息清除：
   - 从访客设备中清除预约信息
   - 调用zk_delete_vistor_data函数删除设备数据
   - 确保设备不再显示已删除的预约信息
9. 取消通知发送：
   - 通知被访人预约已取消
   - 通知审核人员预约已撤销
   - 更新相关人员的待办事项
10. 操作日志记录：记录预约删除操作的详细日志。
11. 流程完成：返回删除成功信息，完成预约删除流程。

#### 删除预约功能点与其他模块及子模块业务关联说明
1. **与审批流程模块的关联**：
   - 删除预约需要清理审批流程中的记录
   - 通知审批人员预约已取消
   - 更新审批待办事项列表

2. **与被访人管理模块的关联**：
   - 预约删除需要通知相关被访人
   - 被访人的日程安排需要相应调整
   - 取消预约影响被访人的接待计划

3. **与设备管理模块的关联**：
   - 删除预约需要从设备中清除相关信息
   - 设备不再显示已删除的预约信息
   - 设备状态影响信息清除的执行

4. **与通知系统的关联**：
   - 预约删除需要通知相关人员
   - 取消之前发送的预约通知
   - 清理系统待办事项和提醒

5. **与系统日志模块的关联**：
   - 所有删除操作都需要记录详细日志
   - 日志用于审计和问题排查
   - 支持数据恢复和回滚操作

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
