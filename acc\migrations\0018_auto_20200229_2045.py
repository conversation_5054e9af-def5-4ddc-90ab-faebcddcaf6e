# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2020-02-29 20:45
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('acc', '0017_auto_20190829_1000'),
    ]

    operations = [
        migrations.AddField(
            model_name='records',
            name='mask_flag',
            field=models.IntegerField(blank=True, choices=[(0, 'No'), (1, 'Yes')], null=True, verbose_name='Mask Flag'),
        ),
        migrations.AddField(
            model_name='records',
            name='temperature',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Temperature'),
        ),
        migrations.AlterField(
            model_name='accdoor',
            name='opendoor_type',
            field=models.IntegerField(choices=[(0, 'automatic matching'), (1, 'fingerprint only'), (2, 'Work only number'), (3, 'password only'), (4, 'only card'), (5, 'fingerprint or password'), (6, 'card or fingerprint'), (7, 'card or password'), (8, 'Work number plus fingerprint'), (9, 'Fingerprint plus password'), (10, 'Caga fingerprint'), (11, 'Caga Password'), (12, 'Fingerprint plus password plus card'), (13, 'Work number plus fingerprint plus password'), (14, 'Work number plus fingerprint or card plus fingerprint'), (15, 'human face'), (16, 'Face plus fingerprint'), (17, 'Face plus password'), (18, 'Face plus card'), (19, 'Face plus fingerprint plus card'), (20, 'Face plus fingerprint plus password'), (21, 'Finger vein'), (22, 'Finger vein plus password'), (23, 'Finger vein plus card'), (24, 'Finger vein plus card plus password'), (25, 'Palm'), (200, 'other')], default=6, null=True, verbose_name='Ways of identifying'),
        ),
        migrations.AlterField(
            model_name='records',
            name='verify',
            field=models.IntegerField(choices=[(0, 'automatic matching'), (1, 'fingerprint only'), (2, 'Work only number'), (3, 'password only'), (4, 'only card'), (5, 'fingerprint or password'), (6, 'card or fingerprint'), (7, 'card or password'), (8, 'Work number plus fingerprint'), (9, 'Fingerprint plus password'), (10, 'Caga fingerprint'), (11, 'Caga Password'), (12, 'Fingerprint plus password plus card'), (13, 'Work number plus fingerprint plus password'), (14, 'Work number plus fingerprint or card plus fingerprint'), (15, 'human face'), (16, 'Face plus fingerprint'), (17, 'Face plus password'), (18, 'Face plus card'), (19, 'Face plus fingerprint plus card'), (20, 'Face plus fingerprint plus password'), (21, 'Finger vein'), (22, 'Finger vein plus password'), (23, 'Finger vein plus card'), (24, 'Finger vein plus card plus password'), (25, 'Palm'), (200, 'other')], default=200, null=True, verbose_name='verification'),
        ),
    ]
