#!/usr/bin/python
# -*- coding: utf-8 -*-
import datetime
import os
import subprocess
import string
from django.contrib import auth
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.template import loader, Context, RequestContext
from django.conf import settings
from django.contrib.auth.decorators import permission_required, login_required
from mysite.iclock.models import *
from mysite.acc.models import *
#from mysite.iclock.datasproc import *
from django.core.paginator import Paginator
#from mysite.iclock.datas import *
from mysite.core.menu import *
from mysite.core.zktools import *
from mysite.iclock.models import *
from mysite.utils import *

import ctypes
import six
import sys
from mysite.core.zkcmdproc import *

from mysite.visitors.models import reservation, visitionlogs
from mysite.visitors.views import sendReservationToAcc, sendVisitorsToAcc

@login_required
def index(request, ModelName):
    if request.method=='GET' and ModelName in ['acc','accessRules']:
        tmpFile='acc/'+'acc_sys.html'
        tmpFile=request.GET.get('t', tmpFile)
        sub_menu='"%s"'%createmenu(request,ModelName)
        cc={}

        cc['sub_menu']=sub_menu
        #print sub_menu
        return render(request,tmpFile,cc)#render(tmpFile,cc,RequestContext(request, {}))







            #if ModelName=='inbio' or ModelName=='iaccess':
            #    tmpFile='acc/'+ModelName+'_sys.html'
            #    tmpFile=request.GET.get('t', tmpFile)
            #    sub_menu='"%s"'%createmenu(request,ModelName)
            #    cc={}
            #    
            #    cc['sub_menu']=sub_menu
            #    
            #    #print sub_menu
            #    return render(tmpFile,cc,RequestContext(request, {}))
            #else:
            #    tmpFile='acc/'+ModelName+'.html'
            #    tmpFile=request.GET.get('t', tmpFile)
            #    cc={}
            #    return render(tmpFile, cc,RequestContext(request, {}))
 
 
 
 
 
@login_required	
def SaveLevelEmp(request):
    """
    门禁权限组添加人员
    :param request:
    :return:
    """
    levelID=request.GET.get('levelid','')
    deptIDs=request.POST.get('deptIDs',"")
    userIDs=request.POST.get('UserIDs',"")
    isContainedChild=request.POST.get('isContainChild',"")
    if levelID=='':
        return getJSResponse({"ret":1,"message":u"%s"%_('Save failed')})
    levelID=levelID.split(',')   
    if userIDs == "":
        isAllDept=False
        deptidlist=deptIDs.split(',')
        deptids=deptidlist
        if isContainedChild=="1":
            deptids, isAllDept = getContainedDept(request, deptidlist)
        if isAllDept:
            emps=employee.objects.all().exclude(OffDuty=1).exclude(DelTag=1)
        else:
            emps=employee.objects.filter(DeptID__in=deptids).exclude(OffDuty=1).exclude(DelTag=1)
                
    else:
        userids=userIDs.split(',')
        emps=employee.objects.filter(id__in=userids)
    for emp in emps:
        for t in levelID:
            sql,params=getSQL_insert_new(level_emp._meta.db_table,level_id=t,UserID_id=emp.id)
            try:
                customSqlEx(sql,params)
            except Exception as e:
                    #print (e)
                    pass

    doors=level_door.objects.filter(level__in=levelID).values('door')
    devs=AccDoor.objects.filter(id__in=doors).distinct().values_list('device',flat=True)
    if devs:
        zk_send_acc_data(emps,devs)
    for sn in devs:
        dev=getDevice(sn)
        appendDevCmd(dev, 'INFO')


    #sendLevelToAccEx(emps,levelID)
    levels=level.objects.filter(id__in=levelID)
    s=u','.join(t.name for t in levels)

    adminLog(time=datetime.datetime.now(),User_id=request.user.id,object=s[:80],count=len(emps),
             model=str(_('Add people by permission group')) + ":" + str(_('Add')),
             action=_(u"Add emps")).save(force_insert=True)
    return getJSResponse({"ret":0,"message":u"%s"%_('Operation Success')})

#按人员设置
def SaveLevel_Emp(request):
    """
    门禁权限按人员设置 添加权限组
    :param request:
    :return:
    """
    userid=request.GET.get('id','')
    levelids=request.POST.get('K',"")
    if levelids=='':
        return getJSResponse({"ret":1,"message":u"%s"%_('Save failed')})
    levelID=levelids.split(',')
    for t in levelID:
        sql,params=getSQL_insert_new(level_emp._meta.db_table,level_id=t,UserID_id=userid)
        customSql(sql,params)
#	emp=employee.objByID(userid)
    emps=employee.objects.filter(id=userid)

    doors=level_door.objects.filter(level__in=levelID).values('door')
    devs=AccDoor.objects.filter(id__in=doors).distinct().values_list('device',flat=True)
    if devs:
        zk_send_acc_data(emps,devs)

    for sn in devs:
        dev=getDevice(sn)
        appendDevCmd(dev, 'INFO')

    #sendLevelToAccEx(emps,levelID)

    adminLog(time=datetime.datetime.now(),User_id=request.user.id,object=emps[0], model=str(_('Add permission groups by person')) + ":" + str(_('Add')), action=_(u"Modify")).save(force_insert=True)#
    return getJSResponse({"ret":0,"message":u"%s"%_('Operation Success')})


@login_required	
def SaveFirstOpen_Emp(request):
    FirstID=request.GET.get('door','')
    deptIDs=request.POST.get('deptIDs',"")
    userIDs=request.POST.get('UserIDs',"")
    isContainedChild=request.POST.get('isContainChild',"")
    if FirstID=='':
        return getJSResponse({"ret":1,"message":u"%s"%_('Save failed')})
    FirstIDs=FirstID.split(',')   
    if userIDs == "":
        isAllDept=False
        deptidlist=deptIDs.split(',')
        deptids=deptidlist
        if isContainedChild=="1":
            deptids,isAllDept=getContainedDept(request, deptidlist)
            
        if isAllDept:
            userids=list(employee.objects.all().exclude(OffDuty=1).exclude(DelTag=1).values_list('id',flat=True))
        else:
            userids=list(employee.objects.filter(DeptID__in=deptids).exclude(OffDuty=1).exclude(DelTag=1).values_list('id',flat=True))
    else:
        userids=userIDs.split(',')
    for uid in userids:
        for t in FirstIDs:
            sql,params=getSQL_insert_new(FirstOpen_emp._meta.db_table,firstopen_id=t,UserID_id=uid)
 #           try:
            customSql(sql,params)
            # except Exception as e:
            #         #print (e)
            #         pass
    sendFirstCardToAcc(FirstIDs)
    adminLog(time=datetime.datetime.now(),User_id=request.user.id,object="", model=FirstOpen_emp._meta.verbose_name, action=_(u"Modify")).save(force_insert=True)#
    return getJSResponse({"ret":0,"message":u"%s"%_('Operation Success')})
  
  
@login_required
def save_linkage(request,key):
    """input_type:0-any  1-AccDoor  2-Reader  3-AuxIn     output_type:0-AccDoor 1-AuxOut"""
    #   print request.POST
    SN=request.POST.get('device','')
    name=request.POST.get('name')
    remark=request.POST.get('remark')
    action_type=int(request.POST.get('action_type','-1'))
    events=request.POST.get('events_h')
    inputs=request.POST.get('inputs_h')
    outputs=request.POST.get('outputs_h')
    action_time=int(request.POST.get('action_time','0'))
    tab_index=int(request.POST.get('tab_index','0'))
    if events:
        events=events.split(',')
    else:
        events=[]
        outputs=[]
        inputs=[]

    if inputs:
        inputs=inputs.split(',')
    else:
        events=[]
        outputs=[]
        inputs=[]

    if outputs:
        outputs=outputs.split(',')
    else:
        events=[]
        outputs=[]
        inputs=[]


    try:
        events.remove(-10)
    except:
        pass

    if key=='_new_':

        #SN+输入类型+输入点+触发条件，判断联动唯一，不可重复
        new_list = []
        for i in inputs:
            its = int(i)
            if its == 0:
                inputtype = 0
                if tab_index == 1:
                    inputtype = 100   #读头为任意时
                if tab_index == 2: #辅助输入为任意时
                    inputtype = 1000
            elif tab_index == 2:#('220' in events) or ('221' in events):
                inputtype = 3
            elif tab_index == 0:
                inputtype = 1#表示door
            elif tab_index == 1:
                inputtype = 2  #表示Reader
            for e in events:
                new_tmp = "{}_{}_{}_{}".format(SN, inputtype, i, e)
                new_list.append(new_tmp)
        exist_list = [] 
        linkage_trigger_objs = linkage_trigger.objects.all()
        for lto in linkage_trigger_objs:
            linkage_obj = lto.linkage
            linkage_inout_obj = lto.linkage_inout
            _tmp = "{}_{}_{}_{}".format(linkage_obj.device.SN, linkage_inout_obj.input_type, linkage_inout_obj.input_id, lto.trigger_cond)
            exist_list.append(_tmp)
        if len(set(new_list) & set(exist_list)) > 0:
            return getJSResponse({"ret": 1, "message": u"%s" % _(u'The device has been set up linkage, save failed')})

        link_obj=linkage(device_id=SN,name=name,remark=remark)
        try:
            link_obj.save(force_insert=True)
        except Exception as e:
            print ("save_linkage",e)
            return getJSResponse({"ret":1,"message": u"%s"%_(u'Save failed')})
        link_id=link_obj.id
    else:
        link_id=key
        link_obj=linkage(id=link_id,device_id=SN,name=name,remark=remark)
        link_obj.save(force_update=True)
    objs=linkage_trigger.objects.filter(linkage=link_id)
    if tab_index==0:
        objs=objs.filter(trigger_cond__in=[5,7,8,9,24,25,28,36,37,38,100,102,200,201,202,204,205,209])

    if tab_index==1:
        objs=objs.filter(trigger_cond__in=[0,2,3,4,20,21,22,23,27,29,41,42,101])


    if tab_index==2:
        objs=objs.filter(trigger_cond__in=[220,221])
    del_linkage_trig(SN,objs)
    objs.delete()
    if tab_index==0:
        linkage_inout.objects.filter(linkage=link_id,input_type__in=[0,1]).delete()
    if tab_index==1:
        linkage_inout.objects.filter(linkage=link_id,input_type__in=[2,100]).delete()
    if tab_index==2:
        linkage_inout.objects.filter(linkage=link_id,input_type__in=[3,1000]).delete()
    for its in inputs:
        its=int(its)
        if its==0:
            inputtype=0
            if tab_index==1:
                inputtype=100   #读头为任意时
            if tab_index==2: #辅助输入为任意时
                inputtype=1000
        elif tab_index==2:#('220' in events) or ('221' in events):
            inputtype=3
        elif tab_index==0:
            inputtype=1#表示door
        elif tab_index==1:
            inputtype=2  #表示Reader
        for ots in outputs:
            ots=int(ots)
            outtype=0
            if ots<0:
                outtype=1
                ots=ots*(-1)
            link_inout=linkage_inout(linkage_id=link_id,input_type=inputtype,input_id=its,output_type=outtype,output_id=ots,action_type=action_type,action_time=action_time)
            link_inout.save(force_insert=True)
            inout_id=link_inout.id
            for es in events:
                link_trig=linkage_trigger(trigger_cond=es,linkage_index=inout_id,linkage_id=link_id,linkage_inout_id=inout_id)
                link_trig.save(force_insert=True)
    try:
        sendLinkageToAcc(link_id)
    except Exception as e:
        print ("sendLinkageToAcc=======",e)
    adminLog(time=datetime.datetime.now(),User_id=request.user.id, model=u'%s'%_(u'linkage'), action=_(u'Create') if key=='_new_' else _(u'Modify'),
    object=name).save(force_insert=True)#
    return getJSResponse({"ret":0,"message": u"%s"%_('Save Success')})

@login_required
def save_combopen_comb(request,key):
    door=request.POST.get('door','')
    name=request.POST.get('name')
    combs=request.POST.get('combopen_door_data','[]')
    if key=='_new_':
        obj=combopen_door(name=name,door_id=int(door))
        try:
            obj.save()
        except Exception as e:
            print ("save_combopen_door",e)
            return getJSResponse({"ret":1,"message": u"%s"%_('Save failed')})
        combopen_door_id=obj.id
    else:
        combopen_door_id=key
        obj=combopen_door(id=key,name=name,door_id=int(door))
        obj.save()

    combopen_comb.objects.filter(combopen_door=combopen_door_id).delete()
    comb=loads(combs)
    j=0
    for t in comb:
        d={}
        d['combopen_door_id']=combopen_door_id
        d['combopen_id']=t['combid']
        d['opener_number']=t['empCount']
        d['sort']=j
        j=j+1
        sql,params=getSQL_insert_new(combopen_comb._meta.db_table,d)
        #try:
        customSql(sql,params)
        #except Exception as e:
            #print (e)
            #pass

    sendMulCardToAcc(combopen_door_id)#combopenid,snlist=[]
    return getJSResponse({"ret":0,"message": u"%s"%_('Save Success')})

@login_required
def save_interlock(request, key):
    SN = request.POST.get('device', '')
    interlock_rule = request.POST.get('interlock_rule', '')
    remark = request.POST.get('remark', '')

    device = getDevice(SN)
    # 1U项目, P3000系列控制器
    if device.Style in ['30']:
        initial_triggerlist = request.POST.get('initial_triggerlist', '')
        itrigger_items = [item for item in initial_triggerlist.split(',') if item.strip()]
        itrigger_set = set(itrigger_items)
        if interlock_rule == '12':
            # 组1与组2互锁
            target_triggerlist = request.POST.get('target_triggerlist', '')
            ttrigger_items = [item for item in target_triggerlist.split(',') if item.strip()]
            ttrigger_set = set(ttrigger_items)
            # 组1和组2，必须都有勾选项
            if not itrigger_set or not ttrigger_set:
                return getJSResponse({"ret":1,"message":u"%s"%_(u'Both Group 1 and Group 2 must not be empty.')})
        else:
            # 组内互锁
            ttrigger_set = set()
        #删除0, 这个表示任意点,不需要判断组1与组2是否重叠
        itrigger_set.discard('0')

        if itrigger_set & ttrigger_set:
            return getJSResponse({"ret":1,"message":u"%s"%_(u'The options for Group 1 and Group 2 are mutually exclusive. Please uncheck the selections in Group 1 before making a choice in Group 2.')})

        if key != '_new_':  # 编辑时先删除原数据
            obj = InterLock.objects.get(id=key)
            zk_delete_interlock(obj)
            # 先删旧的触发点，再下发新的，简化业务
            i_trigger = obj.initial_triggerlist
            i_trigger.delete()
            t_trigger = obj.target_triggerlist
            if t_trigger:
                t_trigger.delete()
            obj.delete()

        # 保存触发点集合
        tl_init_obj = TriggerList.objects.create(
            device=device,
            feature_type = 0,  # 门
            trigger = initial_triggerlist
        )
        if interlock_rule == '11':  # 组内互锁
            tl_target_obj = None
        else:
            tl_target_obj = TriggerList.objects.create(
                device=device,
                feature_type = 0,
                trigger = target_triggerlist
            )
        obj = InterLock.objects.create(
                device=device,
                interlock_rule=interlock_rule,
                initial_triggerlist=tl_init_obj,
                target_triggerlist=tl_target_obj,
                remark=remark
            )
    else:
        obj, created = InterLock.objects.update_or_create(
            device=device,
            defaults={
                'interlock_rule': interlock_rule,
                'remark': remark
            }
        )
    sendInterLockToAcc([],[obj.device_id])
    return getJSResponse({"ret":0,"message": u"%s"%_('Save Success')})

@login_required
def save_antipassback(request, key):
    SN = request.POST.get('device', '')
    apb_rule = request.POST.get('apb_rule', '')
    remark = request.POST.get('remark', '')

    device = getDevice(SN)
    # 1U项目, P3000系列控制器
    if device.Style in ['30']:
        initial_triggerlist = request.POST.get('initial_triggerlist', '')
        target_triggerlist = request.POST.get('target_triggerlist', '')

        itrigger_items = [item for item in initial_triggerlist.split(',') if item.strip()]
        ttrigger_items = [item for item in target_triggerlist.split(',') if item.strip()]

        itrigger_set = set(itrigger_items)
        ttrigger_set = set(ttrigger_items)

        # 组1和组2，必须都有勾选项
        if not itrigger_set or not ttrigger_set:
            return getJSResponse({"ret":1,"message":u"%s"%_(u'Both Group 1 and Group 2 must not be empty.')})

        # 删除0, 这个表示任意点,不需要判断组1与组2是否重叠
        itrigger_set.discard('0')
        if itrigger_set & ttrigger_set:
            return getJSResponse({"ret":1,"message":u"%s"%_(u'The options for Group 1 and Group 2 are mutually exclusive. Please uncheck the selections in Group 1 before making a choice in Group 2.')})

        if key != '_new_':  # 编辑逻辑为先删除再新增,简化业务
            obj = AntiPassBack.objects.get(id=key)
            obj.delete()  # 边边会同步执行触发点集合的删除

        tl_init_obj = TriggerList.objects.create(
            device=device,
            feature_type = 0 if apb_rule == '1001' else 4,  # 0门, 4读头
            trigger = initial_triggerlist
        )
        tl_target_obj = TriggerList.objects.create(
            device=device,
            feature_type = 0 if apb_rule == '1001' else 4,
            trigger = target_triggerlist
        )
        obj = AntiPassBack.objects.create(
                device=device,
                apb_rule=apb_rule,
                initial_triggerlist=tl_init_obj,
                target_triggerlist=tl_target_obj,
            )
    else:
        obj, created = AntiPassBack.objects.update_or_create(
            device=device,
            defaults={
                'apb_rule': int(apb_rule)
            }
        )
    set_antipassback(obj)
    return getJSResponse({"ret":0,"message": u"%s"%_('Save Success')})

#人员维护时保存的有关门禁信息
def saveToAccEmployee(request,emp,oldemp):
    t1=datetime.datetime.now()
    set_time=False
    st=None
    et=None
    blacklist=False
    morecard_group=request.POST.get('morecard_group')
    set_valid_time=request.POST.get('set_valid_time')
    super_auth=request.POST.get('acc_super_auth')
    isblacklist=request.POST.get('isblacklist')
    levels=request.POST.getlist('level')
    level_unchk=request.POST.get('level_unchk')  #人员编辑页，未勾选的权限组

    t1=datetime.datetime.now()
    if isblacklist=='on':
        blacklist=True
    if set_valid_time=='on':
        set_time=True
        startdate=request.POST.get('acc_startdate')[0:16]
        enddate=request.POST.get('acc_enddate')[0:16]

        st=datetime.datetime.strptime(startdate,'%Y-%m-%d %H:%M')
        et=datetime.datetime.strptime(enddate,'%Y-%m-%d %H:%M')

    tmp=acc_employee.objByUserID(emp.id)

    super_auth_change=0  # 默认没变更
    acc_employee_change=0  # 默认没变更
    update_relevant_devices=False
    is_send_morecard_group=True
    if tmp:
        # 超级用户权限变更的
        if (str(tmp.acc_super_auth) != str(super_auth)):
            if str(super_auth) == '15':
                #赋予超管权限，更新到所有相关设备
                super_auth_change = 1
                update_relevant_devices = True
            elif str(tmp.acc_super_auth) == '15':
                #取消超管权限
                super_auth_change = 2
                update_relevant_devices = True
        # 如果门禁权限变更，超级用户权限变更，门禁权限有效期变更，都触发重新下发
        if (tmp.acc_startdate != st or tmp.acc_enddate != et) or (tmp.isblacklist != blacklist):
            acc_employee_change = 1
            update_relevant_devices = True

        old_morecard_group=tmp.morecard_group_id or ''
        if str(old_morecard_group)==morecard_group:
            is_send_morecard_group=False
        accEmployee=tmp
        accEmployee.isblacklist=blacklist
        accEmployee.morecard_group_id=morecard_group
        accEmployee.set_valid_time=set_time
        accEmployee.acc_startdate=st
        accEmployee.acc_enddate=et
        accEmployee.acc_super_auth=super_auth
        accEmployee.save()
    else:
        if not morecard_group:
            is_send_morecard_group=False
        acc_employee(UserID=emp,morecard_group_id=morecard_group,set_valid_time=set_time,acc_startdate=st,acc_enddate=et,acc_super_auth=super_auth,isblacklist=blacklist).save()
    levellist = level_emp.objects.filter(UserID=emp).distinct().values_list('level', flat=True)
    new_levels=[]
    del_levels=[]
    change_levels=[]

    level_unchk = level_unchk.split(',')

    #未选中的权限与数据库已有权限的交集，即为此次新删除的权限
    del_levels = list(set([int(lvlu) for lvlu in level_unchk if lvlu]) & set(list(levellist)))
    #已选中的权限与数据库已有权限比较，不在数据库中的权限，即此次新增的权限
    new_levels = list(set([int(lvl) for lvl in levels if lvl]) - set(list(levellist)))

    #部门门禁权限判断
    if oldemp and emp.DeptID_id != oldemp.DeptID_id:
        #原部门的门禁权限组
        del_level_ids = level_dept.objects.filter(DeptID_id=oldemp.DeptID_id).values_list('level__id', flat=True)
        #如果手动编辑有新勾选权限组，且新勾选的权限组为要删除的部门权限组，则不再删除此权限组
        del_level_ids = list(set(del_level_ids) - set(new_levels))
        #新部门的门禁权限组
        new_level_ids = level_dept.objects.filter(DeptID_id=emp.DeptID_id).values_list('level__id', flat=True)
        #如果手动编辑有删除权限组，且删除的权限组为要添加的新部门权限组，则不再添加此权限组
        new_level_ids = list(set(new_level_ids) - set(del_levels))

        new_levels += new_level_ids
        del_levels += del_level_ids

    for t in new_levels:
        try:
            level_emp(UserID=emp,level_id=t).save()
        except:
            pass
    
    change_levels = new_levels + del_levels  #新增的权限及删除的权限

    doors=level_door.objects.filter(level__in=change_levels).values('door')  
    devs=AccDoor.objects.filter(id__in=doors).distinct().values_list('device',flat=True)

    doors=level_door.objects.filter(level__in=del_levels).values('door')
    del_devs=AccDoor.objects.filter(id__in=doors).distinct().values_list('device',flat=True)
    if del_levels:
        level_emp.objects.filter(level__in=del_levels,UserID_id=emp.id).delete()

    if update_relevant_devices:
        lv_s = level_emp.objects.filter(UserID=emp).values('level')
        doors = level_door.objects.filter(level__in=lv_s).values('door')
        snlists = AccDoor.objects.filter(id__in=doors).distinct().values_list('device', flat=True)
        
        for sn in list(snlists):
            device = getDevice(sn)
            if getattr(device, 'SuperAuthorizeFunOn', 0):
                if super_auth_change in [1, 2]:
                    if super_auth_change == 2:
                        delete_data(sn, 'SuperAuthorize', 'PIN=%s'%emp.pin())
                    # 下发超管权限给相关设备(这边直接走下发权限的函数了)
                    # 取消超管权限后，同样要下发权限（要下发为普通的门禁权限）
                    zk_send_acc_data([emp], snlists)
                    acc_employee_change = 0  # zk_send_acc_data已经会下发人员信息了，不用再下发一次
            if acc_employee_change:
                # 更新相关设备人员信息（与定时任务一样，只更新人员信息，权限变更的，由change_levels处理逻辑）
                # 但此处只需更新人员基本信息
                zk_set_data(device,'userinfo',[emp],cmdTime=None,is_finger=0,is_face=0,is_pic=0,is_pv=0,is_fv=0)

    if change_levels:
        emps=employee.objects.filter(id=emp.id)
        zk_send_acc_data(emps,devs,del_devs)

    if is_send_morecard_group:
        combopen_emp.objects.filter(UserID=emp).delete()
        try:
            combopen_emp(UserID=emp,combopen_id=morecard_group).save()
            doors=combopen_comb.objects.filter(combopen=morecard_group).distinct().values_list('combopen_door__door',flat=True)
            snlist=list(AccDoor.objects.filter(id__in=doors).distinct().values_list('device_id',flat=True))
            sync_multiPerson(snlist)
        except:pass

#该方法主要用于远程开关门取消报警（包括开部分门)    
@login_required
def send_doors_data(request):
    funid = request.GET.get("func", "")
    itype = request.GET.get("type", "")
    door =  request.GET.get("data", "")
    devs=[]
    doors=[]
    door_id=""
    count = 0
    opName = ""
    if itype == 'part':
        doors_id = door.split(',')
        doors_count = len(doors_id)
        devs = AccDoor.objects.filter(id__in=doors_id).distinct().values('device')
        doors = AccDoor.objects.filter(id__in=doors_id).distinct()

    datas = []
    if funid in ["cancelalarm", "cancelall"]:
        for t in devs:
            sn=t['device']
            dev=getDevice(sn)
            if dev.ProductType not in [4]:
                if dev.Style in ['414', '30']:  # 应该还有其他类型是这逻辑，没测试
                    # 按门取消报警（部分设备只能按门来取消报警）
                    for door in doors:
                        door_no = door.door_no
                        cmdstr = "CONTROL DEVICE 02%02d0000" % door_no
                        saveCmd(door.device_id, cmdstr)
                        if not count:
                            door_id += u'%s' % door.device_id
                        count += 1
                else:
                    appendDevCmd(dev, 'AC_UNALARM')  # 0200
            if not count:
                door_id += u'%s'%sn
            count += 1
        
        if count > 1:   
            door_id += ",..."
                
        if funid == "cancelalarm" :
            opName = u'%s'%_(u'Cancel the alarm')
        else:    
            opName = u'%s'%_(u'Cancel all alarms')    
                
        adminLog(time=datetime.datetime.now(),User_id=request.user.id, model=u'%s'%_(u'equipment'), action=opName, count = count,
        object=door_id[:40]).save(force_insert=True)#     
            
        return getJSResponse({'ret': 0})
    elif funid in ["opendoor", "openpart"]:
        data = loads(GetParamValue('acc_param','{}','acc'))

        if data and data['is_']==1:
            from django.contrib.auth import authenticate
            pwd=request.GET.get('pwd')
            username=request.user.username
            user=authenticate(username=username, password=pwd, UserType='user')
            if not user:
                return getJSResponse({'ret': 1,'message':u'%s'%_(u'No permission')})
        interval = request.GET.get("open_interval", 0)#1-254
        enable = request.GET.get("enable_no_tzs", False)
        if enable == "true":#仅启用常开时间段，不开门
            #for door_obj in doors:
            #	if doors_count > 1:
            #		if not door_obj.device.show_status():
            #			continue

            for door in doors:
                door_no=door.door_no
                cmdstr="CONTROL DEVICE 04%02d0100"%(door_no)
                if door.device.ProductType not in [4]:
                    saveCmd(door.device_id,cmdstr)
                
                if not count:
                    door_id += u'%s'%door.device_id
                count +=1

            #ret = sync_control_no(door_obj, 1)#重新启用常开时间段
            #ret = sync_set_output(door_obj, 1, int(interval))#ret为None代表固件没有返回值，成功还是失败？  door_obj.lock_delay
            #datas.append({ "door_name": door_obj.door_name, "ret": ret == None and -1 or ret })
        else:
            #print '----remote 005', doors.count()
            #for dev in devs:
            for door in doors:
                door_no=door.door_no
                cmdstr="CONTROL DEVICE 01%02d01%02x"%(door_no,int(interval))
                #print cmdstr
                if door.device.ProductType in [4]:
                    saveCmd(door.device_id, "AC_UNLOCK")
                else:
                    saveCmd(door.device_id,cmdstr)
                if not count:
                    door_id += u'%s'%door.device_id
                count +=1    

                #datas.append({ "door_name": door_obj.door_name, "ret": ret == None and -1 or ret })
        if count > 1:   
            door_id += ",..."
                
        if funid == "opendoor" :
            opName = u'%s'%_(u'Open the door')
        elif enable == "true":
            opName = u'%s'%_(u'Enable normally open time period') 
        else:    
            opName = u'%s'%_(u'Open all the doors')    
                
        adminLog(time=datetime.datetime.now(),User_id=request.user.id, model=u'%s'%_(u'equipment'), action=opName, count = count,
        object=door_id[:40]).save(force_insert=True)#       
         
        return getJSResponse({'ret':0})
    elif funid =='closeoutput':
        disable = request.GET.get("disable_no_tzs", False)
        if disable == "true":
            doors=AccDoor.objects.filter(device__in=devs)
        for door in doors:
            cmdstr = "CONTROL DEVICE 01%02d0200" %(door.door_no)
            dev_obj=AccDoor.objects.filter(id=door.pk).distinct().values('device')
            saveCmd(dev_obj[0]['device'], cmdstr)
        return getJSResponse({'ret': 0})
    elif funid in ["closedoor", "closepart"]:# "closeall",:
        disable = request.GET.get("disable_no_tzs", False)
        #print '----disable=',disable
        if disable == "true":
            #for dev in devs:
            for door in doors:
                door_no=door.door_no
                cmdstr="CONTROL DEVICE 04%02d0000"%(door_no)
                if door.device.ProductType not in [4]:
                    saveCmd(door.device_id,cmdstr)
                if not count:
                    door_id += u'%s'%door.device_id
                count +=1  

            for t in devs:
                sn=t['device']
                dev=getDevice(sn)
                if dev.ProductType in [4]:
                    continue
                # auxs=AuxOut.objects.filter(device=sn)
                # for tt in auxs:
                #     aux_no=tt.aux_no
                #     cmdstr="CONTROL DEVICE 01%02d0200"%aux_no
                #     saveCmd(sn,cmdstr)




        else:
            #for dev in devs:
            for door in doors:
                door_no=door.door_no
                cmdstr="CONTROL DEVICE 01%02d0100"%(door_no)
                if door.device.ProductType not in [4]:
                    saveCmd(door.device_id,cmdstr)
                if not count:
                    door_id += u'%s'%door.device_id
                count +=1  
                
            for t in devs:
                sn=t['device']
                dev=getDevice(sn)
                if dev.ProductType in [4]:
                    continue
                
                # auxs=AuxOut.objects.filter(device=sn)
                # for tt in auxs:
                #     aux_no=tt.aux_no
                #     cmdstr="CONTROL DEVICE 01%02d0200"%aux_no
                #     saveCmd(sn,cmdstr)

        if count > 1:   
            door_id += ",..."
                
        if funid == "closedoor" :
            opName = u'%s'%_(u'close the door')
        elif disable == "true":
            opName = u'%s'%_(u'Prohibit the normally open time period') 
        else:    
            opName = u'%s'%_(u'Close all doors')    
                
        adminLog(time=datetime.datetime.now(),User_id=request.user.id, model=u'%s'%_(u'equipment'), action=opName, count = count,
        object=door_id[:40]).save(force_insert=True)#      


                #datas.append({ "door_name": door_obj.door_name, "ret": ret == None and -1 or ret })
        return getJSResponse({'ret':0})

def sync_multiPerson(snlist):#同步多人开门组中人员 
    for SN in snlist:
        delete_data(SN,'multimcard')
    sendMulCardToAcc([],snlist)

@login_required
def sync_doors_data(request):
    """
    同步数据至设备
    :param request:
    :return:
    """
    keys=request.POST.getlist('K')
    opts=request.POST.getlist('optBox')
    action=request.POST.get('action','')
    opName=request.POST.get('opName','')
    count=len(keys)
    dev_str = ""
    if count>1:
        dev_str=keys[0]+'...'
    else:
        dev_str=keys[0]    
    cache.set('_sync_doors_data_',1)
    iclocks = iclock.objects.filter(SN__in=keys)  # .exclude(DelTag=1).exclude(State=0)
    try:

        adminLog(time=datetime.datetime.now(),User_id=request.user.id, model=u'%s'%_(u'equipment'), action=unquote(opName)[:40], count = count,
            object=dev_str).save(force_insert=True)#
        #for dev in iclocks:
        #	delete_acc_all_data(dev)
        if 'timeZoneAndHoliday' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    if dev.Style not in ['101']:
                        # delete_data(dev.SN,'holiday')
                        delete_data(dev.SN,'timezone')
            sendTimeZonesToAcc([],keys)
            sendHolidayToAcc([],keys)
        if 'doorOpt' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    # try:
                    #     from mysite.core.zkcmdproc import zk_delete_all_params
                    #     zk_delete_all_params(dev)
                    #
                    # except:
                    #     if dev.Style in ['23', '24', '25','26', '27', '28', '260']:
                    #             delete_data(dev.SN, 'ReaderWGFormat')
                    #             delete_data(dev.SN, 'WGFormat')
                    #             delete_data(dev.SN, 'DoorVSTimezone')
                    #             delete_data(dev.SN, 'DoorParameters')
                    #             if dev.Style != '260':
                    #                 delete_data(dev.SN, 'DevProperty')
                    #             delete_data(dev.SN, 'DevParameters')
                    #             delete_data(dev.SN, 'DoorProperty')
                    #             delete_data(dev.SN, 'ReaderProperty')

                    set_dooroptions([], [dev])

                    # delete_data(dev.SN, 'AuxIn')
                    # j = 0
                    # for t in AuxIn.objects.all():
                    #     j = j + 1
                    #     if dev.Style == '260':
                    #         cmdStr = "DATA UPDATE AuxIn DevID=1\tAddress=%s\tDisable=0" % (j)
                    #     else:
                    #         cmdStr = "DATA UPDATE AuxIn ID=%s\tDevID=%s\tAddress=%s" % (t.id, dev.id or 1, j)
                    #     saveCmd(dev.SN, cmdStr)
                    # delete_data(dev.SN, 'AuxOut')

        if 'accLevel' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                delEmpInDevice(-1,dev.SN)#先删除数据库记录的该设备的人员表
                if dev.ProductType in [5,15,25]:
                    if hasattr(dev,'MulCardUser') and dev.MulCardUser==1:
                        delete_data(dev.SN, 'mulcarduser')
                        delete_data(dev.SN, 'extuser')
                    # 混合协议
                    if getattr(dev, 'MultiBioDataSupport', ''):
                        delete_data(dev.SN, 'biodata', filters='Type=1')
                    else:
                        if dev.isFptemp:
                            delete_data(dev.SN, 'templatev10')
                        if hasattr(dev, 'FvFunOn') and dev.FvFunOn == 1:
                           delete_data(dev.SN, 'fvtemplate')

                    if dev.ProductType == 25:
                        # if hasattr(dev, 'BioPhotoFun') and int(dev.BioPhotoFun) == 1:
                        #     delete_data(dev.SN,'biophoto')
                        if hasattr(dev, 'BioDataFun') and int(dev.BioDataFun) == 1:
                            delete_data(dev.SN, 'biodata',filters='Type=9')
                        elif dev.FaceAlgVer in ['9']:
                            delete_data(dev.SN, 'biodata',filters='Type=2')
                        else:
                            delete_data(dev.SN, 'facev7')

                    delete_data(dev.SN,'userauthorize')
                    if getattr(dev, 'SuperAuthorizeFunOn', 0):
                        delete_data(dev.SN, 'SuperAuthorize')
                    delete_data(dev.SN,'user')

            zk_send_acc_data([],keys)

            # 重新下发有效的访客门禁权限
            zero_of_today = datetime.datetime.combine(datetime.date.today(), datetime.datetime.min.time())
            vis_param = loads(GetParamValue('visitors_param', '{}', 'visitors'))
            if int(vis_param.get('is_res_vis', 2)) == 1:
                res_list = reservation.objects.filter(viscomeDate__gte=zero_of_today, DelTag=0, recType__in=(0, 1, 4))
                for res in res_list:
                    sendReservationToAcc(res)
            vis_list = visitionlogs.objects.filter(VisState=0, DelTag=0)
            for vis in vis_list:
                sendVisitorsToAcc(vis)
        # 下发触发点集合, p3000系列触发
        if (('interlock' in opts) or ('antiPassBack' in opts)):
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.Style == '30':
                    clear_triggerlist(dev.SN)
                    zk_send_triggerlist(sn=dev.SN)
        if 'interlock' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    sendInterLockToAcc([],[dev.SN])
        if 'linkage' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    delete_data(dev.SN,'inoutfun')
                    sendLinkageToAcc([],dev.SN)
        if 'antiPassBack' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    clear_antipassback(dev)
                    objs=AntiPassBack.objects.filter(device__in=[dev.SN])
                    for obj in objs:
                        set_antipassback(obj)
        if 'firstPerson' in opts:
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    delete_data(dev.SN,'firstcard')

                    doors=AccDoor.objects.filter(device__in=[dev.SN])
                    objs=FirstOpen.objects.filter(door__in=doors).values_list('id',flat=True)
                    sendFirstCardToAcc(objs)
        if 'multiPerson' in opts:
            sns=[]
            for obj in iclocks:
                dev=getDevice(obj.SN)
                if dev.ProductType in [5,15,25]:
                    #if hasattr(dev,'MulCardUser') and dev.MulCardUser==1:
                     #   delete_data(dev.SN, 'mulcarduser')
                    sns.append(dev.SN)
                    #delete_data(dev.SN,'multimcard')
                    #sendMulCardToAcc([],[dev.SN])
            if sns:
                sync_multiPerson(sns)
    except Exception as e:
        import traceback
        traceback.print_exc()
        print("sync_doors_data=",e)
        cache.delete('_sync_doors_data_')
        return getJSResponse({'ret': 1,'msg':u'%s'%e})
    if opts:
        for dev in iclocks:
            appendDevCmd(dev,'INFO')
    cache.delete('_sync_doors_data_')
    return getJSResponse({'ret': 0})

@login_required
def restartsvr2(request):
    try:
        subprocess.call("cmd /C %s/%s"%(settings.FILEPATH,'iclockservice.exe -b 2'))
    except:
        pass

    return getJSResponse({'ret': 0,'message':u'%s'%_(u'Startup success!')})


def saveParamsToOtherDoor(request,newobj):
    opts=request.POST.getlist('optBox')
    objs=AccDoor.objects.exclude(device__DelTag=1)
    
    doorset=[]
    for obj in objs:
        for k in opts:
            v=newobj.__getattribute__(k)
            obj.__setattr__(k,v)
            obj.save()
        doorset.append(obj)
    if doorset:
        set_dooroptions(doorset)


@login_required
def search_device(request):
    Host=request.META["HTTP_HOST"]
    #print Host

    auto_reg=GetParamValue('opt_basic_dev_auto','1')
    #if auto_reg=='0':
        #return getJSResponse({'ret': 0,'message':u'%s'%_(u'系统设置不允许自动添加新设备!')})


    commpro = ctypes.windll.LoadLibrary(settings.FILEPATH+"\zkeco_dlls\plcommpro.dll")
    str_buf_len = 64*1024*2 #64K*2 200台设备
    str_buf = ctypes.create_string_buffer(str_buf_len)
    sCommType=b'UDP'
    sAddr=b'***************'
    pCommType = ctypes.create_string_buffer(sCommType)
    pAddr = ctypes.create_string_buffer(sAddr)
    ret=commpro.SearchDevice(b"UDP",b"***************",str_buf)
    sns=[]

#    searchs.objects.update(State=3)
    searchs.objects.all().delete()
    if ret>0:
        result=str_buf.raw
        index=result.find(b'\0')
        result = result[0:index-2].split(b'\r\n')
        for t in result:
            if six.PY3:
                t=t.decode('gb18030')
            devDict=lineToDict(t,',')
            if 'SN' in devDict.keys():
                sn=devDict['SN']
                # 设备序列号位数大于20位的不处理
                if len(sn) > 20:
                    continue
            else:
                continue

            #if 'WebServerIP' in devDict.keys() and devDict['WebServerIP']!='' and devDict['WebServerIP']!='0.0.0.0':continue
            Protype=''
            if "Protype" in devDict.keys():
                Protype=devDict["Protype"]
            devName=devDict['SN']
            if 'Device' in devDict.keys() and devDict['Device']!='':
                devName=devDict['Device']
            #	if 'C3' in devName:
            #		ProductType=15

            objs=searchs.objects.filter(SN=sn)
            if not objs:
                obj=searchs()
            else:
                obj=objs[0]
            obj.SN=sn
            obj.isAdd=0
            obj.State=1
            # try:
            # 	print "1111111111",sn
            # 	obj1=iclock.objects.get(SN=sn)#filter(SN=sn).filter(DelTag=1):
            # 	if
            # 			obj.isAdd=0
            # 	elif iclock.objects.filter(SN=sn).filter(DelTag=0):
            # 		print "------",sn
            # 		obj.isAdd=1
            # except:
            # 	obj.isAdd=0
            #if not obj.Alias:
            #    obj.Alias=devName
            obj.Protype=Protype
            obj.MAC=devDict['MAC']
            obj.Reserved=''

            # 产品安全整改，新旧设备通过广播搜索到设备有没有"signature="可以区分是否允许空密码
            if 'signature' in devDict.keys():
                obj.signature=devDict['signature'] or 'signature'

            if not obj.IPAddress:
                obj.IPAddress=devDict['IP']
            else:
                if obj.IPAddress!=devDict['IP']:
                    obj.Reserved=u"%s"%(_(u'Device IP: %s'))%(devDict['IP'])
            if 'NetMask' in devDict.keys():
                if not obj.NetMask:
                        obj.NetMask=devDict['NetMask']
                else:
                    if obj.NetMask!=devDict['NetMask']:
                        obj.Reserved=obj.Reserved+' '+u"%s"%(_(u'Device NetMask: %s'))%(devDict['NetMask'])
            if 'GATEIPAddress' in devDict.keys():
                if not obj.GATEIPAddress:
                    obj.GATEIPAddress=devDict['GATEIPAddress'][:20]
                else:
                    if obj.GATEIPAddress!=devDict['GATEIPAddress'][:20]:
                        obj.Reserved=obj.Reserved+' '+u"%s"%(_(u'Device GATEIP: %s'))%(devDict['GATEIPAddress'])
            obj.DeviceName=devName
            if 'Ver' in devDict.keys():
                obj.FWVersion=devDict['Ver']
            if Protype=='push' and 'WebServerIP' in devDict.keys():
                if not obj.WebServerIP:
                    obj.WebServerIP=devDict['WebServerIP']
                else:
                    if obj.WebServerIP!=devDict['WebServerIP']:
                        obj.Reserved=obj.Reserved+' '+u"%s"%(_(u'Device WEB: %s'))%(devDict['WebServerIP'])

                if not obj.WebServerPort:
                    obj.WebServerPort=devDict['WebServerPort']
                else:
                    if obj.WebServerPort!=devDict['WebServerPort']:
                        obj.Reserved=obj.Reserved+' '+u"%s"%(_(u'Device Port: %s'))%(devDict['WebServerPort'])

                obj.WebServerURL=devDict['WebServerURL']
            obj.IsSupportSSL=0
            if 'IsSupportSSL' in devDict.keys():
                obj.IsSupportSSL=devDict['IsSupportSSL']
            obj.DNSFunOn=0
            if 'DNSFunOn' in devDict.keys():
                obj.DNSFunOn=devDict['DNSFunOn']
            if 'DNS' in devDict.keys():
                obj.DNS=devDict['DNS']
            if 'MachineType' in  devDict.keys():
                obj.Style=devDict['MachineType']


            obj.OpStamp=datetime.datetime.now()
            obj.save()


        # 	sns.append(sn)
        # 	try:
        # 	    zon=zone.objects.all().order_by('id').exclude(DelTag=1)[0]
        # 	    IclockZone(SN=device,zone=zon).save()
        # 	except Exception as e:
        # 	    pass
        # try:
        # 	if sns:
        # 		sendTimeZonesToAcc(None,sns)
        # 		#restartSvr('AttServer')
        # except Exception as e:
        # 	print "222222222222",e




                #mac = s['MAC']		# 目标设备的MAC地址
                #new_ip = '************'		# 设备新的IP地址
                #comm_pwd = ''
                #str = "MAC=%s,IPAddress=%s,NetMask=%s " % (mac,new_ip,'***********')
                #p_buf = ctypes.create_string_buffer(str)
                #modify_ip = commpro.ModifyIPAddress("UDP", "***************", p_buf)
    else:
        return getJSResponse({'ret': 0,'message':u'%s:%s'%(_(u'The device was not found!'),ret)})

    return getJSResponse(
        {
            'ret': 0,
            'message': u"%s"%(
                _(u'{op}: Total {total_device}, new {new_found}')
            ).format(
                op=u"%s"%(_(u'Search completed'))+u'!',
                total_device=ret,
                new_found=len(sns)
            )
        }
    )
#暂时没用
def SendEmpAccAuth(request):
    keys=request.POST.getlist("K")
    for key in keys:
        lve_list=list(level_emp.objects.filter(UserID=key).values_list('level',flat=True))
        emps=employee.objects.filter(id__in=[key])

        #sendLevelToAccEx(emps,levels)

def modifypass(request):
    """
    修改密码，仅针对pull设备
    """
    id=request.POST.get('id',0)
    old_password = request.POST.get('old_password')
    new_password = request.POST.get('new_password')
    new_password1 = request.POST.get('new_password1')
    obj = searchs.objects.get(id=id)

    commpro = ctypes.windll.LoadLibrary(settings.FILEPATH+"\zkeco_dlls\plcommpro.dll")
    if obj.Protype != 'push' and obj.signature:  # 当前软件只支持pull, push，故protype只判断这些， 广播修改密码，仅支持安全改造后的设备
        if new_password != new_password1:
            return getJSResponse({'ret': -2, "message": u"%s" % (_('The new password and the confirmation password do not match'))})
        ret = commpro.ModifyPassword(b"UDP", b"***************", obj.MAC.encode(), old_password.encode(), new_password.encode())
        if ret == 0:
            # 同时更新device_options表中保存的密码
            cobj = device_options.objects.filter(SN=obj.SN, ParaName='COMKey')
            if not cobj:
                dobj = device_options()
                dobj.ParaName = 'COMKey'
                dobj.ParaValue = new_password1
                dobj.SN_id = obj.SN
                dobj.save()
            else:
                cobj[0].ParaValue = new_password1
                cobj[0].save()
            obj.save()
            cache.delete("iclock_" + obj.SN)
            return getJSResponse({'ret': 0, "message": u"%s" % (_('Password modification successful'))})
        elif ret == -14:
            return getJSResponse({'ret': -3, "message": u"%s" % (_('Old password incorrect'))})
        elif ret == -2:
            return getJSResponse({"ret": -2, "message":u"%s, %s %s %s %s"%(_('fail to edit!'), _('error code'), ret, _('Command not responded'), _('Please search for the device again and try again'))})
        else:
            return getJSResponse({'ret': -4, "message": u"%s %s: %s" % (_('Password modification failed, tools can be used to attempt modification'), _('error code'), ret)})
    else:
        return getJSResponse({'ret': -1, "message": u"%s" % (_('This device does not support changing passwords'))})

def save_modify_device(request):
    id=request.POST.get('id',0)
    try:
        comkey=request.POST.get('COMKey','')
        obj=searchs.objects.get(id=id)
        old_ip=obj.IPAddress
        obj.IPAddress=request.POST.get('IPAddress','').strip()
        obj.NetMask=request.POST.get('NetMask','').strip()
        obj.GATEIPAddress=request.POST.get('GATEIPAddress','').strip()
        obj.WebServerIP=request.POST.get('WebServerIP','').strip()
        obj.WebServerPort=request.POST.get('WebServerPort','').strip()

        # WebServerURL是WebServerIP 和 WebServerPort的组合，可支持https, 用于push控制器
        web_server_url = "{}:{}".format(obj.WebServerIP, obj.WebServerPort)
        if not web_server_url.startswith('http'):
            web_server_url = 'http://' + web_server_url
        obj.WebServerURL = web_server_url
        # WebServerIP前端输入可能带协议头http/https，这边过滤掉，只留ip
        obj.WebServerIP = obj.WebServerIP.replace('http://','').replace('https://','')

        # pull 安全改造
        if obj.Protype != 'push' and obj.signature and not comkey:
            return getJSResponse({"ret":-1, "message":u"%s"%(_('The current device connection password cannot be empty, please fill in the password first'))})

        if not comkey:
            obj.save()
        commpro = ctypes.windll.LoadLibrary(settings.FILEPATH+"\zkeco_dlls\plcommpro.dll")
        if comkey:
            if obj.Protype=='push':
                s = "MAC=%s,ComPwd=%s,IPAddress=%s,NetMask=%s,WebServerIP=%s,WebServerPort=%s,WebServerURL=%s,Reboot=1" % (
                        obj.MAC,comkey,obj.IPAddress,obj.NetMask,obj.WebServerIP,obj.WebServerPort,obj.WebServerURL)
            else:
                # s = "MAC=%s,ComPwd=%s,IPAddress=%s,NetMask=%s,Reboot=1" % (obj.MAC,comkey,obj.IPAddress,obj.NetMask)
                s = "MAC=%s,IPAddress=%s,NetMask=%s,Reboot=1" % (obj.MAC,obj.IPAddress,obj.NetMask)
        else:
            if obj.Protype=='push':
                s = "MAC=%s,IPAddress=%s,NetMask=%s,GATEIPAddress=%s,WebServerIP=%s,WebServerPort=%s,WebServerURL=%s,Reboot=1" % (
                    obj.MAC,obj.IPAddress,obj.NetMask,obj.GATEIPAddress,obj.WebServerIP,obj.WebServerPort,obj.WebServerURL)
            else:
                s = "MAC=%s,IPAddress=%s,NetMask=%s,GATEIPAddress=%s,Reboot=1" % (obj.MAC,obj.IPAddress,obj.NetMask,obj.GATEIPAddress)

        if six.PY3:
            s=s.encode('gb18030')

        if not comkey or obj.Protype=='push':
            p_buf = ctypes.create_string_buffer(s)
            ret = commpro.ModifyIPAddress(b"UDP", b"***************", p_buf)
			#pull设备,UDP广播方式修改控制器IP地址（考虑到设备的安全性，只能修改没有设密码的控制器的IP地址，子网掩码和网关）,控制器带了密码无法修改。
            if ret >= 0:  # 对于安全改造前的设备，返回结果>=0都判断为修改成功
                return getJSResponse({"ret":0, "message":u"%s"%(_('Successfully modified!'))})
            else:
                return getJSResponse({"ret":-2, "message":u"%s"%_('fail to edit!')})
        else:
            cobj = device_options.objects.filter(SN=obj.SN, ParaName= 'COMKey')
            s = "MAC=%s,SRCIPAddress=%s,IPAddress=%s,GATEIPAddress=%s,NetMask=%s" % (
                    obj.MAC, old_ip, obj.IPAddress, obj.GATEIPAddress, obj.NetMask
                )
            p_buf = ctypes.create_string_buffer(s.encode('gb18030'))
            ret = commpro.ModifyIPAddressEx(b"UDP", b"***************", p_buf, comkey.encode())
            if ret == 0:
                if not cobj:
                    dobj = device_options()
                    dobj.ParaName = 'COMKey'
                    dobj.ParaValue = comkey
                    dobj.SN_id = obj.SN
                    dobj.save()
                else:
                    cobj[0].ParaValue = comkey
                    cobj[0].save()
                cache.delete("iclock_" + obj.SN)
                obj.save()
                return getJSResponse({"ret":0, "message":u"%s"%(_('Successfully modified!'))})
            elif ret == -14:  # 连接密码不正确
                return getJSResponse({"ret":-1, "message":u"%s, %s"%(_('password error'), _('fail to edit!'))})
            elif ret == -101:  # 初始密码未修改
                return getJSResponse({"ret":-1, "message":u"%s, %s"%(_('The initial password has not been changed'), _('fail to edit!'))})
            elif ret == -2:
                return getJSResponse({"ret":-2, "message":u"%s, %s %s %s %s"%(_('fail to edit!'), _('error code'), ret, _('Command not responded'), _('Please search for the device again and try again'))})
            else:
                return getJSResponse({"ret":-3, "message":u"%s, %s %s"%(_('fail to edit!'), _('error code'), ret)})
    except:
        if settings.DEBUG:
            import traceback;traceback.print_exc()
    return getJSResponse({"ret":-1, "message":u"%s"%(_('fail to edit!'))})

def add_devices(request):
    from mysite.core.zkmimi import check_sn_valid
    sns=[]
    keys=request.POST.get('k').split(',')
    objs=searchs.objects.filter(id__in=keys)
    ret=len(keys)
    pull_flag=1
    try:
        auth_zone=zone.objects.get(pk=1)
    except:
        auth_zone=None
    for t in objs:
        ic=iclock.objects.all().exclude(DelTag=1).count()
        if ic>=settings.MAX_DEVICES:
            return getJSResponse({"ret":1,"message":u"%s%s"%(_('Save failed'),_(u'Device exceeds the number of points'))})
        sn=t.SN.upper()
        re=check_sn_valid(sn)#判断设备是否在加密文件devices.dat中
        if re[1]==1:
            if not re[0]:
                return getJSResponse({"ret": 1, "message": u"%s%s-%s" % (_('Save failed'), _(u'The device is not in the list of authorized devices'),sn)})
        
        try:
            obj=iclock.objects.get(SN=sn)
            #if t.Protype and t.Protype!='push':
            if t.Protype != 'push':
                obj.ProductType=15
                obj.IPAddress=t.IPAddress
                pull_flag = 1
                obj.Alias=t.IPAddress

                if obj.DelTag==1:
                    IclockZone(SN=obj,zone=auth_zone).save()
                    obj.DelTag=0
                obj.save()
            else:
                pull_flag=0
                obj.ProductType=5
                if obj.DelTag==1:
                    IclockZone(SN=obj,zone=auth_zone).save()
                    obj.DelTag=0
                    obj.save()
            sns.append(t.id)

        except:
            obj=iclock()
            obj.SN=sn
            obj.IPAddress=t.IPAddress
            obj.DeviceName=t.DeviceName
            obj.Alias=''
            obj.Style=t.Style
            if t.Protype=='push':
                obj.ProductType=5
                pull_flag=0
            else:
                obj.ProductType=15
                obj_exists = iclock.objects.filter(IPAddress=t.IPAddress, ProductType=15).exists()
                if obj_exists:
                    return getJSResponse({
                            "ret":1, "message":u"%s-%s"%(_('Save failed'),_(u'A device with IP %s already exists in the system') % t.IPAddress)
                        })
                pull_flag=1
            if hasattr(obj,'id'):
                ic=iclock.objects.all().count()
                obj.id=ic+1

            obj.save(force_insert=True)
            if auth_zone:
                try:
                    IclockZone(SN=obj,zone=auth_zone).save()
                except:
                    pass
            sns.append(t.id)
        if pull_flag==1:
            cache.set(settings.UNIT+'_restart_pull',1)
    return getJSResponse({
        'ret': 0,
        'message': u"%s"%(
            _(u'{op}: Total {total_device}, new {new_found}')
        ).format(
            op=_(u'Operation is complete!'), total_device=ret, new_found=len(sns)
) + u',{prompt}'.format(prompt=_(u'The system automatically connects the device, please wait for about 1 minute to check if the device status is normal.'))
    })

"""获取device_options设备的网络连接信息"""
def getnetwork(request):
    keys=request.POST.getlist("K")
    commtypes = device_options.objects.filter(SN=keys[0], ParaName='CommType')
    try:
        commtype = commtypes[0].ParaValue  # 原始通信类型
    except:
        commtype = 'ethernet'
    return getJSResponse({"commtype":commtype})


def searchwifi(request):
    import time
    #下发命令获取设备搜索到的wifi列表，iclcok/dataview_ex中将wifi列表存储到cache中
    keys=request.POST.getlist("K")
    aplist=[]
    if keys:
        cmdStr = "DATA QUERY tablename=[APList],fielddesc=*,filter=*"
        saveCmd(keys[0], cmdStr)
        i=0
        while not aplist and i<4:
            time.sleep(3)
            aplist=cache.get("%s-aplist-%s" % (settings.UNIT, keys[0]))
            i+=1
    return getJSResponse(aplist)
