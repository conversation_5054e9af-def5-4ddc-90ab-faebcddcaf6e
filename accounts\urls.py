#!/usr/bin/env python
#coding=utf-8
import sys
from django.conf.urls import include
from django.urls import re_path as url
from django.contrib.auth.decorators import permission_required
from django.contrib.auth.views import *
from mysite import authurls as authurls

urlpatterns = [
    url('^logout/$', authurls.logoff),
    url('^password_change/$', authurls.password_change1),
    url('^password_change/done/$', PasswordChangeDoneView.as_view(), name='password_change_done'),
    url('^login/$', authurls.logon),
    url('^password_reset/$', PasswordResetView.as_view(), name='password_reset'),
    url('^password_reset/done/$', PasswordResetDoneView.as_view(), name='password_reset_done'),
    url('^About/$', authurls.About),
    url('^$', authurls.logon),

]
