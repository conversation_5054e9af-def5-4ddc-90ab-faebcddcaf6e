# -*- coding: utf-8 -*-
# Generated by Django 1.11.7 on 2017-11-30 16:00
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('acc', '0004_auto_20171104_1744'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='accdoor',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u95e8', 'verbose_name_plural': '\u95e8'},
        ),
        migrations.AlterModelOptions(
            name='accdoormap',
            options={'default_permissions': ('browse', 'add', 'change', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='accmap',
            options={'default_permissions': ('browse', 'add', 'change', 'delete')},
        ),
        migrations.AlterModelOptions(
            name='accwiegandfmt',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u97e6\u6839\u5361\u683c\u5f0f', 'verbose_name_plural': '\u97e6\u6839\u5361\u683c\u5f0f'},
        ),
        migrations.AlterModelOptions(
            name='acgroup',
            options={'default_permissions': (), 'verbose_name': 'ACGroup-table', 'verbose_name_plural': 'ACGroup-table'},
        ),
        migrations.AlterModelOptions(
            name='acunlockcomb',
            options={'default_permissions': (), 'verbose_name': 'ACUnlockComb-table', 'verbose_name_plural': 'ACUnlockComb-table'},
        ),
        migrations.AlterModelOptions(
            name='antipassback',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u53cd\u6f5c\u8bbe\u7f6e', 'verbose_name_plural': 'antiback'},
        ),
        migrations.AlterModelOptions(
            name='auxin',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u8f93\u5165', 'verbose_name_plural': '\u8f93\u5165'},
        ),
        migrations.AlterModelOptions(
            name='auxout',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u8f93\u5165', 'verbose_name_plural': '\u8f93\u5165'},
        ),
        migrations.AlterModelOptions(
            name='combopen',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u7ec4\u540d\u79f0', 'verbose_name_plural': 'combopen'},
        ),
        migrations.AlterModelOptions(
            name='combopen_comb',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u5f00\u95e8\u7ec4\u5408', 'verbose_name_plural': 'combopen_comb'},
        ),
        migrations.AlterModelOptions(
            name='combopen_door',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u5f00\u95e8\u7ec4\u5408\u540d\u79f0', 'verbose_name_plural': 'combopen_door'},
        ),
        migrations.AlterModelOptions(
            name='combopen_emp',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u5f00\u95e8\u7ec4\u5408\u4eba\u5458', 'verbose_name_plural': 'combopen_emp'},
        ),
        migrations.AlterModelOptions(
            name='firstopen',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u9996\u4eba\u5e38\u5f00', 'verbose_name_plural': '\u9996\u4eba\u5e38\u5f00'},
        ),
        migrations.AlterModelOptions(
            name='firstopen_emp',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u9996\u4eba\u5e38\u5f00\u4eba\u5458', 'verbose_name_plural': 'FirstOpen_emp'},
        ),
        migrations.AlterModelOptions(
            name='iaccdevitemdefine',
            options={'default_permissions': (), 'permissions': (('iaccMonitor_iaccdevitemdefine', 'iaccMonitor iaccdevitemdefine'), ('iaccAlarm_iaccdevitemdefine', 'iaccAlarm iaccdevitemdefine'), ('iaccUserRights_iaccdevitemdefine', 'iaccUserRights iaccdevitemdefine')), 'verbose_name': 'iaccdevitemdefine', 'verbose_name_plural': 'iaccdevitemdefine'},
        ),
        migrations.AlterModelOptions(
            name='iaccempitemdefine',
            options={'default_permissions': (), 'permissions': (('iaccRecordDetails_iaccempitemdefine', 'iaccRecordDetails iaccempitemdefine'), ('iaccSummaryRecord_iaccempitemdefine', 'iaccSummaryRecord iaccempitemdefine'), ('iaccEmpUserRights_iaccempitemdefine', 'iaccEmpUserRights iaccempitemdefine'), ('iaccEmpDevice_iaccempitemdefine', 'iaccEmpDevice iaccempitemdefine')), 'verbose_name': 'iaccempitemdefine', 'verbose_name_plural': 'iaccempitemdefine'},
        ),
        migrations.AlterModelOptions(
            name='interlock',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (), 'verbose_name': '\u4e92\u9501\u89c4\u5219', 'verbose_name_plural': 'interlock'},
        ),
        migrations.AlterModelOptions(
            name='level',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'permissions': (('addemps_level', 'Add emps'), ('delallemps_level', 'Delete allemps')), 'verbose_name': '\u95e8\u7981\u6743\u9650\u7ec4', 'verbose_name_plural': 'AccLevel'},
        ),
        migrations.AlterModelOptions(
            name='level_door',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u95e8\u7981\u4eba\u5458', 'verbose_name_plural': 'level_emp'},
        ),
        migrations.AlterModelOptions(
            name='level_emp',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u95e8\u7981\u4eba\u5458', 'verbose_name_plural': 'level_emp'},
        ),
        migrations.AlterModelOptions(
            name='linkage',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u8054\u52a8', 'verbose_name_plural': 'linkage'},
        ),
        migrations.AlterModelOptions(
            name='linkage_inout',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u8054\u52a8', 'verbose_name_plural': 'linkage_inout'},
        ),
        migrations.AlterModelOptions(
            name='records',
            options={'default_permissions': (), 'permissions': (('monitor_oplog', 'Real Monitor'), ('acc_records', 'Acc Records'), ('acc_reports', 'Acc Reports')), 'verbose_name': '\u95e8\u7981\u8bb0\u5f55', 'verbose_name_plural': '\u95e8\u7981\u8bb0\u5f55'},
        ),
        migrations.AlterModelOptions(
            name='timezones',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': 'ACTimeZones-table', 'verbose_name_plural': 'ACTimeZones-table'},
        ),
        migrations.AlterModelOptions(
            name='useraccdevice',
            options={'default_permissions': (), 'verbose_name': 'UserACCDevice-table', 'verbose_name_plural': 'UserACCDevice-table'},
        ),
        migrations.AlterModelOptions(
            name='useracdevice',
            options={'default_permissions': (), 'verbose_name': 'UserACDevice-table', 'verbose_name_plural': 'UserACDevice-table'},
        ),
        migrations.AlterModelOptions(
            name='useracmachines',
            options={'default_permissions': (), 'verbose_name': 'UserACMachines-table', 'verbose_name_plural': 'UserACMachines-table'},
        ),
        migrations.AlterModelOptions(
            name='useracprivilege',
            options={'default_permissions': (), 'verbose_name': 'UserACPrivilege-table', 'verbose_name_plural': 'UserACPrivilege-table'},
        ),
        migrations.AlterModelOptions(
            name='zone',
            options={'default_permissions': ('browse', 'add', 'change', 'delete'), 'verbose_name': '\u533a\u57df'},
        ),
    ]
