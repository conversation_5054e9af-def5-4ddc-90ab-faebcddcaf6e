#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import string
from django.contrib import auth
from django.shortcuts import render
from django.utils.translation import gettext_lazy as _
from django.template import loader, Context, RequestContext
from django.conf import settings
from django.contrib.auth.decorators import permission_required, login_required
from mysite.iclock.models import *
from mysite.acc.models import *
#from mysite.iclock.datasproc import *
from django.core.paginator import Paginator
#from mysite.iclock.datas import *
#from mysite.core.menu import *
from mysite.core.zktools import *
from mysite.iclock.models import *

import sys
from mysite.core.zkcmdproc import *


@login_required
def index(request):#门禁权限组设置--按人员设置
    if request.method=='GET':
        tmpFile='acc/'+'level_emp.html'
        tmpFile=request.GET.get('t', tmpFile)
        cc={}


        settings.PAGE_LIMIT=GetParamValue('opt_users_page_limit','30',str(request.user.id))
        limit= int(request.GET.get('l', settings.PAGE_LIMIT))
        colmodels= [
                {'name':'id','hidden':True},
                {'name':'PIN','index':'PIN','width':80,'label':u"%s"%(employee._meta.get_field('PIN').verbose_name),'frozen':True},
                {'name':'EName','width':80,'label':u"%s"%(employee._meta.get_field('EName').verbose_name),'frozen': True},
                {'name':'Gender','width':40,'search':False,'label':u"%s"%(employee._meta.get_field('Gender').verbose_name)},
                {'name':'DeptName','index':'DeptID__DeptName','width':200,'label':u"%s"%(_('department name'))},
                {'name':'Card','width':60,'label':u"%s"%(employee._meta.get_field('Card').verbose_name)},
#				{'name':'Title','width':60,'label':u"%s"%(employee._meta.get_field('Title').verbose_name)},
                {'name':'level_detail','sortable':False,'width':120,'label':u"%s"%(_(u'operating'))},
            ]

        colmodels_level= [
                {'name':'id','hidden':True},
                {'name':'name','index':'','width':120,'label':u"%s"%(_(u'Privilege Group Name'))},
                {'name':'tz','width':160,'label':u"%s"%(_(u'period'))}
            ]


        cc['limit']=limit
        cc['colModel']=dumps1(colmodels)
        cc['colModel_level']=dumps1(colmodels_level)
        request.model=level

        return render(request,tmpFile,cc)

#		return render(tmpFile,cc,RequestContext(request, {}))



def get_level_emp(request):
    #print request.POST
    id=request.GET.get('id')
    limit= int(request.POST.get('rows', 0))
    sidx=request.POST.get('sidx')
    if sidx=='tz':
        sidx='level__timeseg__tz'
    else:
        sidx='level'
    sord=request.POST.get('sord')
    try:
        offset = int(request.POST.get('page', 1))
    except:
        offset=1
    if sord=='desc':
        objs=level_emp.objects.filter(UserID=id).order_by('-'+sidx)
    else:
        objs=level_emp.objects.filter(UserID=id).order_by(sidx)
    p=Paginator(objs, limit)
    iCount=p.count
    if iCount<(offset-1)*limit:
        offset=1
    page_count=p.num_pages
    pp=p.page(offset)
    objs=pp.object_list
    re=[]
    Result={}
    Result['datas']=re
    for t in objs:
        d={}
        d['id']=t.id

        d['name']=t.level.name
        d['tz']=t.level.timeseg.Name

        re.append(d.copy())
    if offset>page_count:offset=page_count
    item_count =iCount
    Result['item_count']=item_count
    Result['page']=offset
    Result['limit']=limit
    Result['from']=(offset-1)*limit+1
    Result['page_count']=page_count
    Result['datas']=re
    rs="{"+""""page":"""+str(Result['page'])+","+""""total":"""+str(Result['page_count'])+","+""""records":"""+str(Result['item_count'])+","+""""rows":"""+dumps(Result['datas'])+"""}"""
    return getJSResponse(rs)


