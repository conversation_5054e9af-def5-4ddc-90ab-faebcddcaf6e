## 会议模块
### 会议模块-设备管理功能点新增会议设备
#### 会议模块设备管理功能点RAG检索锚点
<!-- RAG检索锚点: 新增会议设备、会议设备管理、会议室设备、设备添加、会议设备配置 -->

#### 新增会议设备功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择会议室]
    D --> E[选择设备]
    E --> F[验证设备类型]
    F --> G{设备类型验证}
    G -->|不支持| H[提示设备类型错误]
    G -->|支持| I[检查设备重复]
    I --> J{是否重复}
    J -->|重复| K[提示设备已存在]
    J -->|不重复| L[保存设备关联]
    L --> M[更新会议室设备列表]
    M --> N[同步设备配置]
    N --> O[记录操作日志]
    O --> P[返回成功信息]
    C --> Q[结束]
    H --> Q
    K --> Q
    P --> Q
```
#### 新增会议设备功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增会议设备业务流程。
2. 权限验证：验证当前用户是否具有会议设备管理权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行后续操作
3. 会议室选择：
   - 从MeetLocation表中选择要添加设备的会议室
   - 验证会议室的有效性和可用状态
   - 确保会议室未被删除（DelTag=0）
4. 设备选择：
   - 从iclock表中选择要添加的设备
   - 显示可用的设备列表供用户选择
   - 过滤已删除的设备（DelTag=0）
5. 设备类型验证：
   - 验证选择的设备是否为会议设备类型
   - 支持的设备类型包括ProductType为8的信息屏设备
   - 支持ProductType为1的考勤设备用于会议签到
6. 设备重复检查：
   - 检查选择的设备是否已经关联到该会议室
   - 通过meet_devices表的唯一约束验证
   - 防止同一设备重复添加到同一会议室
7. 设备关联保存：
   - 在meet_devices表中创建会议室与设备的关联记录
   - 设置LocationID字段关联会议室
   - 设置SN字段关联设备序列号
8. 设备列表更新：
   - 更新会议室的设备列表显示
   - 刷新设备关联状态
   - 更新设备数量统计
9. 设备配置同步：
   - 将会议室信息同步到设备端
   - 对于信息屏设备，同步会议室基本信息
   - 对于考勤设备，配置会议签到参数
10. 操作日志记录：记录设备添加操作的详细日志信息。
11. 流程完成：返回操作成功信息，完成设备添加流程。

#### 新增会议设备功能点与其他模块及子模块业务关联说明
1. **与会议室管理的关联**：
   - 新增设备需要关联到具体的会议室
   - 会议室状态影响设备的可用性
   - 会议室删除时需要同步删除设备关联

2. **与设备管理模块的关联**：
   - 设备必须在系统中已注册才能添加到会议室
   - 设备状态变更影响会议功能的可用性
   - 设备删除时需要清理会议室关联

3. **与会议安排模块的关联**：
   - 会议设备用于会议信息的显示和签到
   - 会议安排信息需要同步到关联设备
   - 设备故障影响会议的正常进行

4. **与权限管理模块的关联**：
   - 设备添加操作需要相应的管理权限
   - 设备访问权限影响会议功能的使用
   - 用户权限控制设备管理操作范围

5. **与系统配置模块的关联**：
   - 设备配置参数依赖于系统全局设置
   - 设备通信参数需要与系统网络配置匹配
   - 设备功能开关受系统模块配置影响

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 会议模块
### 会议模块-设备管理功能点编辑会议设备
#### 会议模块设备管理功能点RAG检索锚点
<!-- RAG检索锚点: 编辑会议设备、修改设备配置、会议设备参数、设备信息更新 -->

#### 编辑会议设备功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[加载设备信息]
    D --> E[显示编辑界面]
    E --> F[修改设备配置]
    F --> G[验证配置参数]
    G --> H{参数验证}
    H -->|验证失败| I[显示错误信息]
    H -->|验证通过| J[检查设备冲突]
    J --> K{是否有冲突}
    K -->|有冲突| L[提示冲突信息]
    K -->|无冲突| M[保存设备配置]
    M --> N[更新设备状态]
    N --> O[同步配置到设备]
    O --> P[记录操作日志]
    P --> Q[返回成功信息]
    I --> F
    L --> F
    C --> R[结束]
    Q --> R
```
#### 编辑会议设备功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动编辑会议设备业务流程。
2. 权限验证：验证当前用户是否具有设备编辑权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行编辑操作
3. 设备信息加载：
   - 从meet_devices表中加载设备关联信息
   - 从iclock表中获取设备的详细配置信息
   - 加载设备的当前状态和参数设置
4. 编辑界面显示：
   - 显示设备的当前配置信息
   - 提供可编辑的配置项界面
   - 显示设备类型和基本属性
5. 设备配置修改：
   - 允许修改设备的网络配置参数
   - 可以调整设备的功能设置
   - 支持修改设备的显示参数
6. 配置参数验证：
   - 验证网络配置的有效性
   - 检查参数值的合法性和范围
   - 确保配置参数符合设备规范
7. 设备冲突检查：
   - 检查IP地址是否与其他设备冲突
   - 验证设备序列号的唯一性
   - 确保设备配置不会影响其他功能
8. 设备配置保存：
   - 将修改后的配置保存到数据库
   - 更新iclock表中的设备信息
   - 保持数据的一致性和完整性
9. 设备状态更新：
   - 更新设备的最后修改时间
   - 刷新设备的配置状态
   - 标记设备需要重新同步
10. 配置同步：
    - 将新配置同步到设备端
    - 发送配置更新命令到设备
    - 确保设备端配置与服务器端一致
11. 操作日志记录：记录设备编辑操作的详细日志。
12. 流程完成：返回操作成功信息，完成设备编辑流程。

#### 编辑会议设备功能点与其他模块及子模块业务关联说明
1. **与设备监控模块的关联**：
   - 设备配置变更影响监控参数设置
   - 设备状态监控需要反映配置变更
   - 配置同步状态影响设备健康检查

2. **与会议管理模块的关联**：
   - 设备配置变更可能影响会议功能
   - 会议信息显示依赖于设备配置
   - 设备故障影响会议的正常进行

3. **与网络管理模块的关联**：
   - 设备网络配置变更需要网络支持
   - IP地址变更需要网络路由更新
   - 网络安全策略影响设备访问

4. **与系统日志模块的关联**：
   - 所有配置变更都需要记录详细日志
   - 配置历史用于问题排查和审计
   - 日志信息支持配置回滚操作

5. **与权限管理模块的关联**：
   - 设备编辑权限控制操作范围
   - 不同用户可编辑的配置项不同
   - 敏感配置需要更高级别权限

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 会议模块
### 会议模块-设备管理功能点删除会议设备
#### 会议模块设备管理功能点RAG检索锚点
<!-- RAG检索锚点: 删除会议设备、移除设备关联、设备解绑、会议设备删除 -->

#### 删除会议设备功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的设备]
    D --> E[确认删除操作]
    E --> F{用户确认}
    F -->|取消删除| G[返回设备列表]
    F -->|确认删除| H[检查设备使用状态]
    H --> I{设备是否在使用}
    I -->|正在使用| J[提示设备使用中]
    I -->|未使用| K[清理设备数据]
    K --> L[删除设备关联]
    L --> M[清理会议信息]
    M --> N[同步删除到设备]
    N --> O[记录操作日志]
    O --> P[返回成功信息]
    C --> Q[结束]
    G --> Q
    J --> Q
    P --> Q
```
#### 删除会议设备功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除会议设备业务流程。
2. 权限验证：验证当前用户是否具有设备删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行删除操作
3. 设备选择：
   - 从会议室设备列表中选择要删除的设备
   - 显示设备的基本信息供确认
   - 支持单个或批量删除操作
4. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 要求用户明确确认删除意图
5. 设备使用状态检查：
   - 检查设备是否正在进行中的会议中使用
   - 验证设备是否有未完成的任务
   - 确保删除操作不会影响正在进行的业务
6. 设备数据清理：
   - 清理设备上的会议相关数据
   - 删除设备上的参会人员信息
   - 清除设备上的会议安排信息
7. 设备关联删除：
   - 从meet_devices表中删除设备关联记录
   - 解除设备与会议室的绑定关系
   - 更新会议室的设备列表
8. 会议信息清理：
   - 清理与该设备相关的会议配置
   - 删除设备上的会议通知信息
   - 清除设备的会议显示内容
9. 同步删除操作：
   - 向设备发送清理命令
   - 清除设备上的会议信息（CLEAR MEETINFO）
   - 清除设备上的参会人员（CLEAR PERSMEET）
10. 操作日志记录：记录设备删除操作的详细日志。
11. 流程完成：返回操作成功信息，完成设备删除流程。

#### 删除会议设备功能点与其他模块及子模块业务关联说明
1. **与会议安排模块的关联**：
   - 设备删除前需要检查是否有进行中的会议
   - 删除设备会影响会议信息的显示
   - 需要通知相关会议的组织者设备变更

2. **与参会人员模块的关联**：
   - 删除设备需要清理设备上的参会人员数据
   - 参会人员的签到功能会受到影响
   - 需要重新配置其他设备的参会人员信息

3. **与会议通知模块的关联**：
   - 设备删除影响会议通知的发送渠道
   - 需要更新会议通知的显示设备列表
   - 通知内容可能需要调整显示方式

4. **与实时监控模块的关联**：
   - 删除设备需要从监控列表中移除
   - 设备状态监控停止对该设备的跟踪
   - 监控报表需要更新设备统计信息

5. **与系统配置模块的关联**：
   - 设备删除操作受系统权限配置限制
   - 删除操作需要记录到系统审计日志
   - 系统备份策略需要考虑设备配置变更

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
