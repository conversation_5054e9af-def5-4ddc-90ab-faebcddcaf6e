# -*- coding: utf-8 -*-
# Generated by Django 1.11.13 on 2019-01-11 10:58
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('iclock', '0022_auto_20190111_1058'),
        ('acc', '0012_auto_20180824_1615'),
    ]

    operations = [
        migrations.CreateModel(
            name='ZoneLocation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='iclock.applocation')),
                ('zone', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='acc.zone')),
            ],
            options={
                'db_table': 'app_locationzone',
                'verbose_name': 'zonelocation',
                'verbose_name_plural': 'zonelocation',
            },
        ),
        migrations.AlterField(
            model_name='records',
            name='event_no',
            field=models.IntegerField(choices=[(-1, 'nothing'), (0, 'Normal opening'), (1, 'Punch during Passage Mode Time Zone'), (2, 'The first person opens the door (swipe the card)'), (3, 'Multiple people open the door normally'), (4, 'Emergency password opens'), (5, 'Open during Passage Mode Time Zone'), (6, 'trigger linkage event'), (7, 'Cancel the alarm'), (8, 'Open the door remotely'), (9, 'remote closing'), (10, 'Disable the usual opening time of the day'), (11, 'Enable the usual opening time of the day'), (12, 'Turn on auxiliary output'), (13, 'Turn off auxiliary output'), (14, 'Normally open the door by fingerprint'), (15, 'Multiple people open the door (by fingerprint)'), (16, 'Filling fingerprints during the normally open time period'), (17, 'Kajia Fingerprint Opens the Door'), (18, 'First card opens (by fingerprint)'), (19, 'First card opening (kajia fingerprint)'), (20, 'Swipe card interval is too short'), (21, 'Do not valid time period'), (22, 'Illegal time period'), (23, 'Unauthorized access'), (24, 'anti-submarine'), (25, 'Interlock failed'), (26, 'Multiple verification waits'), (27, 'People are not registered'), (28, 'door open timeout'), (29, 'The card has expired'), (30, 'wrong password'), (31, 'The fingerprint interval is too short'), (32, 'Verificando multi-usuario con huellas'), (33, 'Expired validity period'), (34, 'People are not registered'), (35, 'Do not valid time period'), (36, 'The door is not valid (press the exit button)'), (37, 'Unable to close the door during the normal opening period'), (38, 'The card has been lost'), (39, 'blacklist'), (41, 'Verification error'), (42, 'The Wiegand Card format is wrong'), (44, 'Anti-submarine verification failed'), (48, 'Multiple people fail to open the door'), (51, 'Multi-person verification'), (52, 'Multiple authentication failed'), (63, 'The door has been locked'), (64, 'The door button is not in the time period'), (65, 'Auxiliary input is not within the time period'), (100, 'tamper alarm'), (101, 'Duress password to open the door'), (102, 'The door was accidentally opened'), (104, 'Invalid card continuously swipe 5 times'), (200, 'The door is open'), (201, 'The door is closed'), (202, 'Going out the door to open the door'), (204, 'The end of the normally open time period'), (205, 'Remote opening is always open'), (206, 'Device startup'), (207, 'Password opens'), (208, 'Super user opens the door'), (209, 'Trigger exit button (locked)'), (216, 'In the normally open time period (by password)'), (220, 'Auxiliary input point disconnected'), (221, 'Auxiliary input point short circuit'), (225, 'The input point is back to normal'), (226, 'Input point alarm'), (222, 'Anti-submarine verification succeeded'), (223, 'anti-submarine verification'), (224, 'Press the doorbell'), (233, 'Activate Lockdown'), (232, 'Operation Success'), (234, 'Deactivate Lockdown'), (300, 'Trigger Global Linkage'), (500, 'Global Anti-Passback(logical)')], null=True, verbose_name='Event Description'),
        ),
        migrations.AlterUniqueTogether(
            name='records',
            unique_together=set([('TTime', 'SN', 'dev_serial_num', 'event_no', 'event_point_name')]),
        ),
    ]
