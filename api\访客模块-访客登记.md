## 访客模块
### 访客模块-访客登记功能点新增登记
#### 访客模块访客登记功能点RAG检索锚点
<!-- RAG检索锚点: 新增访客登记、访客登记、访客签到、现场登记、访客入场 -->

#### 新增登记功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[显示登记表单]
    D --> E[填写访客信息]
    E --> F[选择被访人]
    F --> G[设置访问事由]
    G --> H[拍摄访客照片]
    H --> I[验证登记数据]
    I --> J{数据验证}
    J -->|验证失败| K[显示错误信息]
    J -->|验证通过| L[检查黑名单]
    L --> M{是否在黑名单}
    M -->|在黑名单| N[提示访客被禁止]
    M -->|不在黑名单| O[检查重复登记]
    O --> P{是否已登记}
    P -->|已登记未签离| Q[提示访客已进入]
    P -->|未登记或已签离| R[生成访客卡号]
    R --> S[保存登记记录]
    S --> T[同步到门禁系统]
    T --> U[记录操作日志]
    U --> V[返回成功信息]
    K --> E
    N --> E
    Q --> E
    C --> W[结束]
    V --> W
```
#### 新增登记功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增访客登记业务流程。
2. 权限验证：验证当前用户是否具有访客登记权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行登记操作
3. 登记表单显示：
   - 显示访客登记表单界面
   - 提供必填和可选字段的输入控件
   - 显示登记规则和注意事项
4. 访客信息填写：
   - 访客姓名（VisempName）：必填，访客的真实姓名
   - 身份证号（SSN）：必填，访客的身份证号码
   - 证件类型（CertificateType）：可选，访客的证件类型
   - 手机号码（VisPhone）：可选，访客的联系方式
   - 访客性别（VisGender）：可选，访客的性别信息
   - 访客公司（VisCompany）：可选，访客所属公司
   - 访客人数（VisitingNum）：可选，访客人数（1-1000）
   - 车牌号码（platenumber）：可选，访客的车牌号码
   - 访客地址（Address）：可选，访客的地址信息
   - 访客民族（National）：可选，访客的民族信息
5. 被访人选择：
   - 从被访人列表中选择接待人员
   - 验证被访人的有效性和在职状态
   - 获取被访人的联系方式和部门信息
6. 访问事由设置：
   - 从来访事由列表中选择访问原因
   - 填写详细的访问目的和内容
   - 访问事由用于访客分类和统计
7. 访客照片拍摄：
   - 现场拍摄访客的实时照片
   - 照片用于身份验证和安全识别
   - 照片格式和质量验证
8. 登记数据验证：
   - 验证必填字段是否完整
   - 检查身份证号格式的正确性
   - 验证手机号码格式的有效性
   - 确保访客人数在合理范围内（1-1000）
   - 检查访问事由和车牌号不包含特殊符号
9. 黑名单检查：
   - 检查访客身份证号是否在黑名单中
   - 通过Blacklist表验证访客的安全状态
   - 黑名单中的访客无法进行登记
10. 重复登记检查：
    - 检查访客是否已经登记且未签离
    - 通过visitionlogs表查询VisState=0的记录
    - 防止同一访客重复登记进入
11. 访客卡号生成：
    - 根据系统配置自动生成访客卡号
    - 支持自动递增和自定义卡号模式
    - 卡号用于访客的身份识别和门禁控制
    - 调用issuecard_verification验证卡号有效性
12. 登记记录保存：
    - 将登记信息保存到visitionlogs表中
    - 设置进入时间（EnterTime）为当前时间
    - 设置访客状态（VisState）为0（未签离）
    - 关联预约记录并更新预约状态为已来访
    - 保存最新卡号到系统参数表
13. 门禁系统同步：
    - 调用sendVisitorsToAcc函数同步访客信息到门禁系统
    - 根据被访人权限配置下发访客门禁权限
    - 设置访客的有效时间和访问区域
14. 操作日志记录：记录访客登记操作的详细日志。
15. 流程完成：返回登记成功信息，完成访客登记流程。

#### 新增登记功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 登记时会自动关联相关的预约记录
   - 登记成功后更新预约状态为已来访
   - 预约信息可以预填登记表单

2. **与访客黑名单模块的关联**：
   - 登记时需要检查访客是否在黑名单中
   - 黑名单中的访客无法进行登记
   - 黑名单验证在登记保存时执行

3. **与被访人管理模块的关联**：
   - 登记时需要选择具体的被访人
   - 被访人信息用于登记记录的关联
   - 被访人的权限配置影响访客的门禁权限

4. **与门禁系统的关联**：
   - 登记成功后需要同步访客信息到门禁系统
   - 访客获得相应的门禁权限和访问时间
   - 门禁系统用于访客的进出控制

5. **与卡号管理模块的关联**：
   - 登记时需要生成唯一的访客卡号
   - 卡号用于访客的身份识别和门禁控制
   - 卡号管理确保访客身份的唯一性

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-访客登记功能点离开登记
#### 访客模块访客登记功能点RAG检索锚点
<!-- RAG检索锚点: 离开登记、访客签离、访客离场、签离登记、访客退场 -->

#### 离开登记功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[输入访客信息]
    D --> E[查找访客记录]
    E --> F{是否找到记录}
    F -->|未找到| G[提示访客记录不存在]
    F -->|找到记录| H[检查访客状态]
    H --> I{访客是否已签离}
    I -->|已签离| J[提示访客已离开]
    I -->|未签离| K[拍摄离场照片]
    K --> L[设置离开时间]
    L --> M[更新访客状态]
    M --> N[清除门禁权限]
    N --> O[记录操作日志]
    O --> P[返回成功信息]
    G --> D
    J --> D
    C --> Q[结束]
    P --> Q
```
#### 离开登记功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动访客离开登记业务流程。
2. 权限验证：验证当前用户是否具有访客登记权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行离开登记操作
3. 访客信息输入：
   - 输入访客的身份证号或卡号
   - 支持扫描身份证或刷卡方式
   - 支持手动输入访客标识信息
4. 访客记录查找：
   - 根据输入的信息查找访客登记记录
   - 从visitionlogs表中查询匹配的记录
   - 查找条件包括身份证号、卡号等标识
5. 记录存在性验证：
   - 验证是否找到对应的访客记录
   - 未找到记录时提示访客记录不存在
   - 可能是访客未登记或信息输入错误
6. 访客状态检查：
   - 检查访客的当前状态（VisState字段）
   - 验证访客是否已经签离
   - VisState=0表示未签离，VisState=1表示已签离
7. 离场照片拍摄：
   - 现场拍摄访客的离场照片
   - 照片用于离场记录和安全验证
   - 保存照片到指定路径（exit_snap_photo字段）
8. 离开时间设置：
   - 设置访客的离开时间（ExitTime字段）
   - 离开时间为当前系统时间
   - 用于计算访客的停留时长
9. 访客状态更新：
   - 更新访客状态（VisState）为1（已签离）
   - 更新记录的修改时间戳（OpStamp）
   - 保存离场相关信息到数据库
10. 门禁权限清除：
    - 清除访客在门禁系统中的权限
    - 调用相关函数删除访客的门禁数据
    - 确保访客离开后无法再次进入
11. 操作日志记录：记录访客离开登记操作的详细日志。
12. 流程完成：返回离开登记成功信息，完成访客离场流程。

#### 离开登记功能点与其他模块及子模块业务关联说明
1. **与门禁系统的关联**：
   - 离开登记后需要清除访客的门禁权限
   - 确保访客离开后无法再次进入受控区域
   - 门禁系统实时更新访客的权限状态

2. **与访客统计模块的关联**：
   - 离开登记完成访客的完整访问记录
   - 访问时长用于访客统计和分析
   - 离场数据用于访客流量统计

3. **与安全管理模块的关联**：
   - 离场照片用于安全验证和记录
   - 访客离场信息用于安全审计
   - 异常离场情况需要安全处理

4. **与报表系统的关联**：
   - 完整的访客记录用于生成访客报表
   - 访问时长统计用于效率分析
   - 离场数据用于访客趋势分析

5. **与通知系统的关联**：
   - 访客离场可能需要通知被访人
   - 异常离场情况需要发送告警通知
   - 离场确认信息可以发送给相关人员

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
