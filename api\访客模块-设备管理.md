## 访客模块
### 访客模块-设备管理功能点新增设备
#### 访客模块设备管理功能点RAG检索锚点
<!-- RAG检索锚点: 新增访客设备、访客设备管理、访客设备添加、设备注册、访客机管理 -->

#### 新增设备功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[检查自动添加设置]
    D --> E{是否允许自动添加}
    E -->|不允许| F[提示不允许自动添加]
    E -->|允许| G[填写设备信息]
    G --> H[验证设备信息]
    H --> I{信息验证}
    I -->|验证失败| J[显示错误信息]
    I -->|验证通过| K[检查设备数量限制]
    K --> L{是否超出限制}
    L -->|超出限制| M[提示设备数量超限]
    L -->|未超出| N[保存设备信息]
    N --> O[设置设备参数]
    O --> P[更新设备状态]
    P --> Q[记录操作日志]
    Q --> R[返回成功信息]
    J --> G
    C --> S[结束]
    F --> S
    M --> S
    R --> S
```
#### 新增设备功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动新增访客设备业务流程。
2. 权限验证：验证当前用户是否具有设备管理权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行设备添加操作
3. 自动添加设置检查：
   - 检查系统参数opt_basic_dev_auto是否为1
   - 验证是否允许自动添加新设备
   - 如果不允许且设备不存在，则拒绝添加
4. 设备信息填写：
   - 设备序列号（SN）：必填，设备的唯一标识
   - 设备IP地址（IPAddress）：必填，设备的网络地址
   - 设备名称（DeviceName）：可选，设备的显示名称
   - 固件版本（FWVersion）：可选，设备的固件版本信息
5. 设备信息验证：
   - 验证设备序列号的格式和唯一性
   - 检查IP地址的有效性和网络可达性
   - 验证设备名称的长度和字符限制
6. 设备数量限制检查：
   - 检查当前系统中已添加的设备数量
   - 通过MAX_DEVICES配置项控制最大设备数量
   - 超出限制时提示用户联系供应商增加授权
7. 设备信息保存：
   - 将设备信息保存到iclock表中
   - 设置ProductType为10，标识为访客设备
   - 设置Style为81，标识为API对接的在线访客设备
   - 设置DelTag为0，标识设备未删除
   - 记录设备的最后活动时间（LastActivity）
8. 设备参数设置：
   - 配置设备的基本参数和功能开关
   - 设置设备的通信参数和网络配置
   - 初始化设备的访客相关配置
9. 设备状态更新：
   - 更新设备的在线状态和连接状态
   - 设置设备的初始工作模式
   - 同步设备时间和系统参数
10. 操作日志记录：记录设备添加操作的详细日志。
11. 流程完成：返回操作成功信息，完成设备添加流程。

#### 新增设备功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 新增设备后可以接收访客预约信息
   - 设备用于显示预约访客的信息
   - 预约审核通过后会同步到访客设备

2. **与访客登记模块的关联**：
   - 设备用于访客现场登记和签到
   - 访客登记信息会同步到设备
   - 设备支持访客身份验证和拍照

3. **与访客黑名单模块的关联**：
   - 黑名单信息会同步到访客设备
   - 设备用于黑名单人员的识别和拦截
   - 黑名单变更会实时推送到设备

4. **与系统配置模块的关联**：
   - 设备添加受系统授权配置限制
   - 设备参数配置依赖于系统全局设置
   - 设备功能开关受系统模块配置影响

5. **与网络管理模块的关联**：
   - 设备添加需要网络连通性支持
   - 设备IP地址需要与网络规划匹配
   - 网络安全策略影响设备访问权限

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-设备管理功能点删除设备
#### 访客模块设备管理功能点RAG检索锚点
<!-- RAG检索锚点: 删除访客设备、移除访客设备、设备删除、访客机删除、设备管理删除 -->

#### 删除设备功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[选择要删除的设备]
    D --> E[检查设备使用状态]
    E --> F{设备是否在使用}
    F -->|正在使用| G[提示设备使用中]
    F -->|未使用| H[确认删除操作]
    H --> I{用户确认}
    I -->|取消删除| J[返回设备列表]
    I -->|确认删除| K[执行软删除]
    K --> L[清理设备数据]
    L --> M[清理关联信息]
    M --> N[记录操作日志]
    N --> O[返回成功信息]
    C --> P[结束]
    G --> P
    J --> P
    O --> P
```
#### 删除设备功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动删除访客设备业务流程。
2. 权限验证：验证当前用户是否具有设备删除权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行删除操作
3. 设备选择：
   - 从访客设备列表中选择要删除的设备
   - 显示设备的基本信息供确认
   - 支持单个设备的删除操作
4. 设备使用状态检查：
   - 检查设备是否有正在进行的访客业务
   - 验证设备是否有未完成的任务
   - 确保删除操作不会影响正在进行的访客服务
5. 删除确认：
   - 显示删除确认对话框
   - 提示删除操作的影响和后果
   - 要求用户明确确认删除意图
6. 软删除执行：
   - 设置DelTag字段为1，标记设备为已删除
   - 保留设备数据用于历史记录和审计
   - 避免物理删除造成的数据丢失
7. 设备数据清理：
   - 清理设备上的访客预约信息
   - 删除设备上的访客登记数据
   - 清除设备上的黑名单信息
8. 关联信息清理：
   - 清理设备与访客业务的关联记录
   - 更新相关统计数据和报表
   - 释放设备占用的系统资源
9. 操作日志记录：记录设备删除操作的详细日志。
10. 流程完成：返回删除成功信息，完成设备删除流程。

#### 删除设备功能点与其他模块及子模块业务关联说明
1. **与访客预约模块的关联**：
   - 删除设备需要清理设备上的预约信息
   - 预约信息不再推送到已删除的设备
   - 需要重新分配预约信息到其他设备

2. **与访客登记模块的关联**：
   - 删除设备影响访客现场登记功能
   - 需要指导访客使用其他可用设备
   - 登记数据不再同步到已删除设备

3. **与访客黑名单模块的关联**：
   - 删除设备需要清理设备上的黑名单数据
   - 黑名单更新不再推送到已删除设备
   - 安全防护功能受到影响

4. **与实时监控模块的关联**：
   - 删除设备需要从监控列表中移除
   - 设备状态监控停止对该设备的跟踪
   - 监控报表需要更新设备统计信息

5. **与系统配置模块的关联**：
   - 设备删除操作受系统权限配置限制
   - 删除操作需要记录到系统审计日志
   - 系统备份策略需要考虑设备配置变更

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥

## 访客模块
### 访客模块-设备管理功能点刷新设备列表
#### 访客模块设备管理功能点RAG检索锚点
<!-- RAG检索锚点: 刷新设备列表、设备状态刷新、设备信息更新、设备列表更新 -->

#### 刷新设备列表功能点业务流程图

```mermaid
   graph TD
    A[开始] --> B{权限验证}
    B -->|权限不足| C[提示权限不足]
    B -->|权限通过| D[清理缓存数据]
    D --> E[重新查询设备列表]
    E --> F[更新设备状态]
    F --> G[检查设备在线状态]
    G --> H[统计设备信息]
    H --> I[更新界面显示]
    I --> J[记录刷新日志]
    J --> K[返回刷新结果]
    C --> L[结束]
    K --> L
```
#### 刷新设备列表功能点业务流程以及规则说明：
1. 流程起始：从"开始"节点启动刷新设备列表业务流程。
2. 权限验证：验证当前用户是否具有设备查看权限。
   - 权限不足时，提示权限不足并结束流程
   - 权限通过时，继续执行刷新操作
3. 缓存数据清理：
   - 清理过期的设备状态缓存
   - 释放内存中的临时数据
   - 确保获取最新的设备信息
4. 设备列表重新查询：
   - 从iclock表中查询ProductType为10的访客设备
   - 过滤已删除的设备（DelTag=0）
   - 按照设备序列号或添加时间排序
5. 设备状态更新：
   - 检查设备的最后活动时间（LastActivity）
   - 根据心跳时间判断设备在线离线状态
   - 更新设备的连接状态和工作状态
6. 设备在线状态检查：
   - 通过心跳机制检测设备在线状态
   - 超过一定时间未收到心跳则标记为离线
   - 统计在线设备和离线设备数量
7. 设备信息统计：
   - 统计访客设备的总数量
   - 计算在线设备和离线设备的比例
   - 生成设备状态分布统计
8. 界面显示更新：
   - 更新设备列表的显示内容
   - 刷新设备状态指示器
   - 更新设备统计信息显示
9. 刷新日志记录：记录设备列表刷新操作的日志。
10. 流程完成：返回刷新操作的执行结果。

#### 刷新设备列表功能点与其他模块及子模块业务关联说明
1. **与设备监控模块的关联**：
   - 刷新操作更新设备的监控状态
   - 设备状态变化影响监控告警
   - 监控数据依赖于设备状态信息

2. **与系统统计模块的关联**：
   - 刷新后的设备信息用于系统统计
   - 设备状态统计影响系统报表
   - 设备数量变化影响系统容量规划

3. **与访客业务模块的关联**：
   - 设备状态影响访客业务的可用性
   - 离线设备无法提供访客服务
   - 设备状态变化需要通知相关业务模块

4. **与用户界面模块的关联**：
   - 刷新结果需要更新界面显示
   - 界面响应速度影响用户体验
   - 刷新状态需要给用户反馈

5. **与缓存管理模块的关联**：
   - 刷新操作需要清理相关缓存
   - 缓存策略影响数据的实时性
   - 缓存失效机制影响刷新频率

￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥￥
